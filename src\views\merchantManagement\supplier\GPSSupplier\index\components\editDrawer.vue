<template>
  <BasicDrawer
    v-bind="$attrs"
    @register="registerDrawer"
    :title="isUpdate ? '编辑供应商' : '新增供应商'"
    :width="adaptiveWidth"
    @ok="handleSubmit"
    @close="handleClose"
    :showFooter="showFooter"
    destroyOnClose
  >
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { useDrawerAdaptiveWidth } from '/@/hooks/jeecg/useAdaptiveWidth';
  import { formSchema } from '../index.data';
  import { saveOrUpdate } from '../index.api';

  // 声明Emits
  const emit = defineEmits(['success', 'register']);
  const isUpdate = ref<boolean>(false);

  //表单配置
  const [registerForm, { resetFields, setFieldsValue, getFieldsValue, validate }] = useForm({
    schemas: formSchema,
    // layout: 'vertical',
    labelWidth: 120,
    showActionButtonGroup: false,
  });

  const showFooter = ref(true);

  //表单赋值
  const [registerDrawer, { setDrawerProps,  closeDrawer }] = useDrawerInner(async data => {
    console.log('是否带过来数据...', data, data.record);
    await resetFields();
    // 无论新增还是编辑，都可以设置表单值
    isUpdate.value = data.isUpdate;
    await setFieldsValue({
      state: 1,
      ...data?.record,
    });
  });

  const { adaptiveWidth } = useDrawerAdaptiveWidth();

  const handleClose = () => {
    isUpdate.value = false;
  };

  //提交事件
  async function handleSubmit() {
    try {
      await validate();
      let values = getFieldsValue();
      const isAdd = !isUpdate.value;
      setDrawerProps({ confirmLoading: true });

      //提交表单
      await saveOrUpdate({ ...values }, !isAdd);
      //关闭弹窗
      closeDrawer();
      //刷新列表
      emit('success', { values });
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
