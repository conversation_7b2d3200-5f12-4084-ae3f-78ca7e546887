/**
 * @Name useWeeksInYearMonth
 * @param date.value 年月格式为'YYYY-MM' 不传则为当月
 * @Description：获取当前月份有几个周
 * <AUTHOR>
 * @Email: <EMAIL>
 * @Date: 2023/9/7 15:58
 * @FilePath: src\hooks\web\useTimePeriod.ts
 */

import { ref, watch } from 'vue';
import dayjs from 'dayjs';
/**
 * @Name useWeeksInYearMonth
 * @param date
 */
export default function useWeeksInYearMonth(date) {
  const weeksNumber = ref<number>(0);
  const currentWeeksNumber = ref<number>(1);

  function getWeeksNumber() {
    // 创建指定年份和月份的Date对象
    const year = dayjs(date.value).year() || new Date().getFullYear();
    const month = dayjs(date.value).month() || new Date().getMonth();
    const dateNew = new Date(year, month);
    // 设置为该月的第一天
    dateNew.setDate(1);

    // 获取该月第一天是星期几（0-6，其中0表示星期日）
    const firstDayOfWeek = dateNew.getDay();

    // 获取该月的天数
    const daysInMonth = new Date(dateNew.getFullYear(), dateNew.getMonth() + 1, 0).getDate();

    // 根据第一天是星期几和该月的天数计算所属的周数
    weeksNumber.value = Math.ceil((daysInMonth + (firstDayOfWeek === 0 ? 7 : firstDayOfWeek)) / 7);
    // console.log(daysInMonth, firstDayOfWeek, weeksNumber.value, date.value);
  }

  function getCurrentWeeksNumber() {
    // 获取本月总天数
    const allDayNum = new Date(date.value).getDate();
    const startWeek = new Date(new Date().getFullYear(), new Date().getMonth(), 1).getDay();
    // 获取第一周天数
    const firstWeekNum = startWeek === 0 ? 1 : 7 - startWeek + 1;
    // 计算剩余天数
    const lastDayNum = allDayNum - firstWeekNum;
    // 计算总周数
    currentWeeksNumber.value = Math.ceil(lastDayNum / 7) + 1;
  }

  watch(
    () => date?.value,
    () => {
      getWeeksNumber();
    }
  );
  getWeeksNumber();
  getCurrentWeeksNumber();
  return {
    weeksNumber,
    currentWeeksNumber,
  };
}
