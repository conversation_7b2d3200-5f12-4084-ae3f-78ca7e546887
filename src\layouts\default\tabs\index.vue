<template>
  <div id="top-page-tabs" :class="getWrapClass">
    <Tabs
      type="editable-card"
      size="small"
      :animated="false"
      :hideAdd="true"
      :tabBarGutter="3"
      :activeKey="activeKeyRef"
      @change="handleChange"
      @edit="handleEdit"
    >
      <template v-for="item in getTabsState" :key="item.query ? item.fullPath : item.path">
        <TabPane :closable="!(item && item.meta && item.meta.affix)">
          <!--<TabPane :closable="true">-->
          <template #tab>
            <TabContent :tabItem="item" />
          </template>
          <template #closeIcon>
            <a-icon type="close-circle-outlined" />
          </template>
        </TabPane>
      </template>

      <template #rightExtra v-if="getShowRedo || getShowQuick">
        <TabRedo v-if="getShowRedo" />
        <TabContent isExtra :tabItem="$route" v-if="getShowQuick" />
        <FoldButton v-if="getShowFold && !isWujieMicro()" />
      </template>
    </Tabs>
  </div>
</template>
<script lang="ts">
  import type { RouteLocationNormalized, RouteMeta } from 'vue-router';

  import { defineComponent, computed, unref, ref } from 'vue';

  import { Tabs } from 'ant-design-vue';
  import TabContent from './components/TabContent.vue';
  import FoldButton from './components/FoldButton.vue';
  import TabRedo from './components/TabRedo.vue';

  import { useGo } from '/@/hooks/web/usePage';

  import { useMultipleTabStore } from '/@/store/modules/multipleTab';
  import { useUserStore } from '/@/store/modules/user';

  import { initAffixTabs, useTabsDrag } from './useMultipleTabs';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { useMultipleTabSetting } from '/@/hooks/setting/useMultipleTabSetting';

  import { REDIRECT_NAME } from '/@/router/constant';
  import { listenerRouteChange } from '/@/logics/mitt/routeChange';

  import { useRouter } from 'vue-router';

  import { isWujieMicro } from '@/utils/wujie';

  export default defineComponent({
    name: 'MultipleTabs',
    components: {
      TabRedo,
      FoldButton,
      Tabs,
      TabPane: Tabs.TabPane,
      TabContent,
    },
    setup() {
      const affixTextList = initAffixTabs();
      const activeKeyRef = ref('');

      useTabsDrag(affixTextList);
      const tabStore = useMultipleTabStore();
      const userStore = useUserStore();
      const router = useRouter();

      const { prefixCls } = useDesign('multiple-tabs');
      const go = useGo();
      const { getShowQuick, getShowRedo, getShowFold, getTabsTheme } = useMultipleTabSetting();

      const getTabsState = computed(() => {
        return tabStore.getTabList.filter((item) => !item.meta?.hideTab);
      });

      const unClose = computed(() => unref(getTabsState).length === 1);

      const getWrapClass = computed(() => {
        return [
          prefixCls,
          {
            [`${prefixCls}--hide-close`]: unref(unClose),
          },
          `${prefixCls}--theme-${unref(getTabsTheme)}`,
        ];
      });

      listenerRouteChange(({route,beforeRoute}) => {
        console.log("🚀 ~ listenerRouteChange ~ route,beforeRoute:", route,beforeRoute)
        const { name } = route;
        if (name === REDIRECT_NAME || !route || !userStore.getToken) {
          return;
        }
        const { path, fullPath, meta = {} } = route;
        const { currentActiveMenu, hideTab } = meta as RouteMeta;
        const isHide = !hideTab ? null : currentActiveMenu;
        const p = isHide || fullPath || path;
        if (activeKeyRef.value !== p) {
          activeKeyRef.value = p as string;
        }
        if (isHide) {
          const findParentRoute = router.getRoutes().find((item) => item.path === currentActiveMenu);

          findParentRoute && tabStore.addTab(findParentRoute as unknown as RouteLocationNormalized);
        } else {
          tabStore.addTab(unref(route), unref(beforeRoute));
        }
      });

      function handleChange(activeKey: any) {
        window.gkLog("activeKey:",activeKey)
        activeKeyRef.value = activeKey;
        go(activeKey, false);
      }

      // Close the current tab
      function handleEdit(targetKey: string) {
        console.log("🚀 ~ handleEdit ~ targetKey:", targetKey)
        // Added operation to hide, currently only use delete operation
        if (unref(unClose)) {
          return;
        }

        tabStore.closeTabByKey(targetKey, router);
      }
      return {
        isWujieMicro,
        prefixCls,
        unClose,
        getWrapClass,
        handleEdit,
        handleChange,
        activeKeyRef,
        getTabsState,
        getShowQuick,
        getShowRedo,
        getShowFold,
      };
    },
  });
</script>
<style lang="less">
  @import './index.less';
  @import './tabs.theme.card.less';
  @import './tabs.theme.smooth.less';
</style>
<style lang="less" scoped>
  #top-page-tabs {
    background: #f0f2f5;
    box-shadow: none;
    border: 0;
    & > :deep(.ant-tabs) {
      //padding: 10px 10px 10px 10px;
      padding: 0 10px;
      background: transparent;
      box-shadow: none;
      border: 0;
    }
    & > :deep(.ant-tabs > .ant-tabs-nav) {
      padding-left: 0;
      background: transparent;
      box-shadow: none;
      border: 0;
    }
    & > :deep(.ant-tabs > .ant-tabs-nav > .ant-tabs-nav-wrap) {
      margin: 0;
      padding: 0;
      border: 0;
      height: 100%;
    }
    & > :deep(.ant-tabs > .ant-tabs-nav > .ant-tabs-extra-content) {
      display: none;
    }
    & > :deep(.ant-tabs > .ant-tabs-nav > .ant-tabs-nav-operations) {
      height: 100%;
      padding: 10px 0;
      display: flex;
      align-items: center;
      margin-left: 10px;
    }
    & > :deep(.ant-tabs > .ant-tabs-nav > .ant-tabs-nav-operations > button) {
      background: #fff;
      padding: 0 2px;
      border-radius: 2px;
      border: 1px solid #ccc;
    }
    & > :deep(.ant-tabs > .ant-tabs-nav > .ant-tabs-nav-wrap > .ant-tabs-nav-list) {
      background: transparent;
      padding-top: 10px;
    }
    & > :deep(.ant-tabs > .ant-tabs-nav > .ant-tabs-nav-wrap > .ant-tabs-nav-list > .ant-tabs-tab) {
      //background: transparent;
      border-radius: 6px;
      border: none;
      height: 35px;
      background: #fff;
      color: #333333;
      padding: 0 20px;
    }
    & > :deep(.ant-tabs > .ant-tabs-nav > .ant-tabs-nav-wrap > .ant-tabs-nav-list > .ant-tabs-ink-bar) {
      display: none;
    }
    & > :deep(.ant-tabs > .ant-tabs-nav > .ant-tabs-nav-wrap > .ant-tabs-nav-list > .ant-tabs-tab.ant-tabs-tab-active) {
      background: #3979f9;
    }

    & > :deep(.ant-tabs > .ant-tabs-nav > .ant-tabs-nav-wrap > .ant-tabs-nav-list > .ant-tabs-tab.ant-tabs-tab-active span) {
      color: #fff;
    }
    & > :deep(.ant-tabs > .ant-tabs-nav > .ant-tabs-nav-wrap > .ant-tabs-nav-list > .ant-tabs-tab span) {
      color: #333333;
      font-weight: 500;
      font-size: 13px;
    }
    & > :deep(.ant-tabs > .ant-tabs-nav > .ant-tabs-nav-wrap > .ant-tabs-nav-list > .ant-tabs-tab .ant-tabs-tab-remove) {
      top: 1px;
      left: 13px;
    }
    //& > :deep(.ant-tabs > .ant-tabs-nav > .ant-tabs-nav-wrap > .ant-tabs-nav-list > .ant-tabs-tab .anticon-close) {
    //  opacity: 1 !important;
    //  width: 18px;
    //  height: 18px;
    //}
    //& > :deep(.ant-tabs > .ant-tabs-nav > .ant-tabs-nav-wrap > .ant-tabs-nav-list > .ant-tabs-tab .anticon-close > svg) {
    //  font-size: 15px;
    //  fill: rgba(0, 0, 0, 0.85) !important;
    //}
    //& > :deep(.ant-tabs > .ant-tabs-nav > .ant-tabs-nav-wrap > .ant-tabs-nav-list > .ant-tabs-tab.ant-tabs-tab-active .anticon-close > svg) {
    //  font-size: 15px;
    //  fill: rgba(255, 255, 255, 0.85) !important;
    //}
  }
</style>
