import { defHttp } from '/@/utils/http/axios';

enum Api {
  cityListAll = '/bo/city/configuration/all',
  list = '/approval/spaceConfig/page',
  edit = '/approval/spaceConfig/update',
  save = '/approval/spaceConfig/add',
  delete = '/approval/spaceConfig/delete',
}

/**
 * 列表接口
 * @param params
 */
export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params });
};

export const deleteSpaceConfig = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.delete, data: params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

export const list = (params) => defHttp.post({ url: Api.list, params });
// 获取所有城市列表
export const cityListAll = () => defHttp.get({ url: Api.cityListAll });
