<template>
  <a-spin :spinning="pageLoading">
    <div class="risk-control-report" :style="{ '--fontSize': tableFontSize }">
      <div class="content-wrap">
        <div class="top-wrap">
          <div class="search-wrap">
            <div class="label">日期：</div>
            <a-date-picker
              class="date-select"
              v-model:value="searchDate"
              format="YYYY/MM/DD"
              placeholder="请选择日期"
              :disabled-date="disabledDate"
              :default-value="dayjs().subtract(1, 'day')"
              :allowClear="false"
            >
              <template #suffixIcon><calendar-outlined :style="{ color: '#00fff9' }" /></template>
            </a-date-picker>
          </div>
          <div class="btn-wrap">
            <a-button class="search_btn" size="large" @click="loadData">查询</a-button>
            <a-button class="reset_btn" size="large" plain @click="handleReset">重置</a-button>
          </div>
        </div>
        <a-card :bordered="false" class="main-card">
          <div class="flex between">
            <div class="title">车辆资产在线率（已交付）</div>
            <a-button class="export_btn" size="large" plain @click="exportExcel">导出</a-button>
          </div>
          <!-- 大区切换Tab -->
          <a-tabs v-model:activeKey="activeRegionId" type="card" class="region-tabs" @change="onRegionChange">
            <a-tab-pane v-for="region in regionList" :key="region.id" :tab="region.name">
              <div class="tab-content">
                <!-- 柱状图区域 -->
                <a-spin :spinning="chartLoading">
                  <div class="chart-section" :loading="chartLoading">
                    <a-card :bordered="false" class="chart-card">
                      <RiskControlChart :data="chartData" height="400px" :index="activeRegionId" />
                    </a-card>
                  </div>
                </a-spin>
              </div>
            </a-tab-pane>
          </a-tabs>
          <!-- 折线图区域 - 移到Tab外面，因为与大区无关 -->
          <a-spin :spinning="lineChartLoading">
            <div class="line-chart-section">
              <a-card :bordered="false" class="line-chart-card">
                <OnlineRateChart :data="lineChartData" height="300px" :loading="lineChartLoading" />
              </a-card>
            </div>
          </a-spin>
          <!-- 表格区域 -->
          <a-spin :spinning="tableLoading">
            <div class="table-section">
              <a-card :bordered="false" class="table-card">
                <div ref="tableContainerRef" :loading="tableLoading">
                  <RiskControlTable
                    ref="tableRef"
                    :data="tableData"
                    :selected-date="dayjs(searchDate).format('YYYY/MM/DD')"
                    :zoomRate="zoomRate"
                    :screenHeight="screenHeight"
                  />
                </div>
              </a-card>
            </div>
          </a-spin>
        </a-card>
      </div>

      <a-modal
        v-model:visible="showModal"
        title="数据导出"
        :wrap-style="{ overflow: 'hidden' }"
        :style="{ top: '30%' }"
        @ok="handleOk"
        @close="handleModalClose"
      >
        <div style="padding: 24px">
          <a-range-picker
            :value="hackValue || value"
            :disabled-date="disabledExportDate"
            @change="onChange"
            @open-change="onOpenChange"
            @calendar-change="onCalendarChange"
          />
        </div>
      </a-modal>
    </div>
  </a-spin>
</template>

<script lang="ts" setup>
  import { ref, onMounted, onUnmounted } from 'vue';
  import { getBarData, getLineData, getTableData, exportUrl } from './index.api';
  import { RegionInfo, RiskControlTableItem, ChannelInfo } from './index.data';
  import RiskControlChart from './RiskControlChart.vue';
  import RiskControlTable from './RiskControlTable.vue';
  import OnlineRateChart from './OnlineRateChart.vue';
  import dayjs, { Dayjs } from 'dayjs';
  import { handleExportXls } from '@/utils/common/renderUtils';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { CalendarOutlined } from '@ant-design/icons-vue';
  type RangeValue = [Dayjs, Dayjs];

  const { createMessage } = useMessage();
  // 响应式数据
  const zoomRate = ref<number>(1);
  const activeRegionId = ref<number>(1);
  const regionList = ref<RegionInfo[]>([]);
  const barData = ref<any[]>([]);
  const chartData = ref<ChannelInfo[]>([]);
  const tableData = ref<RiskControlTableItem[]>([]);
  const lineChartData = ref<any[]>([]);
  const pageLoading = ref<boolean>(false);
  const chartLoading = ref<boolean>(false);
  const tableLoading = ref<boolean>(false);
  const lineChartLoading = ref<boolean>(false);
  const showModal = ref<boolean>(false);
  const dates = ref<RangeValue | undefined>();
  const value = ref<RangeValue>();
  const hackValue = ref<RangeValue>();
  const searchDate = ref<Dayjs | null>(dayjs().subtract(1, 'day'));
  const tableRef = ref<InstanceType<typeof RiskControlTable>>();
  const tableContainerRef = ref<HTMLElement>(); // 表格容器引用
  const tableFontSize = ref<string>('16px');
  const initScreenWidth = ref<number>(window.innerWidth);
  const screenHeight = ref<number>(document.documentElement.clientHeight - 260);
  const handleReset = () => {
    searchDate.value = dayjs().subtract(1, 'day');
    loadData();
    // 如果折线图需要根据日期重新加载，取消下面的注释
    // loadLineChartData();
  };

  // 禁用日期函数
  const disabledDate = (current: any) => {
    // 禁用今天及之后的日期
    const today = dayjs().startOf('day');
    // 禁用数据保存日期之前的日期
    const minDate = dayjs('2025-08-08').startOf('day');
    return current && (current >= today || current < minDate);
  };

  const disabledExportDate = (current: Dayjs) => {
    if (!current) return false;

    const minDate = dayjs('2025-08-08').startOf('day'); // 最早可选日期
    const yesterday = dayjs().subtract(1, 'day').endOf('day'); // 最晚可选日期（昨天）

    // 不允许早于最小日期或晚于昨天
    if (current.isBefore(minDate) || current.isAfter(yesterday)) {
      return true;
    }

    // 控制最大范围为15天
    if (!dates.value || !dates.value.length) {
      return false;
    }

    const [start, end] = dates.value;

    // 如果已有开始日期，检查是否超过15天范围
    if (start && !end) {
      const tooEarly = current.isBefore(start.subtract(15, 'day'));
      const tooLate = current.isAfter(start.add(15, 'day'));
      return tooEarly || tooLate;
    }

    return false;
  };

  const onOpenChange = (open: boolean) => {
    if (open) {
      dates.value = [] as any;
      hackValue.value = [] as any;
    } else {
      hackValue.value = undefined;
    }
  };

  const onChange = (val: RangeValue) => {
    value.value = val;
  };

  const onCalendarChange = (val: RangeValue) => {
    dates.value = val;
  };

  const exportExcel = async () => {
    showModal.value = true;
  };

  const handleOk = async () => {
    if (!dates.value || !dates.value.length) {
      return createMessage.error('请选择导出日期范围');
    }
    try {
      const start = dates.value[0].format('YYYY-MM-DD');
      const end = dates.value[1].format('YYYY-MM-DD');
      const fileName = `渠道资产监管${start
        .split('-')
        .filter((_, i) => i !== 0)
        .join('')}-${end
        .split('-')
        .filter((_, i) => i !== 0)
        .join('')}`;
      pageLoading.value = true;
      await handleExportXls(fileName, exportUrl, { start, end }, true, 'get', false);
      pageLoading.value = false;
      showModal.value = false;
    } catch (error) {
      pageLoading.value = false;
      showModal.value = false;
      console.log('err', error);
    }
  };
  const handleModalClose = () => {};

  // 初始化页面
  onMounted(async () => {
    initHandleData();
    addEventListener('resize', () => initHandleData());
    await Promise.all([loadData(), loadLineChartData()]);
    // 设置表头可见性监听
  });

  // 清理资源
  onUnmounted(() => {
    removeEventListener('resize', () => {
      console.log('remove resize');
    });
  });

  const initHandleData = () => {
    const zoomRate = document.documentElement.clientWidth / initScreenWidth.value;
    screenHeight.value = document.documentElement.clientHeight - 260 / zoomRate;
    if (initScreenWidth.value <= 1920) {
      // tableFontSize.value = '14px';
      tableFontSize.value = 14 * zoomRate + 'px';
    } else if (initScreenWidth.value > 1920) {
      tableFontSize.value = 16 * zoomRate + 'px';
      // tableFontSize.value = '16px';
    }
  };
  /**
   * 加载大区列表
   */
  const loadRegionData = async () => {
    try {
      const barChartData = await getBarData({
        date: searchDate.value ? dayjs(searchDate.value).format('YYYY-MM-DD') : void 0,
      });
      // barData.value = barChartData;
      barData.value = barChartData.sort((a: any, b: any) => {
        const totalA = a.data.reduce((regionSum, company) => {
          const companySum = company.modelList.reduce((companySum, model) => companySum + model.total, 0);
          return regionSum + companySum;
        }, 0);
        const totalB = b.data.reduce((regionSum, company) => {
          const companyTotal = company.modelList.reduce((companySum, model) => {
            return companySum + (model.total || 0);
          }, 0);
          return regionSum + companyTotal;
        }, 0);
        return totalB - totalA;
      });
      regionList.value = barChartData.length
        ? barChartData.map((item: any, index: number) => {
            return {
              id: index + 1,
              name: item.label,
            };
          })
        : [{ id: 1, name: '深圳' }];
      handleChartData(barChartData);
    } catch (error) {
      console.error('❌ 柱状图数据加载失败:', error);
    }
  };
  /**
   * 加载数据
   */
  const loadData = async () => {
    await Promise.all([loadRegionData(), loadTableData()]);
  };

  /**
   * 加载图表数据
   */
  const handleChartData = async (barData: any) => {
    chartLoading.value = true;
    try {
      const { data } = barData[activeRegionId.value - 1] || [];
      let result = data
        ? JSON.parse(JSON.stringify(data))
            .sort((a: any, b: any) => {
              const totalA = a.modelList.reduce((sum: number, model: any) => sum + model.total, 0);
              const totalB = b.modelList.reduce((sum: number, model: any) => sum + model.total, 0);
              return totalA - totalB; // 降序排序
            })
            .reverse()
        : [];
      chartData.value = [...result];
    } catch (error) {
      console.error('加载图表数据失败:', error);
    } finally {
      chartLoading.value = false;
    }
  };

  /**
   * 加载折线图数据
   */
  const loadLineChartData = async () => {
    lineChartLoading.value = true;
    try {
      // 这里可以调用API获取真实的在线率数据
      const lineDaTa = await getLineData();

      // 验证数据格式
      if (Array.isArray(lineDaTa) && lineDaTa.length > 0) {
        lineChartData.value = lineDaTa;
      } else {
        lineChartData.value = [];
      }
    } catch (error) {
      lineChartData.value = [];
    } finally {
      lineChartLoading.value = false;
    }
  };

  /**
   * 加载表格数据
   */
  const loadTableData = async () => {
    if (!activeRegionId.value) {
      return;
    }
    tableLoading.value = true;
    try {
      // 这里替换为您的API调用
      const table = await getTableData({
        date: searchDate.value ? dayjs(searchDate.value).format('YYYY-MM-DD') : void 0,
      });

      tableData.value = table;
    } catch (error) {
      console.error('❌ 表格数据加载失败:', error);
    } finally {
      tableLoading.value = false;
    }
  };

  /**
   * 大区切换事件
   */
  const onRegionChange = async () => {
    await handleChartData(barData.value);
  };
</script>

<style lang="less" scoped>
  .risk-control-report {
    padding: 16px;
    background-color: #001648;
    min-height: calc(100vh - 98px);
    margin-left: -1px;
    .content-wrap {
      background-color: #001648;
      .top-wrap {
        padding: 24px;
        background-color: #133261;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        display: flex;
        align-items: center;
        justify-content: space-between;
        .search-wrap {
          display: flex;
          align-items: center;
          .label {
            color: #00fff9;
            font-size: 16px;
            margin-right: 12px;
          }
          .date-select {
            width: 300px;
            background-color: #133261;
            border: 1px solid #00fff9;
            color: #00fff9;
            :deep(.ant-picker-input) {
              input {
                color: #00fff9;
                &::placeholder {
                  color: #00fff9;
                  opacity: 0.7;
                }
              }
            }
          }
        }
        .btn-wrap {
          :deep(.ant-btn) {
            &:hover {
              background-color: #207bca;
            }
          }
          .search_btn {
            background-color: #005caa;
            border: none;
            outline: unset;
            color: #00fff9;
          }
          .reset_btn {
            background-color: #005caa;
            border: none;
            outline: unset;
            color: #00fff9;
          }
        }
      }
      .main-card {
        margin-top: 10px;
        min-height: calc(100vh - 120px);
        border-radius: 8px;
        background-color: #133261;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        :deep(.ant-card-body) {
          padding: 24px;
          border-radius: 8px;
          background-color: #133261;
        }

        .between {
          display: flex;
          justify-content: space-between;
          :deep(.ant-btn) {
            &:hover {
              background-color: #207bca;
            }
          }
        }
        .export_btn {
          background-color: #005caa;
          border: none;
          outline: unset;
          color: #00fff9;
          :hover {
            background-color: #005caa;
          }
        }
        .title {
          position: relative;
          font-size: 18px;
          font-weight: 600;
          color: #00fff9;
          margin-bottom: 24px;
          &::before {
            content: '';
            position: absolute;
            top: 5px;
            left: -10px;
            width: 4px;
            height: 20px;
            background-color: #00fff9;
          }
        }
        .region-tabs {
          background-color: #001648;
          border-radius: 8px;
          padding: 24px 24px 0px 24px;
          margin-bottom: 24px;
          :deep(.ant-tabs-nav) {
            margin-bottom: 6px;
            &::before {
              border-bottom: none;
            }
            .ant-tabs-nav-wrap {
              .ant-tabs-nav-list {
                .ant-tabs-tab {
                  padding: 12px 24px;
                  font-size: 14px;
                  font-weight: 600;
                  border-top-left-radius: 8px;
                  border-top-right-radius: 8px;
                  border: 1px solid #4e8bcb7a;
                  margin-right: 8px;
                  background-color: #133261;
                  .ant-tabs-tab-btn {
                    color: #fff;
                    opacity: 0.9;
                    font-size: 16px;
                  }
                  &.ant-tabs-tab-active {
                    background-color: #005caa;
                    // background-color: #133261;
                    color: #00fff9;
                    position: relative;
                    .ant-tabs-tab-btn {
                      color: #00fff9;
                    }
                  }

                  &:hover:not(.ant-tabs-tab-active) {
                    background-color: #005caa;
                    .ant-tabs-tab-btn {
                      color: #00fff9 !important;
                    }
                  }
                }
              }
            }
          }

          :deep(.ant-tabs-content-holder) {
            .ant-tabs-content {
              .ant-tabs-tabpane {
                padding: 0;
              }
            }
          }
        }

        .tab-content {
          .chart-section {
            .chart-card {
              border-radius: 8px;

              :deep(.ant-card-head) {
                border-bottom: 1px solid #f0f0f0;
              }

              :deep(.ant-card-body) {
                padding: 20px 0;
                border-radius: 0;
                background-color: #001648;
              }
            }
          }
        }
        .line-chart-section {
          margin-bottom: 24px;
          .line-chart-card {
            border-radius: 8px;

            :deep(.ant-card-body) {
              padding: 20px 0px;
              background-color: #001648;
            }
          }
        }

        .table-section {
          .table-card {
            border-radius: 8px;

            :deep(.ant-card-body) {
              padding: 0px;
              border-radius: 0;
              background-color: #001648;
            }
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .risk-control-report {
      padding: 8px;

      .main-card {
        :deep(.ant-card-body) {
          padding: 16px;
        }

        .region-tabs {
          :deep(.ant-tabs-tab) {
            padding: 8px 16px;
            font-size: 12px;
          }
        }

        .tab-content {
          .chart-section,
          .table-section {
            .chart-card,
            .table-card {
              :deep(.ant-card-body) {
                padding: 12px;
              }
            }
          }
        }
      }
    }
  }
</style>
