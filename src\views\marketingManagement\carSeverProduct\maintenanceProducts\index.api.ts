import { defHttp } from '/@/utils/http/axios';

enum Api {
  list = '/market/attribute/pool/list',
  edit = '/market/attribute/pool/edit',
  save = '/market/attribute/pool/add',
  delete = '/market/attribute/pool/delete',
  getDetailbyId = '/market/attribute/pool/queryById',
  brandList = '/veh/brand/all', // 品牌列表
  carTypeList = '/veh/model/all', // 车型列表
  getDictItems = '/sys/dict/getDictItems/', // 字典列表
}

// 列表接口
export const list = (params: Record<string, any>) => defHttp.get({ url: Api.list, params });

//更新接口
export const saveOrUpdate = (params: Record<string, any>, isUpdate: Boolean) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params });
};

//删除接口
export const deleteItemById = async (params: Record<string, any>, handleSuccess: Fn<any>) => {
  await defHttp.delete({ url: Api.delete, params }, { joinParamsToUrl: true });
  handleSuccess();
};

// 根据id获取详情
export const getDetailbyId = (params: Record<string, any>) => defHttp.get({ url: Api.getDetailbyId, params });

// 品牌列表
export const brandList = (params: Record<string, any>) => defHttp.get({ url: Api.brandList, params });

// 车型列表
export const carTypeList = (params: Record<string, any>) => defHttp.get({ url: Api.carTypeList, params });

// 字典列表
export const getDictItemsApi = (dictCode: string) => defHttp.get({ url: Api.getDictItems + dictCode, params: { dictCode: dictCode } });
