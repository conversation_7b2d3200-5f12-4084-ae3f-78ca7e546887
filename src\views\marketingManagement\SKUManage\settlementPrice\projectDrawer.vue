<template>
  <BasicDrawer
    v-bind="$attrs"
    @register="registerDrawer"
    :title="title"
    :width="adaptiveWidth"
    @ok="handleSubmit"
    :showFooter="showFooter"
    destroyOnClose
  >
    <BasicForm @register="registerForm"> </BasicForm>
    <template v-if="openType == OpenDrawerType.detail && histroyList.length > 0">
      <a-divider>历史价格</a-divider>
      <div style="max-height: 300px;overflow-y: scroll;">
        <a-descriptions v-for="(item,index) in histroyList" title="" :column="2">
          <a-descriptions-item label="操作人/工号"><PERSON></a-descriptions-item>
          <a-descriptions-item label="操作时间">1810000000</a-descriptions-item>
          <a-descriptions-item label="修改前价格" :span="2">Hangzhou, Zhejiang</a-descriptions-item>
          <a-descriptions-item label="修改后价格" :span="2">empty</a-descriptions-item>
          <a-divider v-if="index != histroyList.length -1"></a-divider>
        </a-descriptions>
      </div>
    </template>
  </BasicDrawer>
</template>
<script lang="ts" setup>
  import { ref, computed, unref } from 'vue';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { formSchema, OpenDrawerType } from './index.data';
  import { list } from './index.api';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { saveOrUpdate } from './index.api';
  import { useDrawerAdaptiveWidth } from '/@/hooks/jeecg/useAdaptiveWidth';

  // 声明Emits
  const emit = defineEmits(['success', 'register']);
  const isUpdate = ref(true);
  //表单配置
  const [registerForm, { setProps, resetFields, setFieldsValue, validate }] = useForm({
    schemas: formSchema,
    labelWidth: 200,
    showActionButtonGroup: false,
  });

  const showFooter = ref(true);

  const openType = ref<OpenDrawerType>(OpenDrawerType.detail);
  const histroyList = ref<any>([])
  //表单赋值
  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async data => {
    console.log('是否带过来数据...', data, data.record);
    await resetFields();
    showFooter.value = data?.showFooter ?? true;
    setDrawerProps({ confirmLoading: false, showFooter: showFooter.value });
    isUpdate.value = !!data?.isUpdate;
    // 无论新增还是编辑，都可以设置表单值
    if (typeof data.record === 'object') {
      setFieldsValue({
        ...data.record,
      });
    }
    if (data.openType) {
      openType.value = data.openType;
      setFieldsValue({
        openType: openType.value,
      });
    }
    if(openType.value === OpenDrawerType.detail){
      histroyList.value = [];
      loadHistroy(data.record)
    }
    setProps({ disabled: !showFooter.value });
  });
  // 加载历史价格
  async function loadHistroy(record){
    histroyList.value = await list({id: record.id})
    console.log("🚀 ~ loadHistroy ~ histroyList:", histroyList)
  }
  //获取标题
  const title = computed(() => {
    if (openType.value === OpenDrawerType.detail) {
      return '详情';
    } else {
      return '修改价格';
    }
  });

  const { adaptiveWidth } = useDrawerAdaptiveWidth();

  //提交事件
  async function handleSubmit() {
    try {
      let values = await validate();
      setDrawerProps({ confirmLoading: true });
      let isUpdateVal = unref(isUpdate);
      //提交表单
      await saveOrUpdate({ ...values }, isUpdateVal);
      //关闭弹窗
      closeDrawer();
      //刷新列表
      emit('success', { isUpdateVal, values });
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
<style lang="less">
  .customize-disabled-style {
    .ant-select-tree .ant-select-tree-treenode-disabled .ant-select-tree-node-content-wrapper {
      color: black;
    }
  }
</style>
