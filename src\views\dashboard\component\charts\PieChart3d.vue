<template>
  <div class="chart-container">
    <div class="piechart_3d" ref="chartRef"></div>
    <!-- 底座背景 -->
    <!--    <div class="bg"></div>-->
  </div>
</template>

<script lang="ts" setup>
  import { getPie3D, getParametricEquation } from './chartTool.ts'; //工具类js，页面路径自己修改
  import { ref, onMounted, onBeforeUnmount } from 'vue';
  import echarts from '/@/utils/lib/echarts';

  const color = ['#00FFF9', '#0053FF', '#F9E900'];
  const colorText = ['#00FFF9', '#8CA6FF', '#E0FF6A'];
  const props = defineProps({
    optionData: {
      type: Array<any>,
      default: [],
    },
  });
  const optionData = ref(props.optionData);
  const chartRef = ref(null);
  const chartInstance = ref<any>(null);
  // 配置项
  const option = ref<any>({
    series: [],
  });
  setLabel();

  const handleResize = () => changeSize();
  onMounted(() => {
    initChart();
    // 窗口大小变化监听
    window.addEventListener('resize', handleResize);
  });

  // 组件卸载时移除监听
  onBeforeUnmount(() => {
    window.removeEventListener('resize', handleResize);
    chartInstance.value?.dispose(); // 添加这行
  });

  // 初始化label样式
  function setLabel() {
    optionData.value.forEach((item, index) => {
      item.itemStyle = {
        color: color[index],
      };
      item.label = {
        normal: {
          show: true,
          color: color[index],
          formatter: ['{c|{c}}{c|辆}', '{b|{b}}'].join('\n'), // 用\n来换行
          rich: {
            c: {
              fontSize: 20,
              color: colorText[index],
              align: 'center',
            },
            b: {
              fontSize: 20,
              overflow: 'none', // 关键！禁止缩略
              width: '100%', // 确保宽度足够
              color: colorText[index],
              lineHeight: 20,
              align: 'left',
              padding: [20, 0, 0, 0],
            },
          },
        },
      };
      item.labelLine = {
        normal: {
          show: false,
          lineStyle: {
            width: 1,
            color: 'rgba(255,255,255,0.7)',
          },
        },
      };
    });
  }

  // 图表初始化
  function initChart() {
    if (!chartRef.value) return;

    // 确保只初始化一次
    if (chartInstance.value) {
      chartInstance.value.dispose();
    }
    chartInstance.value = echarts.init(chartRef.value);
    // 传入数据生成 option, 构建3d饼状图, 参数工具文件已经备注的很详细
    option.value = getPie3D(optionData.value, 0.68, 180, 45, 35, 0.11);
    chartInstance.value.setOption(option.value);
    // 是否需要label指引线，如果要就添加一个透明的2d饼状图并调整角度使得labelLine和3d的饼状图对齐，并再次setOption
    option.value.series.push({
      name: '业务类型',
      backgroundColor: 'transparent',
      type: 'pie',
      label: {
        opacity: 1,
        fontSize: 13,
        lineHeight: 10,
      },
      // startAngle: 0,
      startAngle: -30, //起始角度，支持范围[0, 360]。
      clockwise: false, // 饼图的扇区是否是顺时针排布。上述这两项配置主要是为了对齐3d的样式
      radius: ['35%', '35%'],
      center: ['50%', '50%'],
      data: optionData.value,
      itemStyle: {
        opacity: 0, //这里必须是0，不然2d的图会覆盖在表面
      },
    });
    // 延迟设置选项以避免初始化问题
    setTimeout(() => {
      chartInstance.value?.setOption(option.value, true); // 注意第二个参数 true 表示不合并选项
    }, 0);
  }

  // 自适应宽高
  function changeSize() {
    chartInstance.value.resize();
  }
</script>

<style lang="less" scoped>
  .chart-container {
    position: relative;
    width: 200%;
    height: 200%;
    transform: translateX(-25%) translateY(-25%) scale(0.5);

    .piechart_3d,
    .bg {
      width: 200%;
      height: 200%;
      position: absolute;
      top: 0;
      left: 0;
    }
  }
</style>
