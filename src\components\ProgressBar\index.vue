<template>
  <div class="progress-bar">
    <div class="progress-bar-fill" :style="{ width: props.percentage + '%' }"></div>
  </div>
</template>

<script lang="ts" setup>
  import { watch } from 'vue';

  const props = defineProps({
    percentage: {
      type: Number,
      default: 0,
    },
  });
  watch(
    () => props.percentage,
    () => {
      console.log(props.percentage, ' props.percentage');
    }
  );
</script>

<style scoped lang="less">
  .progress-bar {
    display: flex;
    width: 100%;
    align-items: center;
    background-color: #fff;
    border-radius: 5px;
    overflow: hidden;
    height: 40px;
  }

  .progress-bar-fill {
    height: 100%;
    background-color: #007bff;
    transition: width 0.3s ease;
  }
</style>
