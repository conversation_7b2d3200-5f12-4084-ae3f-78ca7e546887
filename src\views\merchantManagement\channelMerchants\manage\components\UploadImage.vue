<template>
  <div class="upload-image__list">
    <div class="upload-image__item" v-for="(item, index) in state.imageUploadArr" :key="index">
      <div class="item__form-item">
        <UploadImageItem v-bind="item" v-model:value="model[item.key]" :bizPath="bizPath" :disabled="disabled" @change="(val) =>handleUploadChange(val, item)" />
        <div v-if="!disabled" class="item__form-item__link-wrap">
          <a-typography-link class="item__form-item__link" :href="exampleUrl(item.key)" target="_blank">
            {{ item?.linkText || '查看示例' }}
          </a-typography-link>
        </div>
      </div>
    </div>
    <div class="upload-image__item more-img-list" v-for="(item, index) in moreUrl" :key="index">
      <div class="item__form-item">
        <ImageItem :src="item" :disabled="disabled" @delete="() => handleDelete(index)" />
        <div v-if="!disabled" class="item__form-item__link-wrap"><a-typography-link class="item__form-item__link"> &nbsp </a-typography-link></div>
      </div>
    </div>
    <div class="upload-image__item" v-if="more && (!disabled || model[moreItem.key])">
      <div class="item__form-item">
        <UploadImageItem v-bind="moreItem" :hiddenUploadIcon="disabled" v-model:value="model[moreItem.key]" :bizPath="bizPath" :disabled="disabled" :showUploadList="false" />
        <div v-if="!disabled" class="item__form-item__link-wrap"><a-typography-link class="item__form-item__link"> &nbsp </a-typography-link></div>
      </div>
    </div>
  </div>
  <a-typography-text v-if="!disabled" disabled v-html="descText"></a-typography-text>
</template>
<script setup lang="ts">
  import { reactive, defineProps, watch, computed, unref, ref } from 'vue';
  import UploadImageItem from './UploadImageItem.vue';
  import { useRuleFormItem } from '@/hooks/component/useFormItem';
  import { isString } from '@/utils/is';
  import ImageItem from './ImageItem.vue';
  // 声明Emits
  const emit = defineEmits(['change', 'update:value']);
  const props = defineProps({
    imageUploads: {
      type: Array,
      default: [],
    },
    descText: {
      type: String,
      default: '',
    },
    more: {
      type: Boolean,
      default: false,
    },
    moreItem: {
      type: Object,
      default: {},
    },
    model: {
      type: Object,
      default: {},
    },
    schema: {
      type: Object,
      default: {},
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    exampleList: {
      type: Array,
      default: [],
    },
    bizPath: {
      type: String,
      required: false,
      default: 'temp',
    },
  });
  const state = reactive({
    imageUploadArr: [],
  });
  //表单值
  const [states] = useRuleFormItem(props);
  const exampleUrl = computed(() => {
    return key => {
      const filterItem = (props.exampleList || []).filter(item => item.fieldName === key)[0];
      return filterItem?.exampleUrl || '';
    };
  });
  const moreUrl = computed(() => {
    let val = props.model[props.moreItem.key];
    if (isString(val)) {
      val = val.split(',');
    }
    return val || [];
  });
  const handleDelete = index => {
    let arr = unref(moreUrl);
    arr.splice(index, 1);
    props.model[props.moreItem.key] = arr?.length ? arr.toString() : null;
  };
  const handleUploadChange = (val, item) => {
    // console.log(e,'121212')
    emit('change', val, item);
  };
  /**
   * 监听value变化
   */
  watch(
    () => props.imageUploads,
    val => {
      //update-begin---author:liusq ---date:20230601  for：【issues/556】JImageUpload组件value赋初始值没显示图片------------
      // if (val && val instanceof Array) {
      //   val = val.join(',');
      // }
      state.imageUploadArr = val;
    },
    { immediate: true }
    //update-end---author:liusq ---date:20230601  for：【issues/556】JImageUpload组件value赋初始值没显示图片------------
  );
</script>
<style scoped lang="less">
  .upload-image {
    &__list {
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      margin: 10px;
    }
    &__item {
      .item__form-item {
        margin: 0;
        &__link-wrap {
          text-align: center;
          margin-bottom: 10px;
        }
        &__link {
          margin-right: 16px;
        }
      }
      &.more-img-list {
        margin: 0 16px 16px 0;
      }
    }
  }
</style>
