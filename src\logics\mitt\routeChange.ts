/**
 * Used to monitor routing changes to change the status of menus and tabs. There is no need to monitor the route, because the route status change is affected by the page rendering time, which will be slow
 */

import mitt from '/@/utils/mitt';
import type { RouteLocationNormalized } from 'vue-router';
import { getRawRoute } from '/@/utils';

const emitter = mitt();

const key = Symbol();

let lastChangeTab: RouteLocationNormalized;
let beforeLastTab: RouteLocationNormalized;

export function setRouteChange(lastChangeRoute: RouteLocationNormalized,from: RouteLocationNormalized) {
  const r = getRawRoute(lastChangeRoute);
  const f = getRawRoute(from);
  emitter.emit(key, {route:r, beforeRoute: f});
  lastChangeTab = r;
  beforeLastTab = f;
}

export function listenerRouteChange(callback: ({route,beforeRoute}:{route: RouteLocationNormalized,beforeRoute: RouteLocationNormalized}) => void, immediate = true) {
  emitter.on(key, callback);
  immediate && lastChangeTab && callback({route:lastChangeTab, beforeRoute: beforeLastTab});
}

export function removeTabChangeListener() {
  emitter.clear();
}
