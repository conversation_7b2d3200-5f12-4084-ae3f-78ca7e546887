<template>
  <div ref="chartRef" :style="{ height, width }" v-loading="loading"></div>
</template>

<script lang="ts" setup>
  import { ref, Ref, reactive, watchEffect, computed, nextTick, PropType } from 'vue';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { ChannelInfo } from './index.data';
  const props = defineProps({
    data: {
      type: Array as PropType<ChannelInfo[]>,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '500px',
    },
    index: {
      type: [String, Number],
      default: 1,
    },
  });
  const status = ref<any[]>([]);
  const originalStatus = [
    {
      name: '正常',
      key: 'normalVehicle',
      color: '#5b8ff9', // 绿色
    },
    {
      name: '异常',
      key: 'abnormalVehicle',
      color: '#5ad8a6',
    },
    {
      name: '正常',
      key: 'normalOperation',
      color: '#5b8ff9', // 绿色
    },
    {
      name: '低效',
      key: 'inefficient',
      color: '#5ad8a6', // 橙色
    },
    {
      name: '久停',
      key: 'longPause',
      color: '#5d7092', // 红色
    },
    {
      name: '离线≥30天',
      key: 'offline30',
      color: '#f6bd16', // 灰色
    },
    {
      name: '30天>离线≥15',
      key: 'offline15_30',
      color: '#6f5ef9', // 灰色
    },
    {
      name: '离线<15',
      key: 'offline15',
      color: '#6dc8ec', // 灰色
    },
    {
      name: '未激活',
      key: 'notActivated',
      color: '#945fe9', // 灰色
    },
  ];
  const chartRef = ref<HTMLDivElement | null>(null);
  const { setOptions, resize } = useECharts(chartRef as Ref<HTMLDivElement>);
  // 计算所有车型（用于图例）
  const allModels = computed(() => {
    const models = new Set<string>();
    props.data.forEach(channel => {
      channel.modelList.forEach(model => {
        models.add(model.model);
      });
    });
    return Array.from(models).sort((a, b) => {
      return a.localeCompare(b);
    });
  });

  // 图表配置
  const option = reactive({
    tooltip: {
      trigger: 'axis' as const,
      axisPointer: {
        type: 'shadow' as const,
      },
      backgroundColor: 'rgba(19, 50, 97, 1)',
      borderColor: '#1a3a6a',
      borderWidth: 1,
      padding: 16,
      textStyle: {
        color: '#00fff9',
      },
      formatter: function (params: any) {
        if (!params || params.length === 0) return '';

        const channelName = params[0].name;
        // 按车型分组数据，过滤掉数量为0的状态
        const modelData: Record<string, any[]> = {};
        params.forEach((param: any) => {
          let model = '';
          if (param.data && param.data.model) {
            model = param.data.model;
          }
          // 只有当数量大于0时才添加到tooltip中
          if (model && param.value > 0) {
            if (!modelData[model]) {
              modelData[model] = [];
            }

            modelData[model].push({
              statusName: param.seriesName,
              marker: param.marker,
              value: param.value,
            });
          }
        });
        const models = Object.keys(modelData);
        // 再次反转状态顺序，以匹配原始状态顺序
        models.forEach(model => {
          const items = modelData[model];
          modelData[model] = status.value.map(status => items.find(i => i.statusName === status.name)).filter(Boolean); // 过滤掉 undefined
        });

        if (models.length === 0) return '';

        // 构建表格样式的HTML
        let html = `<div style="font-size: 14px; font-weight: bold; margin-bottom: 10px;">${channelName}</div>`;

        // 创建表格（无表头）
        html += '<table style="border-collapse: collapse; width: 100%; font-size: 12px;">';

        // 找到最大的状态数量，用于确定行数
        const maxStatusCount = Math.max(...models.map(model => (modelData[model] ? modelData[model].length : 0)));

        // 为每个状态创建一行
        for (let statusIndex = 0; statusIndex <= maxStatusCount - 1; statusIndex++) {
          html += '<tr>';
          models.forEach(model => {
            const statusData = modelData[model] && modelData[model][statusIndex];
            if (statusData) {
              html += `<td style="padding: 2px 8px; text-align: left; min-width: 150px;">${statusData.marker}${model} - ${statusData.statusName}：${statusData.value}辆</td>`;
            } else {
              html += '<td style="padding: 2px 8px; text-align: left; min-width: 120px;"></td>';
            }
          });
          html += '</tr>';
        }

        html += '</table>';
        return html;
      },
    },
    legend: {
      data: [] as string[],
      top: 0,
      left: '0%',
      textStyle: {
        color: '#00fff9',
      },
    },
    grid: {
      left: '2%',
      right: '2%',
      bottom: '3%',
      top: '18%',
      containLabel: true,
    },
    xAxis: {
      type: 'category' as const,
      data: [] as string[],
      axisLabel: {
        interval: 0,
        fontSize: 14,
        // rotate: props.data.length > 10 ? 45 : 0,
        formatter: function (value) {
          // 每行最多6个字符，超过则换行
          const maxLineLength = props.data.length > 9 ? (window.screen.width <= 1920 ? 4 : 6) : 20;
          const lines = Math.ceil(value.length / maxLineLength);
          return new Array(lines)
            .fill('')
            .map((_, i) => value.slice(i * maxLineLength, (i + 1) * maxLineLength))
            .join('\n');
        },
        color: '#00fff9',
      },
      axisTick: {
        show: true,
        lineStyle: {
          color: '#4e8bcb7a', // Y 轴刻度线颜色
          width: 1,
        },
      },
    },
    yAxis: {
      type: 'value' as const,
      name: '车辆数量',
      axisLabel: {
        fontSize: 12,
        color: '#00fff9',
      },
      nameTextStyle: {
        fontSize: 12,
        color: '#00fff9',
      },
      splitLine: {
        lineStyle: {
          color: '#4e8bcb7a', // X 轴刻度参考线颜色
          type: 'solid', // 可选：'solid', 'dashed', 'dotted'
        },
      },
    },
    series: [] as any[],
  });

  // 监听index变化，设置状态
  watchEffect(() => {
    if (props.index == 1) {
      // 总览：只显示前两个状态（正常、异常）
      status.value = originalStatus.slice(0, 2);
    } else {
      // 其他：显示除了"异常"之外的所有状态
      status.value = originalStatus.slice(2, originalStatus.length);
    }

    if (props.data && props.data.length) {
      initCharts();
    } else {
      console.log('看看渲染空了没', props.data);
      initEmptyChart();
    }
  });
  // 初始化空图表
  function initEmptyChart() {
    option.xAxis.data = [];
    option.series = [];
    setOptions(option);
  }
  function initCharts() {
    const channels = props.data.map(item => item.name);
    const maxCount = props.data[0].modelList[0].total;
    // 保持X轴只显示渠道名称
    option.xAxis.data = channels;

    // 构建系列数据
    const series: any = [];
    const legendData: string[] = [];

    // 为每个状态和车型组合创建系列
    const reverseStatus = JSON.parse(JSON.stringify(status.value)).reverse();
    reverseStatus.forEach((statusItem: { name: string; key: string; color: string }) => {
      if (!legendData.includes(statusItem.name)) legendData.push(statusItem.name);

      allModels.value.forEach(model => {
        // 为每个渠道收集该车型该状态的数据
        const data = channels.map(channelName => {
          const channel = props.data.find(c => c.name === channelName);
          if (!channel) return null;

          const modelData = channel.modelList.find(m => m.model === model);
          if (!modelData) return null; // 该渠道没有这个车型，返回0

          // 检查数据键是否存在
          const value = modelData[statusItem.key as keyof typeof modelData];
          return typeof value === 'number'
            ? {
                value,
                model,
              }
            : null;
        });
        // 检查是否有任何渠道拥有这个车型且有数据

        series.push({
          name: statusItem.name,
          type: 'bar' as const,
          stack: `${model}`, // 按车型堆叠
          color: statusItem.color,
          // 动态设置柱子宽度：总宽度固定，根据最大车型数量调整单个柱子宽度
          barGap: '10%', // 柱子间距
          barCategoryGap: '20%', // 分类间距
          label: {
            show: true,
            formatter: ({ value }) => {
              if (maxCount > 0 && maxCount < 30) {
                if (value < 1) return '';
                else return value + '辆';
              }
              if (maxCount > 30 && maxCount < 200) {
                if (value < 4) return '';
                else return value + '辆';
              }
              if (maxCount > 200 && maxCount < 500) {
                if (value < 11) return '';
                else return value + '辆';
              }
              if (maxCount > 500) {
                if (value < 20) return '';
                else return value + '辆';
              }
            },
            fontSize: 10,
            color: '#fff',
          },
          emphasis: { focus: 'series' },
          data,
        });
      });
    });

    // 设置图例数据
    option.legend.data = status.value.map(item => item.name);
    option.series = series;

    setOptions(option);
    nextTick(() => resize());
  }
</script>

<style lang="less" scoped>
  // 图表容器样式
</style>
