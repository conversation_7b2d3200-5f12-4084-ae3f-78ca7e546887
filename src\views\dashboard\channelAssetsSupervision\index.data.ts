import { BasicColumn } from '/@/components/Table';

// 百分比格式化函数
const formatPercentage = (value: any): string => {
  // 如果值为 null、undefined 或空字符串，返回 '-'
  if (value === null || value === undefined || value === '') {
    return '-';
  }

  // 转换为数字
  const numValue = Number(value);

  // 如果不是有效数字，返回 '-'
  if (isNaN(numValue)) {
    return '-';
  }

  // 将小数转换为百分比（乘以100）并保留2位小数
  return (numValue * 100).toFixed(0) + '%';
};
// 类型定义
export interface RegionInfo {
  id: string;
  name: string;
  code: string;
}

export interface ChannelInfo {
  id?: string;
  name: string;
  modelList: VehicleStatisticsItem[];
  T30?: VehicleStatisticsItem;
  V70?: VehicleStatisticsItem;
  V80?: VehicleStatisticsItem;
}

export interface VehicleStatisticsItem {
  model: string;
  normalVehicle: number;
  abnormalVehicle: number;
  normalOperation: number;
  inefficient: number;
  longPause: number;
  offline30: number;
  offline15_30: number;
  offline15: number;
  notActivated: number;
  total: number;
}

export interface RiskControlTableItem {
  cityName: String;
  lesseeName: String;
  model: String;
  offline30: String;
  offline15_30: String;
  offline15: String;
  notActivated: String;
  abnormalTotal: String;
  normal: String;
  inefficient: String;
  longPause: String;
  normalTotal: String;
  total: String;
  thisWeekSOnlineRate: String;
  lastWeekSOnlineRate: String;
  changeInOnlineRate: String;
  thisWeekOperatingRate: String;
  lastWeekOperatingRate: String;
  changeInOperatingRate: String;
  expanded?: boolean;
  children?: Array<{
    vehicleType: string;
    businessSubType: string;
    count: number;
  }>;
}

// 表格列配置函数，接收 expandedRows 和数据源参数
export const getColumns = (expandedRows: any, tableData?: any[], selectedDate?: string): BasicColumn[] => [
  {
    title: `${selectedDate ? selectedDate + '\n' : ''}业务中心`,
    dataIndex: 'cityName',
    key: 'cityName',
    align: 'center',
    customRender: ({ record }: any) => {
      // 展开的子行处理
      if (record.isChild) {
        return '';
      }

      return record.cityName;
    },
    customCell: (record: any) => {
      // 展开的子行处理 - 所有子行都被父行合并
      if (record.isChild) {
        // 正常渠道，只合并大区列
        return {
          rowSpan: 0, // 被父行合并
        };
      }

      // 计算父行的 rowSpan（包括展开的子行）
      let rowSpan = record.regionRowSpan === 0 ? 0 : record.regionRowSpan || 1;
      const colSpan = record.regionColSpan === 0 ? 0 : record.regionColSpan || 1;

      // 使用 expandedRows 判断展开状态
      const isExpanded = expandedRows.has(record.id);

      // 如果该行已展开且有子数据，动态设置 rowSpan
      if (isExpanded && record.children && record.children.length > 0) {
        if (rowSpan > 0) {
          // 如果原来有合并，增加子行数量
          rowSpan += record.children.length;
        }
      }

      // 如果这是合并的主行（rowSpan > 0），检查相关行的展开状态
      if (rowSpan > 0 && tableData) {
        // 对于大区列，需要检查整个大区内所有行的展开状态
        expandedRows.forEach((expandedId: string) => {
          if (expandedId !== record.id) {
            // 在 tableData 中找到对应的行
            const expandedRow = tableData.find(row => row.id === expandedId);
            if (expandedRow) {
              // 普通行：检查同一大区的所有行（不限渠道）
              const isSameRegion = expandedRow.cityName === record.cityName;

              if (isSameRegion) {
                rowSpan += expandedRow.children?.length || 2;
              }
            }
          }
        });
      }

      return {
        rowSpan,
        colSpan,
      };
    },
    width: '6%',
  },
  {
    title: '渠道',
    dataIndex: 'lesseeName',
    key: 'lesseeName',
    align: 'left',
    customRender: ({ record }: any) => {
      // 展开的子行处理
      if (record.isChild) {
        // 正常渠道的子行，被父行合并
        return '';
      }

      return record.lesseeName;
    },
    customCell: (record: any) => {
      // 展开的子行处理 - 所有子行都被父行合并
      if (record.isChild) {
        // 正常渠道的子行，被父行合并
        return {
          rowSpan: 0, // 被父行合并
        };
      }

      // 计算父行的 rowSpan（包括展开的子行）
      let rowSpan = record.channelRowSpan === 0 ? 0 : record.channelRowSpan || 1;
      const colSpan = record.channelColSpan === 0 ? 0 : record.channelColSpan || 1;

      // 使用 expandedRows 判断展开状态
      const isExpanded = expandedRows.has(record.id);

      // 如果该行已展开且有子数据，动态设置 rowSpan
      if (isExpanded && record.children && record.children.length > 0) {
        if (rowSpan > 0) {
          // 如果原来有合并，增加子行数量
          rowSpan += record.children.length;
        }
      }

      // 如果这是合并的主行（rowSpan > 0），检查同组内其他行的展开状态
      if (rowSpan > 0 && tableData) {
        // 查找同组内的其他展开行
        expandedRows.forEach((expandedId: string) => {
          if (expandedId !== record.id) {
            // 在 tableData 中找到对应的行
            const expandedRow = tableData.find(row => row.id === expandedId);
            if (expandedRow) {
              // 普通行：检查同一渠道的行
              const isSameChannel =
                expandedRow.cityName === record.cityName && expandedRow.lesseeName === record.lesseeName;

              if (isSameChannel) {
                rowSpan += expandedRow.children?.length || 2;
              }
            }
          }
        });
      }

      return {
        rowSpan,
        colSpan,
      };
    },
    width: '12%',
  },
  {
    title: '车辆型号',
    dataIndex: 'model',
    key: 'model',
    align: 'left',
    width: '5%',
  },
  {
    // 22%
    title: '异常车辆',
    children: [
      {
        title: '离线≥30天',
        dataIndex: 'offline30',
        key: 'offline30',
        align: 'left',
        width: '5%',
      },
      {
        title: '30>离线≥15',
        dataIndex: 'offline1530',
        key: 'offline1530',
        align: 'left',
        width: '5.5%',
      },
      {
        title: '离线<15',
        dataIndex: 'offline15',
        key: 'offline15',
        align: 'left',
        width: '4%',
      },
      {
        title: '未激活',
        dataIndex: 'notActivated',
        key: 'notActivated',
        align: 'left',
        width: '4%',
      },
      {
        title: '异常合计',
        dataIndex: 'abnormalTotal',
        key: 'abnormalTotal',
        align: 'left',
        width: '4%',
      },
    ],
    width: '22.5%',
  },
  {
    // 20%
    title: '正常车辆',
    children: [
      {
        title: '正常运营',
        dataIndex: 'normal',
        key: 'normal',
        align: 'left',
        width: '4%',
      },
      {
        title: '低效运营',
        dataIndex: 'inefficient',
        key: 'inefficient',
        align: 'left',
        width: '4%',
      },
      {
        title: '久停车辆',
        dataIndex: 'longPause',
        key: 'longPause',
        align: 'left',
        width: '4%',
      },
      {
        title: '正常合计',
        dataIndex: 'normalTotal',
        key: 'normalTotal',
        align: 'left',
        width: '4%',
      },
    ],
    width: '20%',
  },
  {
    title: '总计',
    dataIndex: 'total',
    key: 'total',
    align: 'left',
    width: '4%',
  },
  {
    title: '本周在线率',
    dataIndex: 'thisWeekOnlineRate',
    key: 'thisWeekOnlineRate',
    align: 'left',
    customRender: ({ text }) => formatPercentage(text),
    width: '5%',
  },
  {
    title: '上周在线率',
    dataIndex: 'lastWeekOnlineRate',
    key: 'lastWeekOnlineRate',
    align: 'left',
    customRender: ({ text }) => formatPercentage(text),
    width: '5%',
  },
  {
    title: '在线率变化',
    dataIndex: 'changeInOnlineRate',
    key: 'changeInOnlineRate',
    align: 'left',
    customRender: ({ text }) => formatPercentage(text),
    width: '5%',
  },
  {
    title: '本周运营率',
    dataIndex: 'thisWeekOperatingRate',
    key: 'thisWeekOperatingRate',
    align: 'left',
    customRender: ({ text }) => formatPercentage(text),
    width: '5%',
  },
  {
    title: '上周运营率',
    dataIndex: 'lastWeekOperatingRate',
    key: 'lastWeekOperatingRate',
    align: 'left',
    customRender: ({ text }) => formatPercentage(text),
    width: '5%',
  },
  {
    title: '运营率变化',
    dataIndex: 'changeInOperatingRate',
    key: 'changeInOperatingRate',
    align: 'left',
    customRender: ({ text }) => formatPercentage(text),
    width: '5%',
  },
];
