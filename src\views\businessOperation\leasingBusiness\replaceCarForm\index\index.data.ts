import { getEnterList, getUserAllList } from '/@/api/common/api';
import { BasicColumn, FormSchema } from '/@/components/Table';
import { commonColPropsThree } from '/@/views/businessOperation/leasingBusiness/leaseContract/index/index.data';

export const columns: BasicColumn[] = [
  {
    title: '上游订单编号',
    dataIndex: 'upstreamOrderNo',
    key: 'upstreamOrderNo',
    fixed: 'left',
    width: 150,
  },
  {
    title: '替换车单编号',
    dataIndex: 'replaceOrderNo',
    key: 'replaceOrderNo',
    fixed: 'left',
    width: 150,
  },
  {
    title: '出租方',
    dataIndex: 'lessorName',
    key: 'lessorName',
    width: 200,
  },
  {
    title: '承租方',
    dataIndex: 'lesseeName',
    key: 'lesseeName',
    width: 200,
  },
  {
    title: '替换原车车牌号',
    dataIndex: 'oldVehiclePlate',
    key: 'oldVehiclePlate',
    width: 180,
  },
  {
    title: '替换原车车架号',
    dataIndex: 'oldVin',
    key: 'oldVin',
    width: 180,
  },
  {
    title: '替换车交车状态',
    dataIndex: 'deliveryStatus',
    key: 'deliveryStatus',
    width: 200,
    slots: { customRender: 'deliveryStatus' },
  },
  {
    title: '审核状态',
    dataIndex: 'approvalStatus',
    key: 'approvalStatus',
    width: 150,
    slots: { customRender: 'approvalStatus' },
  },
  {
    title: '替换车车牌号',
    dataIndex: 'vehiclePlate',
    key: 'vehiclePlate',
    width: 180,
    customRender: ({ text }) => text ?? '-',
  },
  {
    title: '替换车车架号',
    dataIndex: 'vin',
    key: 'vin',
    width: 180,
    customRender: ({ text }) => text ?? '-',
  },
  {
    title: '交车场站',
    dataIndex: 'stationName',
    key: 'stationName',
    width: 150,
  },
  {
    title: '交车员工',
    dataIndex: 'deliveryPerson',
    key: 'deliveryPerson',
    customRender: ({ text }) => text ?? '-',
    width: 150,
  },
  {
    title: '业务员',
    dataIndex: 'salesmanName',
    key: 'salesmanName',
    customRender: ({ text }) => text ?? '-',
    width: 150,
  },
  {
    title: '客户提车人',
    dataIndex: 'customerName',
    key: 'customerName',
    width: 150,
  },
  {
    title: '申请类型',
    dataIndex: 'applicationType',
    key: 'applicationType',
    width: 120,
  },
  {
    title: '要求提车时间',
    dataIndex: 'requestPickupDate',
    key: 'requestPickupDate',
    customRender: ({ text }) => (text ? text.slice(0, 10) : '-'),
    width: 120,
  },
  {
    title: '提车时间  ',
    dataIndex: 'pickupDate',
    key: 'pickupDate',
    customRender: ({ text }) => (text ? text.slice(0, 10) : '-'),
    width: 120,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '合同类型',
    field: 'contractType',
    component: 'Input',
    defaultValue: '01',
    show: false,
  },
  {
    label: '替换车单编号',
    field: 'replaceOrderNo',
    component: 'Input',
    colProps: commonColPropsThree,
  },
  {
    label: '承租方',
    field: 'lesseeName',
    component: 'ApiSelect',
    componentProps: {
      api: getEnterList,
      params: { tags: '1,2,7,8' },
      labelField: 'entName',
      valueField: 'entName',
      resultField: 'records',
      placeholder: '请选择承租方',
      showSearch: true,
      immediate: false,
    },
    colProps: commonColPropsThree,
  },
  {
    label: '出租方',
    field: 'lessorName',
    component: 'ApiSelect',
    componentProps: {
      api: getEnterList,
      params: { tags: '4' },
      labelField: 'entName',
      valueField: 'entName',
      resultField: 'records',
      placeholder: '请选择出租方',
      showSearch: true,
      immediate: false,
    },
    colProps: commonColPropsThree,
  },
  {
    label: '车牌号',
    field: 'vehiclePlate',
    component: 'Input',
    colProps: commonColPropsThree,
    rules: [{ min: 4, message: '至少输入4位' }],
  },
  {
    label: '车架号',
    field: 'vin',
    component: 'Input',
    colProps: commonColPropsThree,
    rules: [{ min: 4, max: 20, message: '至少输入4位' }],
  },
  {
    label: '业务员',
    field: 'salesmanName',
    component: 'ApiSelect',
    componentProps: {
      api: getUserAllList,
      labelField: 'realname',
      valueField: 'realname',
      immediate: false,
      placeholder: '请选择业务员',
      showSearch: true,
    },
    colProps: commonColPropsThree,
  },
  {
    field: 'deliveryStatus',
    component: 'Input',
    slot: 'operation',
    label: '交车状态',
    colProps: {
      sm: 24,
      md: 24,
      lg: 24,
      xl: 18,
      xxl: 18,
    },
  },
  {
    field: 'approvalStatus',
    component: 'Input',
    slot: 'approval',
    label: '审批状态',
    colProps: {
      sm: 24,
      md: 24,
      lg: 24,
      xl: 18,
      xxl: 18,
    },
  },
];

// 改派-表单
export const returnFormSchema: FormSchema[] = [
  {
    label: '任务单id',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    label: '场站名称',
    field: 'stationName',
    component: 'Input',
    show: false,
  },
  {
    label: '退车原因',
    field: 'tcReason',
    // slot: 'tcReason',
    component: 'Cascader',
    componentProps: {
      options: [],
      placeholder: '请选择退车原因',
      fieldNames: {
        label: 'reason',
        value: 'reason',
      },
    },
    colProps: {
      span: 24,
    },
    rules: [
      {
        required: true,
        validator: (_rule, value, _callback) => {
          return new Promise((resolve, reject) => {
            if (value?.length) {
              resolve();
            } else {
              reject('请选择退车原因');
            }
          });
        },
      },
    ],
  },
  {
    label: '停放场站',
    field: 'stationId',
    component: 'Select',
    componentProps: {
      showSearch: false,
      options: [],
      placeholder: '请选择场站',
      fieldNames: {
        label: 'stationName',
        value: 'id',
      },
    },
    colProps: {
      span: 24,
    },
    rules: [{ required: true, message: '请选择场站', trigger: 'change' }],
  },
  {
    label: '交车员工',
    field: 'deliveryPerson',
    component: 'ApiSelect',
    colProps: {
      span: 24,
    },
  },
];

// 提车单审批表单
export const DingTalkApprovalFormSchema: FormSchema[] = [
  {
    // 替换车单ID -- 隐藏字段
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    // 替换车单ID -- 隐藏字段
    label: '',
    field: 'assetId',
    component: 'Input',
    show: false,
  },
  {
    label: '印章归属公司',
    field: 'operationOwnership',
    component: 'Input',
    dynamicDisabled: true,
    show: false,
  },
  {
    // 运营公司
    label: '运营公司',
    field: 'lessorName',
    component: 'Input',
    dynamicDisabled: true,
  },
  {
    // 渠道客户名称
    label: '渠道客户名称',
    field: 'lesseeName',
    component: 'Input',
    dynamicDisabled: true,
  },
  {
    // 原车牌号
    label: '原车牌号',
    field: 'oldVehiclePlate',
    component: 'Input',
    dynamicDisabled: true,
  },
  {
    // 原车架号
    label: '原车架号',
    field: 'oldVin',
    component: 'Input',
    dynamicDisabled: true,
  },
  {
    // 原车辆型号
    label: '原车辆型号',
    field: 'oldModel',
    component: 'Input',
    dynamicDisabled: true,
  },
  {
    // 替换车车牌号
    label: '替换车车牌号',
    field: 'vehiclePlate',
    component: 'Input',
    slot: 'vehiclePlate',
  },
  {
    // 替换车车架号
    label: '替换车车架号',
    field: 'vin',
    component: 'Input',
    dynamicDisabled: true,
  },
  {
    // 替换车车辆型号
    label: '替换车车辆型号',
    field: 'model',
    component: 'Input',
    dynamicDisabled: true,
  },
  {
    // 申请人
    label: '申请人',
    field: 'createBy',
    component: 'Input',
    dynamicDisabled: true,
  },
  {
    // 申请时间
    label: '申请时间',
    field: 'applyTime',
    component: 'Input',
    dynamicDisabled: true,
  },
  {
    // 申请类型
    label: '申请类型',
    field: 'applicationType',
    component: 'Input',
    dynamicDisabled: true,
  },
  {
    // 补充附件 -- request
    label: '附件',
    field: 'fileList',
    component: 'JUpload',
    required: false,
    slot: 'supplementary',
  },
  {
    // 订单补充约定 -- request
    label: '申请原因',
    field: 'applicationCause',
    component: 'InputTextArea',
    componentProps: {
      placeholder: ' ',
      autoSize: { minRows: 4 },
    },
    dynamicDisabled: true,
  },
];
