#app {
  width: 100%;
  height: 100%;
}

// =================================
// ==============scrollbar==========
// =================================

::-webkit-scrollbar {
  width: 7px;
  height: 8px;
}

// ::-webkit-scrollbar-track {
//   background: transparent;
// }

::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0.05);
}

::-webkit-scrollbar-thumb {
  // background: rgba(0, 0, 0, 0.6);
  background-color: rgba(144, 147, 153, 0.3);
  // background-color: rgba(144, 147, 153, 0.3);
  border-radius: 2px;
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.2);
}

::-webkit-scrollbar-thumb:hover {
  background-color: @border-color-dark;
}

[data-theme='dark'] {
  ::-webkit-scrollbar-thumb:hover {
    background-color: #5e6063;
  }
}

// =================================
// ==============nprogress==========
// =================================
#nprogress {
  pointer-events: none;

  .bar {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 99999;
    width: 100%;
    height: 2px;
    background-color: @primary-color;
    opacity: 0.75;
  }
}

// =======================================
// ============ [sjl] 按钮组样式 ==========
// =======================================
.j-table-operator {

  // Button按钮间距
  .ant-btn {
    margin: 0 8px 8px 0;
    transition: margin 0s;
  }

  &>.ant-btn:last-of-type {
    margin: 0 0 8px 0;
  }

  .ant-btn-group,
  &.ant-btn-group {
    .ant-btn {
      margin: 0;
      transition: margin 0s;
    }

    &>.ant-btn:last-of-type {
      margin: 0 8px 8px 0;
    }
  }
}

// ========================================
// ============ [sjl] 底部按钮样式 ==========
// ========================================
.j-box-bottom-button {
  height: 28px;

  &-float {
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    border-top: 1px solid #e8e8e8;
    padding: 10px 16px;
    text-align: right;
    background: #fff;
    border-radius: 0 0 2px 2px;

    & .ant-btn {
      margin-left: 8px;
    }
  }

  &.offset-20 &-float {
    left: -20px;
    right: -20px;
    bottom: -20px;
  }
}

// 新增-属性组公用弹框样式
.blue-header-dialog {
  .ant-modal-header {
    background: #3979F9;
    border-bottom: none;

    .jeecg-basic-title {
      color: #ffffff;
    }
  }

  .anticon {
    color: #ffffff;
  }
}

//新增-表单label左对齐（文字左对齐）
.ant-form-item-label {
  label {
    width: 100%;
  }

  &>:not(label.ant-form-item-required) {
    padding-left: 11px;
  }
}

// 新增-气泡型确认弹框-按钮不换行
.ant-popconfirm .ant-popover-buttons {
  white-space: nowrap;
}

// 表格背景铺满页面
.jeecg-layout-content.full {

  &>div,
  .ant-spin-nested-loading,
  .ant-spin-container {
    height: 100%;
    display: flex;
    flex-direction: column;

    .jeecg-basic-table {
      min-height: 0;
      flex: 1;
      display: flex;
      flex-direction: column;
    }

    .ant-table-wrapper {
      flex: 1;
      min-height: 0;
      display: flex;
      flex-direction: column;

      .ant-spin-nested-loading,
      .ant-spin-container {
        flex: 1;

        .ant-table {
          flex: 1;
        }
      }
    }
  }
}

// 新增-textarea文字限制显示在框内
.ant-input-textarea-show-count::after {
  position: relative;
  top: -25px;
  right: 5px;
  z-index: 99;
}

// 新增-去掉表格左右的阴影
.ant-table .ant-table-container::before,
.ant-table .ant-table-container::after {
  display: none;
}

// 新增-link类型的按钮,去掉左右内边距
.c-btn-link {
  padding-left: 0 !important;
  padding-right: 0 !important;
}