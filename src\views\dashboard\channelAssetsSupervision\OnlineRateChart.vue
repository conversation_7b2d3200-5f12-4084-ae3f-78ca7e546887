<template>
  <div ref="chartRef" :style="{ height, width }" v-loading="loading"></div>
</template>

<script lang="ts" setup>
  import { ref, Ref, reactive, watchEffect, PropType } from 'vue';
  import { useECharts } from '/@/hooks/web/useECharts';

  const props = defineProps({
    data: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '400px',
    },
  });

  const chartRef = ref<HTMLDivElement | null>(null);
  const { setOptions } = useECharts(chartRef as Ref<HTMLDivElement>);

  // 图表配置
  const option = reactive({
    tooltip: {
      trigger: 'axis' as const,
      axisPointer: {
        type: 'cross' as const,
        label: {
          backgroundColor: '#6a7985',
        },
      },
      backgroundColor: 'rgba(19, 50, 97, 1)',
      borderColor: '#1a3a6a',
      borderWidth: 1,
      padding: 16,
      textStyle: {
        color: '#00fff9',
      },
      formatter: function (params: any) {
        if (!params || params.length === 0) return '';

        const date = params[0].name;
        let html = `<div style="font-size: 14px; font-weight: bold; margin-bottom: 8px;">${date}</div>`;

        params.forEach((param: any) => {
          html += `<div style="margin-bottom: 4px; font-size: 12px;">${param.marker}${param.seriesName}：${param.value}</div>`;
        });

        return html;
      },
    },
    legend: {
      data: [] as string[],
      top: 0,
      left: 24,
      textStyle: {
        color: '#00fff9',
      },
    },
    grid: {
      left: '3%',
      right: '3%',
      bottom: '15%',
      top: '20%',
      containLabel: true,
    },
    xAxis: {
      type: 'category' as const,
      boundaryGap: false,
      data: [] as string[],
      axisLabel: {
        fontSize: 12,
        // rotate: 45,
        color: '#00fff9',
      },
      axisTick: {
        show: false,
        lineStyle: {
          color: '#4e8bcb7a', // Y 轴刻度线颜色
          width: 1,
        },
      },
    },
    yAxis: {
      type: 'value' as const,
      name: '在线率(%)',
      nameTextStyle: {
        fontSize: 12,
        color: '#00fff9',
      },
      min: 80,
      max: 100,
      axisLabel: {
        formatter: '{value}',
        color: '#00fff9',
      },
    },
    dataZoom: [
      {
        type: 'slider' as const,
        show: true,
        xAxisIndex: [0],
        start: 0,
        end: 100,
        bottom: '5%',
        height: 20,
        handleIcon:
          'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4v1.3h1.3v-1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7V23.1h6.6V24.4z M13.3,19.6H6.7v-1.4h6.6V19.6z',
        handleSize: '80%',
        handleStyle: {
          color: '#fff',
          shadowBlur: 3,
          shadowColor: 'rgba(0, 0, 0, 0.6)',
          shadowOffsetX: 2,
          shadowOffsetY: 2,
        },
        textStyle: {
          color: '#00fff9',  // 修改显示文字颜色
        },
      },
    ],
    series: [] as any[],
  });

  watchEffect(() => {
    if (props.data && props.data.length > 0) {
      console.log('折线图数据加载完成，开始初始化图表');
      initCharts();
    } else {
      console.log('折线图数据为空，初始化空图表');
      initEmptyChart();
    }
  });

  function initCharts() {
    // 处理API返回的数据
    const processedData = processApiData(props.data);

    // 设置X轴数据（日期）
    option.xAxis.data = processedData.dates;

    // 构建系列数据
    const series: any[] = [];

    processedData.legendData.forEach(name => {
      const seriesData = processedData.seriesData[name] || [];

      series.push({
        name: name,
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 10,
        lineStyle: {
          width: 4,
        },
        label: {
          color: '#fff',
        },
        data: seriesData,
      });
    });
    series[0].markLine = {
      symbol: 'none',
      tooltip: {
        show: false,
      },
      emphasis: {
        disabled: true, // 禁用高亮样式
      },
      lineStyle: {
        type: 'dashed',
        color: 'gray',
      },
      label: {
        show: true,
        distance: 8,
        position: 'start',
        formatter: '92',
        color: '#fff',
      },
      data: [
        {
          yAxis: 92,
        },
      ],
    };
    option.series = series;
    option.legend.data = processedData.legendData;
    // 设置滑块默认显示最近一个月，但支持展开到三个月
    // const totalDays = processedData.dates.length;
    // const oneMonthDays = Math.min(5, totalDays);
    // const visiblePercent = (oneMonthDays / totalDays) * 100;

    option.dataZoom[0].start = 0;
    option.dataZoom[0].end = 100;

    setOptions(option);
  }

  // 初始化空图表
  function initEmptyChart() {
    option.xAxis.data = [];
    option.series = [];
    setOptions(option);
  }

  // 处理API返回的数据
  function processApiData(apiData: any[]) {
    // 按日期排序
    const sortedData = [...apiData].sort((a, b) => new Date(a.day).getTime() - new Date(b.day).getTime());
    // 提取日期数组
    const dates = sortedData.map(item => {
      const date = new Date(item.day);
      return `${date.getMonth() + 1}月${date.getDate()}日`;
    });

    // 构建系列数据映射
    const seriesData: Record<string, number[]> = {};
    const legendData: string[] = sortedData[0].dataList.map((item: { name: string; onlineRate: number }) => item.name);

    // 填充数据
    sortedData.forEach(dayData => {
      dayData.dataList.forEach((item: any) => {
        if (!seriesData[item.name]) {
          seriesData[item.name] = [];
        }
        if (seriesData[item.name]) {
          // 将小数转换为百分比，保留2位小数
          const percentage = Math.round(item.onlineRate * 10000) / 100;
          seriesData[item.name].push(percentage);
        } else {
          console.warn(`未找到匹配的系列: ${item.name}`);
        }
      });
    });

    return {
      dates,
      seriesData,
      legendData,
    };
  }
</script>

<style lang="less" scoped>
  // 图表容器样式
</style>
