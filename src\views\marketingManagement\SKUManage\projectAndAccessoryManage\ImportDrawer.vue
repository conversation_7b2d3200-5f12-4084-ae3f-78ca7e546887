<template>
  <BasicDrawer
    v-bind="$attrs"
    @register="registerDrawer"
    :title="title"
    :width="adaptiveWidth"
    @ok="handleSubmit"
    :showFooter="showFooter"
    @close="handleClose"
    destroyOnClose
  >
    <div class="upload-box">
      <JUpload v-model:value="fileCache" :maxCount="1" :returnUrl="false" :beforeUpload="handleBeforeUpload" />

      <div class="upload-box__text">
        1、只允许上传后缀为xls,xlsx的文件<br />
        2、单个文件大小大不超20M<br />
        3、最多上传1个文件（10000条以内）<br />
      </div>
      <div v-if="messageContent">
        <div style="white-space: pre-wrap; color: red">{{ messageContent }}</div>
      </div>
      <div class="upload-box__model">
        <div class="upload-box__model-download" @click="handleDownload">导入模板下载</div>
      </div>
    </div>
  </BasicDrawer>
</template>
<script lang="ts" setup>
  import { ref, computed, unref, defineProps } from 'vue';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { checkImportExcel } from './index.api';
  import { useDrawerAdaptiveWidth } from '/@/hooks/jeecg/useAdaptiveWidth';
  import { useMessage } from '/@/hooks/web/useMessage';
  import JUpload from '/@/components/Form/src/jeecg/components/JUpload/JUpload.vue';
  import { getExcelModel } from '@/api/common/api';

  const { createMessage } = useMessage();
  // 声明Emits
  const emit = defineEmits(['success', 'register']);
  const props = defineProps({
    handleImport: {
      type: Function,
      default() {
        return () => {};
      },
    },
    skuType: Number, // 	类型（1：项目；2：配件）
  });
  const isUpdate = ref(true);
  const flag = ref(false);
  const fileValue = ref<any>(null);
  const fileCache = ref<any>(null);
  //表单配置

  const showFooter = ref(true);
  const messageContent = ref('');

  //表单赋值
  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async data => {
    console.log('是否带过来数据...', data, data.record);
    showFooter.value = data?.showFooter ?? true;
    setDrawerProps({ confirmLoading: false, showFooter: showFooter.value });
    isUpdate.value = !!data?.isUpdate;
  });
  //获取标题

  const title = computed(() => {
    return '导入';
  });

  const { adaptiveWidth } = useDrawerAdaptiveWidth();

  async function handleDownload() {
    if (unref(flag)) {
      return;
    }
    flag.value = true;
    getExcelModel({
      templateCode: props.skuType == 1 ? 'sku_project' : 'sku_accessory',
    })
      .then(res => {
        window.open(res.data.templateExcelUrl);
      })
      .finally(() => {
        flag.value = false;
      });
  }

  //提交事件
  async function handleSubmit() {
    if (!fileCache.value) {
      createMessage.error('提交失败,请先上传文件后进行提交！');
      return;
    }

    if (fileCache.value && messageContent.value) {
      createMessage.error('文件校验失败，请修改后上传！');
      return;
    }
    try {
      await props.handleImport({
        file: fileValue.value,
        skuType: props.skuType,
      });
      setDrawerProps({ confirmLoading: true });
      let isUpdateVal = unref(isUpdate);
      //关闭弹窗
      closeDrawer();
      //刷新列表
      emit('success', { isUpdateVal });
    } finally {
      fileCache.value = null;
      fileValue.value = null;
      messageContent.value = '';
      setDrawerProps({ confirmLoading: false });
    }
  }

  function handleBeforeUpload(file) {
    if (!file) return;
    fileValue.value = file;
    messageContent.value = '';
    checkImportExcel(file, props.skuType, data => {});
  }

  function handleClose() {
    messageContent.value = '';
    fileCache.value = null;
    fileValue.value = null;
  }
</script>

<style lang="less" scoped>
  .upload-box {
    padding: 10px 20px;
    box-sizing: border-box;

    &__text {
      margin-top: 20px;
    }

    &__model {
      margin-top: 30px;

      &-download {
        cursor: pointer;
        width: fit-content;
        text-decoration: underline;
        color: blue;
      }
    }
  }
</style>
