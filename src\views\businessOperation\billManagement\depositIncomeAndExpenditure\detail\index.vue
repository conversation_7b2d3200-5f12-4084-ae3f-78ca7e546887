<template>
  <a-spin :spinning="loading" wrapperClassName="detail-spinning">
    <div style="margin: -2px 10px; background-color: #fff; padding: 20px 0 0 20px">
      <a-descriptions title="基本信息" :labelStyle="{ width: '120px' }">
        <a-descriptions-item v-for="item in baseInfo" :span="item.span" :label="item.label">
          <div v-if="item.field === 'approvalStatus'">
            <div v-if="item.value !== null">
              <StateBox
                :text="approvalStatusObj[item.value].text"
                :stateBC="approvalStatusObj[item.value].stateBC"
              ></StateBox>
            </div>
          </div>
          <div v-else-if="item.field === 'orderNo'" style="color: #3a78f9">
            {{ item.value }}
          </div>
          <div v-else>
            {{ item.value }}
          </div>
        </a-descriptions-item>
      </a-descriptions>
    </div>

    <div class="common-detail-container">
      <div class="detail-box m-t-10px flex-1">
        <div class="box-title">信息明细</div>
        <div class="p-15px">
          <a-tabs v-model:activeKey="activeKey" tabPosition="left">
            <a-tab-pane key="carPickupDemand" tab="附件内容">
              <BasicTable @register="registerTable">
                <template #img="{ text }">
                  <TableImg v-if="text" :size="60" :imgList="[text]" />
                </template>
                <template #action="{ record }">
                  <TableAction :actions="getTableAction(record)" />
                </template>
              </BasicTable>
            </a-tab-pane>
            <a-tab-pane
              v-if="detailRef?.approvalStatus != null && detailRef?.approvalStatus != -1 && detailRef?.approvalStatus != 3"
              key="approval"
              tab="审核记录"
            >
              <ApprovalInfo ref="approvalRef" :infoData="infoData" :loading="loadingApproval" />
            </a-tab-pane>
          </a-tabs>
        </div>
      </div>
    </div>
  </a-spin>
</template>

<script lang="ts" setup name="vehicleManagement-vehicleAssets-detail">
  // #region
  import ApprovalInfo from '@/components/DTApprovalInfo/ApprovalInfo.vue';
  import { depositDetailsGetApproval } from '@/api/common/api';
  import StateBox from '@/components/common/StateBox.vue';
  import { approvalStatusObj } from '@/enums/contract.enum';
  // #endregion
  import { BasicTable, BasicColumn, TableAction, ActionItem, useTable, TableImg } from '/@/components/Table';
  import { onMounted, ref } from 'vue';
  import { getDetail } from '../index.api';
  import { useRoute } from 'vue-router';
  const { query } = useRoute();
  const queryId = query.id || '';

  const detailRef = ref({
    approvalStatus: null,
    attachments: null,
  });
  const loading = ref(false);
  const baseInfo = ref([
    { field: 'orderNo', value: '', span: 3, label: '押金收支编号' },
    { field: 'approvalStatus', value: null, span: 3, label: '审核状态' },
    { field: 'lessorName', value: '', span: 1, label: '出租方' },
    { field: 'item_dictText', value: '', span: 1, label: '事项' },
    { field: 'remark', value: '', span: 1, label: '备注' },
    { field: 'enterpriseName', value: '', span: 1, label: '承租方' },
    { field: 'amount', value: '', span: 1, label: '金额' },
    { field: 'createTime', value: '', span: 1, label: '创建时间' },
    { field: 'type_dictText', value: '', span: 1, label: '类型' },
    { field: 'salesmanName', value: '', span: 1, label: '业务员' },
  ]);

  onMounted(() => {
    getDetaiInfo();
  });

  function getDetaiInfo() {
    if (!queryId) return;
    loading.value = true;
    getDetail({ id: queryId })
      .then(res => {
        if (!res) return;
        detailRef.value = res;
        baseInfo.value.forEach(item => {
          item.value = res[item.field];
        });
        if (res.approvalStatus != null && res.approvalStatus != -1 && res.approvalStatus != 3) {
          getApprovalInfo();
        }
        reload();
      })
      .finally(() => {
        loading.value = false;
      });
  }

  const columns: BasicColumn[] = [
    {
      title: '附件内容',
      dataIndex: 'filePath',
      align: 'center',
      slots: { customRender: 'img' },
    },
    {
      title: '操作',
      dataIndex: 'filePath',
      align: 'center',
      width: 100,
      slots: { customRender: 'action' },
    },
  ];
  const [registerTable, { reload }] = useTable({
    title: '',
    api: async () => {
      return detailRef.value?.attachments ? JSON.parse(detailRef.value?.attachments) : [];
    },
    actionColumn: {
      width: 120,
      fixed: 'right',
    },
    columns: columns,
  });

  /**
   * 操作栏
   */
  function getTableAction(record): ActionItem[] {
    return [
      {
        label: '下载',
        onClick: handleDownload.bind(null, record),
      },
    ];
  }
  function handleDownload(record) {
    window.open(record.filePath);
  }

  const activeKey = ref();

  // #region 钉钉审核 -- 详情展示
  const infoData = ref<any>({});
  // 请求审批详情
  async function getApprovalInfo() {
    loadingApproval.value = true;
    const result = await depositDetailsGetApproval({
      businessId: queryId,
    });
    infoData.value = result;
    loadingApproval.value = false;
  }
  const loadingApproval = ref<boolean>(false);
  // #endregion
</script>
<style lang="less" scoped>
  .item {
    width: 450px;
  }
  .detail-info {
    padding: 10px;
    overflow: hidden;
    box-sizing: border-box;
    outline: none;

    &__top {
      padding: 14px 10px 15px 30px;
      margin-bottom: 8px;
      background-color: #fff;
      border-radius: 6px;
      box-sizing: border-box;

      &-item {
        display: flex;
        justify-content: flex-start;
        align-items: center;
      }

      &-carPlate {
        font-size: 18px;
        font-weight: 700;
        color: black;
        margin-bottom: 14px;
      }

      &-carInfo {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        font-size: 15px;
        margin-bottom: 5px;
      }
    }

    &__bottom {
      padding: 25px;
      background-color: #fff;
      border-radius: 6px;
      box-sizing: border-box;
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;

      &-left {
        display: flex;
        flex-direction: column;
        // margin-right: 10px;

        &__menuItem {
          cursor: pointer;
          position: relative;
          display: flex;
          justify-content: center;
          align-items: center;
          height: 30px;
          width: 100px;

          &.active {
            &::after {
              content: '';
              position: absolute;
              right: 0;
              top: 0;
              width: 3px;
              height: 100%;
              background-color: rgba(107, 217, 169, 1);
            }
          }
        }
      }

      &-right {
        width: 80%;
        min-height: 400px;
        max-height: 500px;
        overflow: hidden auto;

        &__wrapper {
          margin-bottom: 20px;

          &-title {
            display: flex;
            justify-content: flex-start;
            align-items: flex-end;
            height: 20px;
            margin-top: 8px;
            font-weight: 800;
            margin-left: 20px;
            font-size: 15px;
          }

          &-line {
            width: 100%;
            height: 1px;
            background-color: rgba(182, 182, 182, 0.6);
          }

          &-content {
            display: flex;
            flex-wrap: wrap;
            box-sizing: border-box;
            padding-left: 20px;
            margin-top: 15px;

            &__item {
              width: 50%;
              // float: left;
              margin-bottom: 10px;

              &-title {
                display: inline-block;
                width: 100px;
              }
              &-value {
                display: inline-block;
                width: fit-content;
              }
            }
          }
        }
      }
    }
  }

  .line {
    height: 16px;
    border-right: 1px solid black;
    margin: 0 12px;
  }

  .common-label {
    font-size: 14px;
    // margin-right: 60px;
  }
  .status-wrapper {
    display: inline-flex;
    justify-content: flex-start;
    align-items: center;
    position: relative;
    width: 300px;
    max-width: 500px;
  }
  .status-dot {
    position: absolute;
    left: -14px;
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: red;

    &.red {
      background-color: rgba(227, 96, 80, 1);
    }

    &.green {
      background-color: rgba(107, 217, 169, 1);
    }

    &.blue {
      background-color: rgba(74, 120, 241, 1);
    }
  }

  @import url('@/views/businessOperation/leasingBusiness/pickupTaskForm/common/styles.less');
</style>