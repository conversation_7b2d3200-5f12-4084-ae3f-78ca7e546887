<template>
  <div>
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <template #action="{ record }">
        <TableAction v-if="!STATIC_DATA_NAME.includes(record.attributeName)" :actions="getTableAction(record)" />
      </template>
    </BasicTable>
  </div>
</template>
<script lang="ts" setup name="marketingManagement-attributeManagement-pool">
  import { ExclamationCircleFilled } from '@ant-design/icons-vue';
  import { Modal } from 'ant-design-vue';
  import { createVNode } from 'vue';
  import { deleteItemById, getDetailbyId, list } from './index.api';
  import { columns, searchFormSchema, STATIC_DATA } from './index.data';
  import { ActionItem, BasicTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';

  declare type Recordable<T = any> = Record<string, T>;
  const STATIC_DATA_NAME: string[] = STATIC_DATA.map((item: any) => item.attributeName);
  const { tableContext } = useListPage({
    tableProps: {
      title: '属性池',
      showIndexColumn: true,
      api: list,
      rowKey: 'id',
      rowSelection: { type: 'checkbox' },
      columns: columns,
      size: 'small',
      formConfig: {
        schemas: searchFormSchema,
      },
      actionColumn: {
        width: 200,
        fixed: 'right',
      },
      afterFetch: async (res: any) => {
        //将数据中的number类型转换为string类型
        res.forEach((item: any) => {
          for (const key in item) {
            if (Object.prototype.hasOwnProperty.call(item, key)) {
              if (typeof item[key] === 'number') {
                item[key] = item[key].toString();
              }
            }
          }
        });
        // const _data = await getStaticDataValue(res);
        // return [..._data];
        return res;
      },
    },
  });
  //注册table数据
  const [registerTable, { reload }, { rowSelection }] = tableContext;

  /**
   *@description 编辑事件
   */
  async function handleEdit(record: Recordable) {
    const { isCanEdit, attributeGroupStr, productStr, attributeValues } = await hasEditPermission(record.id);
    if (!isCanEdit) {
      Modal.success({
        title: '该属性已关联属性组/车服产品，不允许编辑',
        icon: createVNode(ExclamationCircleFilled, { style: { color: '#1890ff' } }),
        content: createVNode(
          'div',
          {
            style: {
              margin: '10px 0',
            },
          },
          [
            createVNode('p', null, attributeGroupStr ? '属性组：' + attributeGroupStr : ''),
            createVNode(
              'p',
              {
                style: {
                  wordBreak: 'break-all',
                },
              },
              productStr && productStr != 'null' ? '车服产品：' + productStr : ''
            ),
          ]
        ),
      });
      return;
    }
    openDrawer(true, {
      record: { ...record, attributeValues },
      isUpdate: true,
      showFooter: true,
      tenantSaas: false,
    });
  }

  /**
   *@description 删除事件
   */
  async function handleDelete(record: Recordable) {
    const { isCanEdit, attributeGroupStr, productStr } = await hasEditPermission(record.id);
    if (!isCanEdit) {
      Modal.success({
        title: '该属性已关联属性组/车服产品，不允许删除',
        icon: createVNode(ExclamationCircleFilled, { style: { color: '#1890ff' } }),
        content: createVNode(
          'div',
          {
            style: {
              margin: '10px 0',
            },
          },
          [createVNode('p', null, attributeGroupStr ? '属性组：' + attributeGroupStr : ''),
          createVNode('p', null, productStr && productStr != 'null' ? '车服产品：' + productStr : '')]
        ),
      });
      return;
    }

    Modal.confirm({
      title: '确认删除这条信息吗？',
      icon: createVNode(ExclamationCircleFilled, { style: { color: '#1890ff' } }),
      onOk() {
        deleteItemById({ id: record.id }, reload);
      },
    });
  }

  /**
   *@description 校验是否可以编辑
   */
  async function hasEditPermission(id: number) {
    try {
      const res = await getDetailbyId({ id });
      const { attributeGroupStr, productStr, attributeValues } = res;
      return {
        isCanEdit: !attributeGroupStr && !productStr,
        attributeValues,
        attributeGroupStr,
        productStr,
      };
    } catch (e) {
      return {
        isCanEdit: false,
        attributeGroupStr: '',
        productStr: '',
      };
    }
  }

  /**
   *@description 表格操作
   */
  function getTableAction(record): ActionItem[] {
    return [
      {
        auth: 'market:pool:edit',
        label: '编辑',
        onClick: handleEdit.bind(null, record),
      },
      {
        auth: 'market:pool:delete',
        label: '删除',
        onClick: handleDelete.bind(null, record),
      },
    ];
  }

</script>
<style lang="less" scoped></style>
