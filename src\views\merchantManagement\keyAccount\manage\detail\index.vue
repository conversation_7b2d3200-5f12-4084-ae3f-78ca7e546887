<template>
  <div class="detail">
    <div class="basic-info">
      <div class="detail-top-info">
        <div class="business-name">
          <div class="name"> {{ detail.businessName }}</div>
          <div class="state-box">
            <div class="state-box__wrap">
              <span :class="['state-dot', `status-${detail.reviewStatus}`]"></span>
              <span class="state-text">{{ getTextByStatus(detail.reviewStatus) }}</span>
            </div>
          </div>
        </div>
        <a-button class="edit-btn" type="primary" @click="handleEdit">编辑</a-button>
      </div>
      <div class="flex flex-wrap info-row-horizontal-three">
        <div class="flex common-info-row w-33">
          <div class="common-info-label w60">行业：</div>
          <div class="common-info-text" :title="detail.industry">{{ detail.industry || '-' }}</div>
        </div>
        <div class="flex common-info-row w-33">
          <div class="common-info-label">统一社会信用代码：</div>
          <div class="common-info-text">{{ detail.socialCreditCode || '-' }}</div>
        </div>
        <div class="flex common-info-row w-33">
          <div class="common-info-label">营业执照：</div>
          <div class="common-info-text license-img">
            <a-image v-if="detail.license" width="160px" height="60px" :src="detail.license" />
            <span v-else>-</span>
          </div>
        </div>
        <div class="flex common-info-row w-33">
          <div class="common-info-label w60">地址：</div>
          <div class="common-info-text" :title="detail.address">{{ detail.address || '-' }}</div>
        </div>
        <div class="flex common-info-row w-33">
          <div class="common-info-label">创建时间：</div>
          <div class="common-info-text">{{ detail?.createTime || '-' }}</div>
        </div>
      </div>
    </div>
    <div class="bottom-wrap">
      <div class="info-title">详情信息</div>
      <div class="info-content">
        <a-tabs v-model="activeKey" tabPosition="left">
          <a-tab-pane key="subEnter" tab="子企业信息">
            <BasicTable @register="registerTable" />
          </a-tab-pane>
          <a-tab-pane key="audit" tab="审核意见">
            <a-table :data-source="detail.logList" :columns="auditColumns" :pagination="false" />
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
    <EditDrawer @register="registerDrawer" @success="handleSuccess" @cancel="handleSuccess" />
  </div>
</template>
<script setup lang="ts">
  import { columns, searchFormSchema } from './index.data';
  import { queryByIdApi, getSubEnterList } from '../index.api';
  import { auditColumns } from '../index.data';
  import { BasicTable } from '@/components/Table';
  import { useListPage } from '@/hooks/system/useListPage';
  import { useDrawer } from '@/components/Drawer';
  import { useRoute } from 'vue-router';
  import { onMounted, ref } from 'vue';
  import { initDictOptions } from '/@/utils/dict/JDictSelectUtil';
  import EditDrawer from '../components/EditDrawer.vue';
  const route = useRoute();
  const activeKey = ref<string>('subEnter');
  const statusList = ref<any[]>([]);
  const detail = ref<any>({});
  const logList = ref<any[]>([]);

  initDictOptions('key_account_audit_status').then(res => {
    statusList.value = res || [];
  });

  const getTextByStatus = (status: number) => {
    return statusList.value.find(item => +item.value === +status)?.text;
  };
  const [registerDrawer, { openDrawer }] = useDrawer();
  const handleEdit = () => {
    openDrawer(true, {
      record: { ...detail.value, reviewStatus: String(detail.value.reviewStatus) },
      isUpdate: true,
      showFooter: true,
      labelKey: 'edit',
    });
  };

  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    tableProps: {
      api: getSubEnterList,
      rowKey: 'id',
      columns: columns,
      showIndexColumn: true,
      size: 'small',
      showActionColumn: false,
      formConfig: {
        schemas: searchFormSchema,
        labelWidth: 96,
      },
      beforeFetch: params => {
        params.id = route.query.id;
        delete params.order;
        delete params.column;
        return params;
      },
    },
  });
  //注册table数据
  const [registerTable, { reload }] = tableContext;

  /**
   * 成功回调
   */
  const handleSuccess = async () => {
    await initData();
    await reload();
  };

  const initData = async () => {
    const id = route.query.id as string;
    await queryByIdApi({ id }).then(res => {
      detail.value = res;
      logList.value = res.logList;
    });
  };
  onMounted(initData);
</script>
<style scoped lang="less">
  .detail {
    .basic-info {
      margin: 10px;
      padding: 24px 10px;
      background-color: #fff;
      border-radius: 8px;
      .detail-top-info {
        position: relative;
        width: 100%;
        text-align: left;
        display: flex;
        align-items: center;
        .business-name {
          display: flex;
          align-items: center;
          margin-left: 12px;
          .name {
            font-size: 28px;
            margin-right: 12px;
            position: relative;
            &::before {
              content: '';
              position: absolute;
              left: -12px;
              top: 50%;
              transform: translateY(-50%);
              width: 4px;
              height: 20px;
              background-color: #1890ff;
            }
          }
        }
        .edit-btn {
          position: absolute;
          right: 24px;
        }
      }
      .flex {
        display: flex;
        // align-items: center;
      }
      .common-title {
        display: inline-block;
        font-size: 16px;
        font-weight: 600;
        padding: 10px 0;
      }

      .common-info-label {
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 15px;
        color: #333;
        line-height: 21px;
        width: 150px;
      }
      .common-info-text {
        font-family: PingFangSC-Regular;
        font-weight: 400;
        font-size: 15px;
        color: #333;
        line-height: 21px;
        max-width: calc(100% * 0.8);
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
      }
      .info-row-horizontal-three {
        padding: 16px 12px 0 12px;
        .common-info-row:nth-child(n + 4) {
          margin-top: 16px;
        }
      }
      .common-info-row {
        position: relative;
      }
      .w-33 {
        width: calc(100% / 3);
      }
      .ml0 {
        margin-left: 0 !important;
      }

      .mt16 {
        margin-top: 16px;
      }

      .wunset {
        width: unset !important;
      }
      .w90 {
        width: 90px;
      }
      .w60 {
        width: 60px;
      }
    }
    .bottom-wrap {
      margin: 0 10px 10px 10px;
      padding: 12px 12px;
      background-color: #fff;
      border-radius: 8px;
      height: 100%;
      .info-title {
        font-size: 16px;
        font-weight: 600;
        padding: 10px 12px 24px 12px;
        border-bottom: 1px solid #e9e9e9;
      }
      .info-content {
        padding: 16px 0 0 0;
        .ant-table-thead > tr > th {
          padding: 16px 16px 16px 16px;
        }
        .ant-table-tbody > tr > td {
          padding: 16px 16px 16px 16px;
        }
      }
    }
  }
  :deep(.ant-tabs) {
    min-height: 580px;

    .ant-tabs-tab-btn {
      font-weight: 400;
      font-size: 17px;
      color: #333;
    }

    .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
      color: #3979f9;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 50%;
        transform: translateX(-50%);
        width: 35px;
        height: 1px;
        background-color: #3979f9;
      }
    }

    .ant-tabs-tabpane {
      padding-left: 45px;
    }

    .ant-tabs-content {
      padding-top: 14px;
    }

    .ant-tabs-nav-list {
      padding-top: 22px;
    }
  }
  .license-img {
    position: absolute;
    left: 90px;
    height: 120px;
  }
  .state-box {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    text-align: left;

    &__wrap {
      display: flex;
      align-items: center;
      // width: 30%;
      // min-width: 59.5px;
    }
    .state-dot {
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      // margin-right: 24px;
      &.status-0 {
        background-color: #bdbdbd;
      }
      &.status-1 {
        background-color: #ffe53a;
      }
      &.status-2 {
        background-color: #2cdc9b;
      }
      &.status-3 {
        background-color: #f55448;
      }
    }
    .state-text {
      margin-left: 12px;
      min-width: 100px;
    }
  }
</style>
