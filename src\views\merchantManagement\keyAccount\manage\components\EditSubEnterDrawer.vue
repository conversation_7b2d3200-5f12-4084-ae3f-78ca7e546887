<template>
  <BasicDrawer
    v-bind="$attrs"
    @register="registerDrawer"
    title="编辑"
    :width="adaptiveWidth"
    @ok="handleSubmit"
    :showFooter="true"
    @close="handleClose"
    destroyOnClose
  >
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  import { editSubEnterSchema } from '../index.data';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { useDrawerAdaptiveWidth } from '/@/hooks/jeecg/useAdaptiveWidth';
  const emit = defineEmits(['success', 'register']);
  const index = ref<number>();
  //表单配置
  const [registerForm, { resetFields, validate, setFieldsValue, getFieldsValue }] = useForm({
    schemas: editSubEnterSchema,
    labelWidth: 120,
    showActionButtonGroup: false,
  });

  //表单赋值
  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async data => {
    const { record, index: idx } = data;
    index.value = idx;
    await resetFields();
    setFieldsValue({ ...record });
  });

  const { adaptiveWidth } = useDrawerAdaptiveWidth();

  const handleClose = () => {
    index.value = 0;
    resetFields;
  };

  //提交事件
  async function handleSubmit() {
    try {
      await validate();
      let values = getFieldsValue();
      setDrawerProps({ confirmLoading: true });
      //关闭弹窗
      closeDrawer();
      //刷新列表
      emit('success', { data: values, index: index.value });
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
