import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';

export const columns: BasicColumn[] = [
  {
    title: '任务日期',
    dataIndex: 'taskDate',
    key: 'taskDate',
    fixed: 'left',
  },
  {
    title: '同步开始时间',
    dataIndex: 'syncStartTime',
    key: 'syncStartTime',
  },
  {
    title: '同步结束时间',
    dataIndex: 'syncEndTime',
    key: 'syncEndTime',
  },
  {
    title: '同步状态',
    dataIndex: 'status',
    key: 'status',
    // 同步状态（0：同步中；1：同步完成；2：同步失败）
    slots: { customRender: 'synchronizationStatus' },
  },
  {
    title: '新增违章数',
    dataIndex: 'addNum',
    key: 'addNum',
    customRender:({text})=>{
      return text || 0
    }
  },
  {
    title: '更新违章数',
    dataIndex: 'updateNum',
    key: 'updateNum',
    customRender:({text})=>{
      return text || 0
    }
  },
  {
    title: '数据源',
    dataIndex: 'source',
    key: 'source',
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '任务日期',
    labelWidth: 90,
    field: 'syncTime',
    component: 'RangePicker',
    componentProps: {
      //日期格式化
      format: 'YYYY-MM-DD',
      valueFormat: 'YYYY-MM-DD',
      //范围文本描述用集合
      placeholder: ['请选择日期范围', ''],
      style: { width: '100%' },
    },
    colProps: { span: 8 },
  },
];
