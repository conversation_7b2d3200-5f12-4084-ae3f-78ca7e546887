<script setup lang="ts">
  const { stateBC, text } = defineProps({
    stateBC: {
      type: String,
      default: '',
    },
    text: {
      type: String,
      default: '',
    },
  });
</script>

<template>
  <div class="state-box">
    <div class="state-box__wrap"
      ><span class="state-dot" :style="{ backgroundColor: stateBC }"></span> <span>{{ text }}</span></div
    >
  </div>
</template>

<style scoped lang="less">
  .state-box {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    text-align: left;
    width: 100%;

    &__wrap {
      display: flex;
      align-items: center;
      width: 80%;
      min-width: 100px;
    }

    .state-dot {
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 15px;
    }
  }
</style>
