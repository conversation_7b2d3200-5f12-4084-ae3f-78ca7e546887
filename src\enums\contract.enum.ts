/**
 * 合同类型枚举（带扩展方法）
 */
export enum EContractType {
  LEASE_CONTRACT = '01', // 租赁合同
  LEASE_PURCHASE_CONTRACT = '02', // 以租代售合同
  SHOW_CAR_CONTRACT = '03', // 展示车合同
  NON_STANDARD_CONTRACT = '04', // 非标合同
  LEASE_ORDER = '05', // 租赁订单
  LEASE_PURCHASE_ORDER = '06', // 以租代售订单
}

// 为枚举扩展方法
export namespace EContractType {
  /**
   * 获取枚举的显示文本
   */
  export function getLabel(type: EContractType): string {
    const map = {
      [EContractType.LEASE_CONTRACT]: '租赁合同',
      [EContractType.LEASE_PURCHASE_CONTRACT]: '以租代售合同',
      [EContractType.SHOW_CAR_CONTRACT]: '展示车合同',
      [EContractType.NON_STANDARD_CONTRACT]: '非标合同',
      [EContractType.LEASE_ORDER]: '租赁订单',
      [EContractType.LEASE_PURCHASE_ORDER]: '以租代售订单',
    };
    return map[type] || '未知类型';
  }

  /**
   * 检查是否是有效类型
   */
  export function isValid(type: string): boolean {
    return Object.values(EContractType).includes(type as EContractType);
  }
}

export enum ECTemplateType {
  STANDARD_CONTRACT = 1, // 标准合同
  NON_STANDARD_CONTRACT = 2, // 非标合同
}

/**
 * 审批类型枚举
 */
export enum ApprovalTemplateType {
  CONTRACT = 1, // 合同
  NON_STANDARD_CONTRACT = 2, // 非标合同
  ORDER = 3, // 订单
  NON_STANDARD_ORDERS = 4, // 非标订单
  VEHICLE_PICKUP_APPLICATION_FORM = 5, // 提车申请单
  RETURN_APPLICATION_FORM = 6, // 退车申请单
  DEPOSIT_INCOME_AND_EXPENDITURE = 7, // 押金收支
  REMOTE_CONTROL_OF_VEHICLES = 8, // 车辆远程控制
}
// 审批类型枚举Options
export const ApprovalTemplateTypeOptions = [
  { label: '合同', value: ApprovalTemplateType.CONTRACT },
  { label: '非标合同', value: ApprovalTemplateType.NON_STANDARD_CONTRACT },
  { label: '订单', value: ApprovalTemplateType.ORDER },
  { label: '非标订单', value: ApprovalTemplateType.NON_STANDARD_ORDERS },
  { label: '提车申请单', value: ApprovalTemplateType.VEHICLE_PICKUP_APPLICATION_FORM },
  { label: '退车申请单', value: ApprovalTemplateType.RETURN_APPLICATION_FORM },
  { label: '押金收支', value: ApprovalTemplateType.DEPOSIT_INCOME_AND_EXPENDITURE },
  { label: '车辆远程控制', value: ApprovalTemplateType.REMOTE_CONTROL_OF_VEHICLES },
];
// 合同业务类型Options
export const ContractBusinessType = [
  { label: '租赁合同', value: EContractType.LEASE_CONTRACT },
  { label: '以租代售合同', value: EContractType.LEASE_PURCHASE_CONTRACT },
  { label: '展示车合同', value: EContractType.SHOW_CAR_CONTRACT },
];
// 订单业务类型Options
export const OrderBusinessType = [
  { label: '租赁订单', value: EContractType.LEASE_CONTRACT },
  { label: '以租代售订单', value: EContractType.LEASE_PURCHASE_CONTRACT },
  { label: '展示车订单', value: EContractType.SHOW_CAR_CONTRACT },
];
export const approvalStatusObj = {
  '-1': {
    text: '待发起审批',
    stateBC: '#e0e0e0',
  },
  '0': {
    text: '待审批',
    stateBC: '#3979f9',
  },
  '1': {
    text: '审批通过',
    stateBC: '#26f338',
  },
  '2': {
    text: '审批不通过',
    stateBC: '#fd4545',
  },
  '3': {
    text: '撤销审批',
    stateBC: '#e0e0e0',
  },
};
export const approvalStatusOptions = [
  { label: '待发起审批', value: -1 },
  { label: '待审批', value: 0 },
  { label: '审批通过', value: 1 },
  { label: '审批不通过', value: 2 },
  { label: '撤销审批', value: 3 },
];

export enum ORDER_STATUS_CONST {
  CONFIRMED_1 = '1', // '已确认',
  TOBE_CONFIRMED_2 = '2', // '待确认',
  USED_SEAL_3 = '3', // '已用印',
  ARCHIVED_4 = '4', // '已归档'
  IN_REVIEW_5 = '5', // '审核中' #3979f9
  REVIEW_FAILED_6 = '6', // '审核未通过' #fe4242
}