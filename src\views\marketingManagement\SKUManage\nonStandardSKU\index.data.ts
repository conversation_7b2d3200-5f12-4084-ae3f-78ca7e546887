import { BasicColumn, FormSchema } from '/@/components/Table';

export const columns: BasicColumn[] = [
  {
    title: '一级类目',
    dataIndex: 'field1',
    key: 'field1',
    fixed: 'left',
  },
  {
    title: '二级类目',
    dataIndex: 'field2',
    key: 'field2',
  },
  {
    title: '类目SPU',
    dataIndex: 'spuNO',
    key: 'spuNO',
  },
  {
    title: '项目名称',
    dataIndex: 'projectName',
    key: 'projectName',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    slots: { customRender: 'recordStatus' },
  },
  {
    title: '工时定额',
    dataIndex: 'gongshidinge',
    key: 'gongshidinge',
  },
  {
    title: 'SKU编码',
    dataIndex: 'SKUbianma',
    key: 'SKUbianma',
    slots: { customRender: 'SKUbianma' },
  },
  {
    title: '适用车型',
    dataIndex: 'shiyongchexing',
    key: 'shiyongchexing',
  },
  {
    title: '规格',
    dataIndex: 'guige',
    key: 'guige',
  },
  {
    title: '结算价',
    dataIndex: 'jiesuanjia',
    key: 'jiesuanjia',
  },
  {
    title: '更新人',
    dataIndex: 'updateBy',
    key: 'updateBy',
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
  },
];

export enum OnShelves {
  yes = '1',
  no = '2',
}
export enum TypeEnum {
  ACCESSORY = '2',
  PROJECT = '2',
}
export const searchFormSchema: FormSchema[] = [
  {
    label: '服务商名称',
    field: 'spuNO',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: 'SKU名称',
    field: 'projectName',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: 'SKU编码',
    field: 'skuCode',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '状态',
    field: 'typexxxxxxx2',
    component: 'Select',
    componentProps: {
      options: [
        { label: '已上架', value: OnShelves.yes },
        { label: '已下架', value: OnShelves.no },
      ],
    },
    colProps: { span: 6 },
  },
    {
      label: '类型',
      field: 'typexxxxxxx',
      component: 'Select',
      componentProps: {
        options: [
          { label: '配件', value: TypeEnum.ACCESSORY },
          { label: '项目', value: TypeEnum.PROJECT },
        ],
      },
      colProps: { span: 6 },
    },
];

export enum OpenDrawerType {
  add = 'add',
  edit = 'edit',
  detail = 'detail',
}
export enum CATEGORY_TYPE {
  LEVEL_1 = '1',
  LEVEL_2 = '2',
}
export const formSchema: FormSchema[] = [
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    label: '',
    field: 'openType',
    component: 'Input',
    show: false,
  },
  {
    label: '类目名称',
    field: 'leimuName',
    component: 'Input',
    labelWidth: 80,
    colProps: { span: 22 },
    slot: 'treeSelectLevel2',
    rules: [{ required: true, message: '请选择类目' }],
    dynamicDisabled: ({ values }) => values.openType == OpenDrawerType.detail,
  },
  {
    label: '类目SPU',
    field: 'leimuSPU',
    component: 'Input',
    labelWidth: 80,
    colProps: { span: 22 },
    componentProps: {
      maxlength: 30,
    },
    dynamicDisabled: () => true,
  },
  {
    label: '项目名称',
    field: 'projectName',
    component: 'Input',
    labelWidth: 80,
    colProps: { span: 22 },
    componentProps: {
      maxlength: 50,
    },
    rules: [{ required: true, message: '请输入项目名称' }],
    dynamicDisabled: ({ values }) => values.openType == OpenDrawerType.detail,
  },
  {
    label: 'SKU编号',
    field: 'skubianhao',
    component: 'Input',
    labelWidth: 80,
    colProps: { span: 22 },
    componentProps: {
      maxlength: 20,
    },
    dynamicDisabled: ({ values }) => values.id || values.openType == OpenDrawerType.detail,
    rules: [
      { required: true, message: '请输入SKU编号' },
      {
        pattern: /^[a-zA-Z0-9]+$/,
        message: '类目SPU仅支持数字和字母',
      },
    ],
  },
  {
    label: '工时定额',
    field: 'gongshidinge',
    component: 'Input',
    labelWidth: 80,
    colProps: { span: 22 },
    rules: [
      { required: true, message: '请输入工时定额' },
      {
        validator: (_, value, callback) => {
          if (!/^(0|[1-9]\d*)(\.\d)?$/.test(value)) {
            callback('请输入数字，最多保留一位小数');
          } else {
            const num = parseFloat(value);
            if (num < 0 || num > 1000) {
              callback('工时定额范围应为0-1000');
            } else {
              callback();
            }
          }
        },
      },
    ],
    dynamicDisabled: ({ values }) => values.openType == OpenDrawerType.detail,
  },
  {
    label: '结算价',
    field: 'jiesuanjia',
    component: 'Input',
    labelWidth: 80,
    colProps: { span: 22 },
    componentProps: {
      maxlength: 50,
    },
    rules: [
      { required: true, message: '请输入结算价' },
      {
        validator: (_, value, callback) => {
          if (!/^(0|[1-9]\d*)(\.\d{1,2})?$/.test(value)) {
            callback('请输入数字，最多保留两位小数');
          } else {
            const num = parseFloat(value);
            if (num < 0 || num > 9999) {
              callback('结算价范围应为0-9999');
            } else {
              callback();
            }
          }
        },
      },
    ],
    dynamicDisabled: ({ values }) => values.openType == OpenDrawerType.detail,
  },
  {
    label: '适用车型',
    field: 'shiyongchexing',
    component: 'Input',
    labelWidth: 80,
    colProps: { span: 22 },
    slot: 'applications',
    dynamicDisabled: ({ values }) => values.openType == OpenDrawerType.detail,
  },
  {
    label: '规格',
    field: 'guige',
    component: 'InputTextArea',
    labelWidth: 80,
    colProps: { span: 22 },
    componentProps: {
      maxlength: 200,
      rows: 4,
    },
    required: false,
    dynamicDisabled: ({ values }) => values.openType == OpenDrawerType.detail,
  },
];
