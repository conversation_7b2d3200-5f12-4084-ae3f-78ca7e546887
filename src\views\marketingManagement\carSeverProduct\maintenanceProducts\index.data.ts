
import { BasicColumn, FormSchema } from '/@/components/Table';
import { JVxeTypes } from '/@/components/jeecg/JVxeTable/types';
import { FormTypeEnum, InputTypeEnum, IsMustEnum } from '/@/enums/attrManagementEnum';


interface IPoolData {
  id: string; // 主键id
  updateBy: string | null; //更新人
  updateTime: string | null; //更新时间
  createBy: string; //创建人,
  createTime: string; //创建时间,
  company: string; //单位
  attributeName: string; //属性名称,
  formType: number | string; //表单方式
  inputType: number | string; //输入方式
  attributeValue: null | string; //属性值,
  isMust: number | string; //是否必填,
  attributeRemark: null | string; //备注,
  attributeValuesStr: null | string; //属性值,
  attributeValues: null | string;
  attributeGroupStr: null | string; //属性组,
  realname: string; //真实姓名
  workNo: string; //工号,
}

export const columns: BasicColumn[] = [
  {
    title: '属性名称',
    dataIndex: 'attributeName',
    key: 'attributeName',
    fixed: 'left',
  },
  {
    title: '单位',
    dataIndex: 'company',
    key: 'company',
  },
  {
    title: '表单方式',
    dataIndex: 'formType',
    key: 'formType',
    format: (_text, record) => {
      return record?.formType === FormTypeEnum.select ? '下拉框' : '输入框';
    },
  },
  {
    title: '输入方式',
    dataIndex: 'inputType',
    key: 'inputType',
    format: (_text, record) => {
      if (String(record?.inputType) === InputTypeEnum.single) {
        return '单选';
      }
      if (String(record?.inputType) === InputTypeEnum.multiple) {
        return '多选';
      }
      return '--';
    },
  },
  {
    title: '是否必填',
    dataIndex: 'isMust',
    key: 'isMust',
    format: (_text, record) => {
      return record?.isMust === IsMustEnum.yes ? '是' : '否';
    },
  },
  {
    title: '属性值',
    dataIndex: 'attributeValuesStr',
    key: 'attributeValuesStr',
    slots: { customRender: 'attributeValue' },
    format: (_text, record) => {
      return record?.attributeValuesStr ?? '--';
    },
    ellipsis: true,
  },
  {
    title: '备注',
    dataIndex: 'attributeRemark',
    key: 'attributeRemark',
  },
  {
    title: '创建人',
    dataIndex: 'createBy',
    key: 'createBy',
    format: (_text: string, record: Recordable): string | number => {
      return record.realname + '/' + record.workNo;
    },
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
  },

  {
    title: '更新时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '属性名称',
    field: 'attributeName',
    component: 'Input',
    colProps: { span: 8 },
    componentProps: {
      autocomplete: 'off',
    },
    rules: [
      {
        min: 2,
        message: '产品名称模糊搜索至少输入两位',
      },
    ],
  },
];

export const formSchema: FormSchema[] = [
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    label: '表单方式',
    field: 'formType',
    component: 'JSelectInput',
    labelWidth: 80,
    colProps: { span: 24 },

    componentProps: {
      placeholder: '请选择表单方式',
      autocomplete: "off",
      options: [
        { label: '下拉框', value: FormTypeEnum.select },
        { label: '输入框', value: FormTypeEnum.input },
      ],
    },

    rules: [{ required: true, message: '请选择表单方式' }],
  },
  {
    label: '输入方式',
    field: 'inputType',
    component: 'JSelectInput',
    labelWidth: 80,
    colProps: { span: 24 },
    componentProps: {
      placeholder: '请选择输入方式',
      options: [
        { label: '单选', value: InputTypeEnum.single },
        { label: '多选', value: InputTypeEnum.multiple },
      ],
    },
    defaultValue: InputTypeEnum.single,
    rules: [{ required: true, message: '请选择输入方式' }],
    ifShow: ({ values }) => {
      return values.formType === FormTypeEnum.select;
    },
  },
  {
    label: '是否必填',
    field: 'isMust',
    component: 'RadioGroup',
    labelWidth: 80,
    colProps: { span: 24 },
    componentProps: {
      placeholder: '请选择是否必填',
      type: 'radio',
      options: [
        { label: '是', value: IsMustEnum.yes },
        { label: '否', value: IsMustEnum.no },
      ],
      defaultValue: IsMustEnum.yes,
    },
    defaultValue: IsMustEnum.yes,
    rules: [{ required: true, message: '请选择是否必填' }],
  },
  {
    label: '单位',
    field: 'company',
    component: 'Input',
    labelWidth: 80,
    colProps: { span: 24 },
    componentProps: {
      maxlength: 10,
      autocomplete: 'off',
    },
  },
  {
    label: '备注',
    field: 'attributeRemark',
    component: 'InputTextArea',
    labelWidth: 80,
    colProps: { span: 24 },
    componentProps: {
      autocomplete: "off",
      allowClear: false,
      autosize: { minRows: 2, maxRows: 6 },
      maxlength: 100,
    },
  },
];

export const attributeValueColumns: any[] = [
  {
    title: '属性值',
    key: 'attributeValue',
    type: JVxeTypes.input,
    width: 120,
    props: {
      autocomplete: "off",
      autoFoucs: true,
    },
    placeholder: '请填写属性值',
    validateRules: [
      {
        required: true,
        message: '请填写属性值',
      },
      {
        handler({ cellValue, $table, row }, callback: Fn<any>) {
          const { fullData } = $table.getTableData();
          const isHas: Boolean = fullData.some((item: any) => item.attributeValue && item.id !== row.id && item.attributeValue === cellValue);
          if (fullData.length > 0 && isHas) {
            callback(false, '${title}不能重复');
          } else {
            callback(true);
          }
          if (cellValue.length > 100) {
            callback(false, '${title}不能超过100个字符');
          } else {
            callback(true);
          }
        },
      },
    ],
  },
  {
    title: '操作',
    key: 'action',
    // 固定在右侧
    fixed: 'right',
    // 对齐方式为居中
    align: 'center',
    // 组件类型定义为【插槽】
    type: JVxeTypes.slot,
    // slot 的名称，对应 v-slot 冒号后面和等号前面的内容
    slotName: 'action',
  },
];
export const STATIC_DATA: IPoolData[] = [
  {
    id: '1',
    updateBy: null,
    updateTime: '',
    createBy: '--',
    createTime: '',
    company: '',
    attributeName: '品牌',
    formType: FormTypeEnum.select,
    inputType: InputTypeEnum.single,
    attributeValue: null,
    isMust: IsMustEnum.yes,
    attributeRemark: '',
    attributeValuesStr: '',
    attributeValues: null,
    attributeGroupStr: null,
    realname: '--',
    workNo: '--',
  },
  {
    id: '2',
    updateBy: null,
    updateTime: '',
    createBy: '--',
    createTime: '',
    company: '',
    attributeName: '型号',
    formType: FormTypeEnum.select,
    inputType: InputTypeEnum.single,
    attributeValue: null,
    isMust: IsMustEnum.yes,
    attributeRemark: '',
    attributeValuesStr: '',
    attributeValues: null,
    attributeGroupStr: null,
    realname: '--',
    workNo: '--',
  },
  {
    id: '3',
    updateBy: null,
    updateTime: '',
    createBy: '--',
    createTime: '',
    company: '',
    attributeName: '车身颜色',
    formType: FormTypeEnum.select,
    inputType: InputTypeEnum.single,
    attributeValue: null,
    isMust: IsMustEnum.yes,
    attributeRemark: '',
    attributeValuesStr: '',
    attributeValues: null,
    attributeGroupStr: null,
    realname: '--',
    workNo: '--',
  },
  {
    id: '4',
    updateBy: null,
    updateTime: '',
    createBy: '--',
    createTime: '',
    company: '',
    attributeName: '货箱类型',
    formType: FormTypeEnum.select,
    inputType: InputTypeEnum.single,
    attributeValue: null,
    isMust: IsMustEnum.yes,
    attributeRemark: '',
    attributeValuesStr: '',
    attributeValues: null,
    attributeGroupStr: null,
    realname: '--',
    workNo: '--',
  },
];
