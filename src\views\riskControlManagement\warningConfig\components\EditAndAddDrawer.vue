<template>
  <BasicDrawer
    v-bind="$attrs"
    @register="registerDrawer"
    :title="title"
    :width="adaptiveWidth"
    @ok="handleSubmit"
    :showFooter="showFooter"
    destroyOnClose
    @closed="handleCloseDrawer"
  >
    <BasicForm @register="registerForm">
      <template #tableName="{ model, field }">
        <a-select v-model:value="model[field]" placeholder="请选择" allowClear>
          <a-select-option v-for="item in tableNameList" :value="item.value" :key="item.value">
            {{ item.text }}
          </a-select-option>
        </a-select>
      </template>
      <template #pushRules="{ model, field }">
        <a-select v-model:value="model[field]" placeholder="请选择推送规则" allowClear>
          <a-select-option v-for="item in ruleList" :value="item.value" :key="item.value">
            {{ item.text }}
          </a-select-option>
        </a-select>
      </template>
      <template #pushTime="{ model, field }">
        <a-time-picker v-model:value="model[field]" format="HH:mm" :disabled="model.pushRules === undefined" />
      </template>
      <template #pushScenario="{ model, field }">
        <a-select v-model:value="model[field]" placeholder="请选择推送场景" allowClear>
          <a-select-option v-for="item in scenarioList" :value="item.value" :key="item.value">
            {{ item.text }}
          </a-select-option>
        </a-select>
      </template>
      <template #atList="{ model, field }">
        <a-tree-select
          v-model:value="model[field]"
          placeholder="请选择部门或员工"
          show-search
          allow-clear
          multiple
          tree-checkable
          :tree-data="treeSelectData"
          :field-names="{ children: 'children', label: 'title', value: 'value' }"
        />
      </template>
    </BasicForm>
  </BasicDrawer>
</template>
<script lang="ts" setup>
  // 数据状态 0: 未处理、 1: 已处理、 2: 待审核、 3: 审核不通过 4: 已处理、未付款
  import { computed, ref, unref } from 'vue';
  import { getTreeList, saveOrUpdate } from '../index.api';
  import { formSchema } from '../index.data';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { useDrawerAdaptiveWidth } from '/@/hooks/jeecg/useAdaptiveWidth';
  import { initDictOptions } from '/@/utils/dict/JDictSelectUtil';
  import dayjs from 'dayjs';

  // 声明Emits
  const emit = defineEmits(['success', 'register']);
  const isUpdate = ref(true);
  const treeList = ref<any[]>([]);
  const scenarioList = ref<any[]>([]);
  const ruleList = ref<any[]>([]);
  const tableNameList = ref<any[]>([]);

  // 处理树形数据，支持三级部门结构和员工信息
  const treeSelectData = computed(() => {
    // 获取部门层级 - 使用扁平化查找方式
    const getDepartmentLevel = (node: any): number => {
      if (!node.parentId || node.parentId === '') {
        return 1; // 一级部门
      }

      // 将树形结构扁平化，创建一个 id -> 部门 的映射
      const flattenDepartments = (nodes: any[]): Map<string, any> => {
        const map = new Map();

        const traverse = (nodeList: any[]) => {
          for (const n of nodeList) {
            map.set(n.id, n);
            if (n.children && n.children.length > 0) {
              traverse(n.children);
            }
          }
        };

        traverse(nodes);
        return map;
      };

      const departmentMap = flattenDepartments(treeList.value);

      // 递归计算层级
      const calculateLevel = (currentId: string): number => {
        const dept = departmentMap.get(currentId);
        if (!dept) {
          console.warn(`未找到部门 ID: ${currentId}`);
          return 1;
        }

        if (!dept.parentId || dept.parentId === '') {
          return 1; // 一级部门
        }

        return calculateLevel(dept.parentId) + 1; // 父部门层级 + 1
      };

      const level = calculateLevel(node.id);
      return level;
    };

    // 根据层级获取部门图标
    const getDepartmentIcon = (level: number): string => {
      switch (level) {
        case 1:
          return '🏢 '; // 一级部门：总公司/集团
        case 2:
          return '🏬 '; // 二级部门：分公司/事业部
        case 3:
          return '🏪 '; // 三级部门：部门/小组
        default:
          return '📁 '; // 默认文件夹
      }
    };

    // 检查部门是否有员工（包括子部门中的员工）
    const hasEmployees = (node: any): boolean => {
      // 检查当前部门是否有员工
      if (node.usersLis && node.usersLis.length > 0) {
        return true;
      }

      // 递归检查子部门是否有员工
      if (node.children && node.children.length > 0) {
        return node.children.some((child: any) => hasEmployees(child));
      }

      return false;
    };

    // 处理树形结构数据
    const processTreeNode = (node: any): any => {
      const children: any[] = [];
      const currentLevel = getDepartmentLevel(node);

      // 先添加子部门（部门在前）
      if (node.children && node.children.length > 0) {
        const childNodes = node.children.map((child: any) => processTreeNode(child));
        children.push(...childNodes);
      }

      // 再添加当前部门的员工（员工在后）
      if (node.usersLis && node.usersLis.length > 0) {
        const userNodes = node.usersLis.map((user: any) => ({
          title: `👤 ${user.realname}(${user.workNo})`,
          value: user.phone, // 传递给接口的是手机号
          key: `${user.phone}`,
          isLeaf: true,
          selectable: true,
        }));
        children.push(...userNodes);
      }

      const departmentIcon = getDepartmentIcon(currentLevel);

      const hasEmp = hasEmployees(node);

      return {
        title: `${departmentIcon} ${node.departName}`,
        value: node.id,
        key: `${node.id}`,
        children: children,
        selectable: hasEmp, // 有员工的部门可选择
        disableCheckbox: !hasEmp, // 没有员工的部门复选框禁用，但仍显示在树中参与父子关联
      };
    };

    return treeList.value.map(node => processTreeNode(node));
  });
  //表单配置
  const [registerForm, { resetFields, setFieldsValue, validate }] = useForm({
    schemas: formSchema,
    // layout: 'vertical',
    labelWidth: 200,
    showActionButtonGroup: false,
  });

  const showFooter = ref(true);

  const title = computed(() => (unref(isUpdate) ? '编辑' : '新增'));
  //表单赋值
  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async data => {
    await resetFields();
    await getTreeList().then(res => {
      treeList.value = res;
    });
    scenarioList.value = data.scenarioList.map(item => {
      return { ...item, value: Number(item.value) };
    });
    ruleList.value = data.ruleList.map(item => {
      return { ...item, value: Number(item.value) };
    });
    initDictOptions('warning_config_table_name').then(res => {
      tableNameList.value = res;
    });
    showFooter.value = data?.showFooter ?? true;
    isUpdate.value = data?.isUpdate;
    setDrawerProps({ confirmLoading: false, showFooter: showFooter.value });
    if (data.record) {
      setFieldsValue({
        ...data.record,
        pushRules: data.record.pushRules,
        pushScenario: data.record.pushScenario,
        pushTime: dayjs(dayjs().format('YYYY-MM-DD') + ' ' + data.record.pushTime),
        atList: data.record.atList ? data.record.atList.split(',') : [],
      });
    }
  });

  const { adaptiveWidth } = useDrawerAdaptiveWidth();

  //提交事件
  async function handleSubmit() {
    try {
      let values = await validate();
      //根据code 查询城市名称
      console.log('values', values);
      setDrawerProps({ confirmLoading: true });
      let isUpdateVal = unref(isUpdate);
      await saveOrUpdate({ ...values, pushTime: dayjs(values.pushTime).format('HH:mm') }, isUpdateVal);
      //关闭弹窗
      closeDrawer();
      //刷新列表
      emit('success', { isUpdateVal, values });
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
  const handleCloseDrawer = async () => {
    await resetFields();
  };
</script>
<style lang="less" scoped>
  :deep(.ant-picker) {
    width: 100%;
  }
</style>
