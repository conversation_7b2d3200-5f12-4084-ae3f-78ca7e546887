<template>
  <BasicDrawer
    @register="registerDrawer"
    title="导入"
    :width="adaptiveWidth"
    @ok="handleSubmit"
    :showFooter="showFooter"
    destroyOnClose
    @close="handleClose"
  >
    <div class="upload-box">
      <JUpload
        accept="application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        v-model:value="fileCache"
        bucketName="ov-portal-pub"
        :maxCount="1"
        :fileSize="10"
        bizPath="merchants"
        :returnUrl="false"
        :beforeUpload="handleBeforeUpload"
        :onChange="handleUploadChange"
      />
      <div class="upload-box__text">
        1、只允许上传后缀为xls,xlsx的文件<br />
        2、单个文件大小大不超30M<br />
        3、最多上传1个文件（1000条以内）<br />
      </div>

      <div v-for="(item, index) in errorArray" :key="index" style="color: red; margin-top: 10px">
        {{ item }}
      </div>
      <div class="upload-box__model">
        <div class="upload-box__model-download" @click="DownTypeFn">导入模板下载</div>
      </div>
    </div>
  </BasicDrawer>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  import { getExcelModel, importSubEnterList } from '../index.api';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import JUpload from '/@/components/Form/src/jeecg/components/JUpload/JUpload.vue';
  import { useDrawerAdaptiveWidth } from '/@/hooks/jeecg/useAdaptiveWidth';
  import { useMessage } from '/@/hooks/web/useMessage';

  const { createMessage } = useMessage();
  // 声明Emits
  const emit = defineEmits(['success', 'register']);
  const isUpdate = ref(true);
  const fileValue = ref<any>(null); //文件提交
  const errorArray = ref<any>(); //错误信息上传
  const fileCache = ref<any>(null); //上传表格绑定model
  const showFooter = ref(true);
  // const exportUrl = ref('');

  //表单赋值
  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async data => {
    showFooter.value = data?.showFooter ?? true;
    setDrawerProps({ confirmLoading: false, showFooter: showFooter.value });
    isUpdate.value = !!data?.isUpdate;
    // 清空错误信息
    errorArray.value = [];
    // 清空文件信息
    fileValue.value = '';
  });

  const { adaptiveWidth } = useDrawerAdaptiveWidth();

  //下载模板  zip ||xls
  async function DownTypeFn() {
    await getExcelModel({
      templateCode: 'key_customer',
    }).then(res => {
      if (res) {
        window.open(res.templateExcelUrl);
      }
    });
  }
  // 上传前校验
  async function handleBeforeUpload(file) {
    fileValue.value = file;
    errorArray.value = [];
    if (!file) return;
    //校验文件类型
    const validTypes = [
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    ];
    if (!validTypes.includes(file.type)) {
      createMessage.error('只能上传xls或xlsx文件');
      return false;
    }
    // 校验文件大小（30MB）
    const isLtSize = file.size / 1024 / 1024 < 10;
    if (!isLtSize) {
      createMessage.error('文件大小不能超过 10MB');
      return false;
    }
    return false;
  }

  //提交事件
  async function handleSubmit() {
    if (!fileValue.value) {
      createMessage.error('提交失败,请先上传文件后进行提交！');
      return;
    }

    try {
      setDrawerProps({ loading: true, confirmLoading: true });
      await importSubEnterList({ file: fileValue.value })
        .then(res => {
          closeDrawer();
          emit('success', res);
        })
        .catch(err => {
          errorArray.value = err.message.split(',');
        });
    } finally {
      fileCache.value = [];
      fileValue.value = null;
      handleClose();
      setDrawerProps({ loading: false, confirmLoading: false });
    }
  }
  //关闭事件
  function handleClose() {
    fileCache.value = [];
    fileValue.value = null;
  }
  function handleUploadChange(file) {
    console.log('file excel', file);
  }
</script>

<style lang="less" scoped>
  .dif {
    display: flex;
    .label {
      display: block;
      width: 120px;
      font-size: 14px;
    }
    .required {
      color: red;
      padding-left: 6px;
    }
  }
  .col {
    flex-direction: column;
  }
  .c {
    align-items: center;
  }
  .w200 {
    width: 200px;
  }
  .mt20 {
    margin-top: 20px;
  }
  .upload-box {
    padding: 10px 20px;
    box-sizing: border-box;

    &__text {
      margin-top: 20px;
    }

    &__model {
      margin-top: 30px;

      &-download {
        cursor: pointer;
        width: fit-content;
        text-decoration: underline;
        color: blue;
      }
    }
  }
</style>
