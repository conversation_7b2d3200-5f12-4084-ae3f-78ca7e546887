import { defHttp } from '/@/utils/http/axios';

enum Api {
  list = '/market/category/list',
  save = '/market/category/add',
  edit = '/market/category/update',
  deleteCategory = '/market/category/delete',

  // 增删改 SPU
  add = '/market/spu/add',
  update = '/market/spu/update',
  deleteSPU = '/market/spu/delete',

}


// 列表接口
export const list = (params: Record<string, any>) => defHttp.get({ url: Api.list, params });

//更新接口
export const saveOrUpdate = (params: Record<string, any>, isUpdate: Boolean) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.get({ url: url, params });
};

//删除接口
export const deleteItemById = async (params: Record<string, any>, handleSuccess: Fn<any>) => {
  await defHttp.delete({ url: Api.deleteCategory, params }, { joinParamsToUrl: true });
  handleSuccess();
};
/**
 * 获取企业标签数据
 * @param params
 */
export const getEnterList = params => {
  return defHttp.get({ url: Api.list, params: { ...params } });
};

//更新spu接口
export const saveOrUpdateSPU = (params: Record<string, any>, isUpdate: Boolean) => {
  const url = isUpdate ? Api.update : Api.add;
  return defHttp.post({ url: url, params });
};


//删除SPU接口
export const deleteSPUById = async (params: Record<string, any>, handleSuccess: Fn<any>) => {
  await defHttp.delete({ url: Api.deleteSPU, params }, { joinParamsToUrl: true });
  handleSuccess();
};