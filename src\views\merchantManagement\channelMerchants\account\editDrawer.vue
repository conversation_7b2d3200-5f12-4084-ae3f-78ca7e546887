<template>
  <BasicDrawer
    v-bind="$attrs"
    @register="registerDrawer"
    :title="title"
    :width="adaptiveWidth"
    @ok="handleSubmit"
    :showFooter="showFooter"
    destroyOnClose
  >
    <BasicForm @register="registerForm">
      <template #basicNameText="{ model, field }">
        {{ model[field] }}
      </template>
      <template #basicRealNameText="{ model, field }">
        {{ model[field] }}
      </template>
      <template #basicPhoneText="{ model, field }">
        {{ model[field] }}
      </template>
    </BasicForm>
  </BasicDrawer>
</template>
<script lang="ts" setup>
  import { ref, computed, unref, useAttrs, defineProps, watch, nextTick } from 'vue';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { useDrawerAdaptiveWidth } from '/@/hooks/jeecg/useAdaptiveWidth';
  import { formSchema } from './index.data';
  import { updatePhone } from './index.api';

  // 声明Emits
  const emit = defineEmits(['success', 'register']);
  const props = defineProps({
    brandList: { type: Array, default: () => [] },
  });
  const attrs = useAttrs();
  const isUpdate = ref(true);
  //表单配置
  const [registerForm, { setProps, resetFields, setFieldsValue, validate, updateSchema, clearValidate }] = useForm({
    schemas: formSchema,
    // layout: 'vertical',
    labelWidth: 200,
    showActionButtonGroup: false,
  });

  const showFooter = ref(true);

  //表单赋值
  const [registerDrawer, { setDrawerProps, closeDrawer, getVisible }] = useDrawerInner(async data => {
    await resetFields();
    // 无论新增还是编辑，都可以设置表单值
    if (typeof data.record === 'object') {
      setFieldsValue({
        ...data.record,
        basicRealName_Text: data.record.basicRealName,
        basicPhone_Text: data.record.basicPhone,
      });
    }
  });
  //获取标题
  const title = '编辑';

  const { adaptiveWidth } = useDrawerAdaptiveWidth();

  watch(getVisible, n => {
    nextTick(() => {
      clearValidate();
    });
  });

  //提交事件
  async function handleSubmit() {
    try {
      console.log(validate);
      let values = await validate();
      console.log('🚀 ~ handleSubmit ~ values:', values);

      setDrawerProps({ confirmLoading: true });

      //提交表单
      await updatePhone({ ...values });
      //关闭弹窗
      closeDrawer();
      //刷新列表
      emit('success', { values });
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
