import { BasicColumn, FormSchema } from '@/components/Table';

export const columns: BasicColumn[] = [
  {
    title: '员工姓名',
    dataIndex: 'phone',
  },
  {
    title: '员工手机号',
    dataIndex: 'userName',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
  },
  {
    title: '账号状态',
    dataIndex: 'accountStatus',
    slots: { customRender: 'accountStatus' },
    // customRender: ({ text }) => text == 0 ?'正常':'禁用',
  },
];
export const searchFormSchema: FormSchema[] = [
  {
    label: '员工姓名',
    field: 'userName',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '员工手机号',
    field: 'phone',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '账号状态',
    field: 'accountStatus',
    component: 'Select',
    colProps: { span: 6 },
    componentProps: {
      options: [
        {
          label: '正常',
          value: 0,
        },
        {
          label: '禁用',
          value: 1,
        },
      ],
    },
  },
];
