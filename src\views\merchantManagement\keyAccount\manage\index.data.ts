import { BasicColumn, FormSchema } from '@/components/Table';
import { render } from '@/utils/common/renderUtils';
import { Badge } from 'ant-design-vue';
import { h } from 'vue';
import { getUserAllList } from '/@/api/common/api';
import { getEnterNameCheckResult } from './index.api';
import { initDictOptions } from '@/utils/dict';
import { rules } from '@/utils/helper/validator';
import { debounce } from 'lodash-es';
const phone = (required, schema, model) => {
  return [
    {
      required: required,
      type: 'string',
      validator: async (_, value) => {
        if (required && !value) {
          return Promise.reject(`请输入${schema.label}`);
        }
        if (!/^1[3456789]\d{9}$/.test(value)) {
          return Promise.reject(`${schema.label}格式有误`);
        }
        return Promise.resolve();
      },
      trigger: 'change',
    },
    {
      validator: rules.duplicateCheckRule('dis_basic_info', 'basic_phone', model, schema, false, { delFlag: 0 })[0]
        .validator,
      trigger: 'blur',
      type: 'string',
    },
    {
      validator: rules.duplicateCheckRule('ser_basic_info', 'basic_phone', model, schema, false, { delFlag: 0 })[0]
        .validator,
      trigger: 'blur',
      type: 'string',
    },
    {
      validator: rules.duplicateCheckRule('mdm_key_customers', 'phone', model, schema, false, { delFlag: 0 })[0]
        .validator,
      trigger: 'blur',
      type: 'string',
    },
  ];
};

const checkEnterName = debounce(async (value: string, id?: string, subId?: string) => {
  return await getEnterNameCheckResult({ businessName: value, id, subId });
}, 300);

export const columns: BasicColumn[] = [
  {
    title: '大客户名称',
    dataIndex: 'businessName',
    width: '23%',
    customRender: ({ text }) => (text ? text : '-'),
  },
  {
    title: '审核人',
    dataIndex: 'reviewers',
    width: '15%',
    customRender: ({ text }) => (text ? text : '-'),
  },
  {
    title: '提交时间',
    dataIndex: 'submissionTime',

    width: '15%',
    customRender: ({ text }) => (text ? text : '-'),
  },
  {
    title: '审核时间',
    dataIndex: 'reviewTime',
    width: '15%',
    customRender: ({ text }) => (text ? text : '-'),
  },
  {
    title: '审核状态',
    dataIndex: 'reviewStatus',
    customRender({ text, record }: { text: string; record: any }) {
      const colorArr = ['#ededed', 'orange', 'green', 'red'];
      // 待提交、待审核(#00ffff或cyan)、未通过、已通过
      return render.renderDictComponents(text, 'key_account_audit_status', () =>
        h(Badge, { color: colorArr[text], text: record.reviewStatusStr })
      );
    },
    width: '12%',
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '大客户名称',
    field: 'businessName',
    component: 'Input',
    colProps: { span: 6 },
    componentProps: {
      placeholder: '请输入大客户名称',
    },
  },
  {
    label: '审核人',
    field: 'reviewers',
    component: 'Input',
    colProps: { span: 6 },
  },
];

const commonFormSchema: FormSchema[] = [
  {
    label: '',
    field: 'labelKey',
    component: 'Input',
    show: false,
    defaultValue: 'detail',
  },
  {
    label: '是否是审核后的编辑',
    field: 'isAuditEdit',
    component: 'Switch',
    show: false,
    defaultValue: false,
  },
  {
    label: '是否是审核通过后编辑',
    field: 'approvedStatus',
    component: 'Switch',
    show: false,
    defaultValue: false,
  },
  {
    label: 'ID',
    field: 'id',
    component: 'Input',
    show: false,
  },
];

export const accountFormSchema: FormSchema[] = [
  ...commonFormSchema,
  {
    label: '手机号码',
    field: 'phone',
    component: 'Input',
    componentProps: {
      autoComplete: 'off',
      maxLength: 11,
    },
    required: true,
  },
  {
    label: '真实姓名',
    field: 'realName',
    component: 'Input',
    componentProps: {
      autoComplete: 'off',
    },
    required: true,
  },
];

export const basicFormSchema: FormSchema[] = [
  ...commonFormSchema,
  {
    label: '营业执照',
    field: 'license',
    component: 'JImageUpload',
    colProps: { span: 12, offset: 12, pull: 12 },
    slot: 'license',
  },
  {
    label: '手机号码',
    field: 'phone',
    component: 'Input',
    componentProps: {
      autoComplete: 'off',
      maxLength: 11,
    },
    dynamicRules: ({ model, schema }) => {
      return phone(true, schema, model);
    },
    dynamicDisabled: ({ values }) => values.isAuditEdit || values.labelKey === 'audit',
  },
  {
    label: '真实姓名',
    field: 'realName',
    component: 'Input',
    componentProps: {
      autoComplete: 'off',
    },
    required: true,
    // dynamicDisabled: ({ values }) => values.isAuditEdit,
  },
  {
    field: 'loginPassword',
    label: '登录密码',
    component: 'StrengthMeter',
    componentProps: {
      placeholder: '请输入登录密码',
      maxlength: 12,
      autoComplete: 'off',
    },
    helpMessage: ['密码强度校验5-12位,', '①至少一个大写字母、', '②至少一个小写字母、', '③至少一个数字'],
    required: true,
    rules: [
      {
        pattern: /^\S*(?=\S{5,})(?=\S*\d)(?=\S*[A-Z])(?=\S*[a-z])\S*$/,
        message: '请按要求填写密码',
        required: true,
      },
    ],
    dynamicDisabled: ({ values }) => values.isAuditEdit,
  },
  {
    field: 'secondPassword',
    label: '确认密码',
    component: 'InputPassword',
    componentProps: {
      autocomplete: 'new-password',
    },
    dynamicRules: ({ model }) => {
      return [
        {
          required: true,
          validator: (_, value) => {
            if (!value) {
              return Promise.reject('请输入确认密码');
            }
            if (value != model.loginPassword) {
              return Promise.reject('两次输入的密码不一致');
            }
            return Promise.resolve();
          },
        },
      ];
    },
    dynamicDisabled: ({ values }) => values.isAuditEdit || values.labelKey === 'audit',
  },
  {
    label: '企业名称',
    field: 'businessName',
    component: 'Input',
    componentProps: {
      autoComplete: 'off',
    },
    required: true,
    dynamicRules: ({ model, schema }) => {
      return [
        {
          required: true,
          trigger: 'blur',
          validator: async (_, value) => {
            const { list } = model;
            if (!value) {
              return Promise.reject('请输入企业名称');
            }

            if (list && list.length > -1) {
              const sameNameIndex = list?.findIndex((item: any) => item.businessName === value);
              return sameNameIndex > -1 ? Promise.reject('大客户企业名称不能与子企业的重复') : Promise.resolve();
            } else {
              return Promise.resolve();
            }
          },
        },
        {
          validator: rules.duplicateCheckRule('dis_basic_info', 'basic_name', model, schema, false, { delFlag: 0 })[0]
            .validator,
        },
        {
          validator: rules.duplicateCheckRule('dis_basic_info', 'basic_name', model, schema, false, { delFlag: 0 })[0]
            .validator,
        },
        {
          validator: rules.duplicateCheckRule('mdm_key_customers', 'business_name', model, schema, false, {
            delFlag: 0,
          })[0].validator,
        },
        {
          validator: rules.duplicateCheckRule('mdm_key_customers_sub', 'sub_business_name', model, schema, false, {
            delFlag: 0,
          })[0].validator,
        },
      ];
    },
    itemProps: {
      validateTrigger: 'blur',
    },
    dynamicDisabled: ({ values }) => values.isAuditEdit || values.labelKey === 'audit',
  },
  {
    label: '统一社会信用代码',
    field: 'socialCreditCode',
    component: 'Input',
    componentProps: {
      autoComplete: 'off',
    },
    required: true,
    dynamicRules: ({ model, schema }) => {
      return [
        {
          required: true,
          validator: (_, value) => {
            if (!value) {
              return Promise.reject('请输入统一社会信用代码');
            }
            const { list } = model;
            if (list && list.length > 0) {
              const sameSocialCreditCodeIndex = list.findIndex((item: any) => item.socialCreditCode === value);
              return sameSocialCreditCodeIndex > -1
                ? Promise.reject('大客户统一社会信用代码不能与子企业的重复')
                : Promise.resolve();
            } else {
              return Promise.resolve();
            }
          },
        },
        {
          validator: rules.duplicateCheckRule('dis_basic_info', 'basic_social_credit_code', model, schema, false, {
            delFlag: 0,
          })[0].validator,
        },
        {
          validator: rules.duplicateCheckRule('ser_basic_info', 'basic_social_credit_code', model, schema, false, {
            delFlag: 0,
          })[0].validator,
        },
        {
          validator: rules.duplicateCheckRule('mdm_key_customers', 'social_credit_code', model, schema, false, {
            delFlag: 0,
          })[0].validator,
        },
        {
          validator: rules.duplicateCheckRule('mdm_key_customers_sub', 'social_credit_code', model, schema, false, {
            delFlag: 0,
          })[0].validator,
        },
      ];
    },
    dynamicDisabled: ({ values }) => values.isAuditEdit || values.labelKey === 'audit',
  },
  {
    label: '行业',
    field: 'industry',
    component: 'Input',
    componentProps: {
      autoComplete: 'off',
    },
    required: true,
  },
  {
    label: '企业地址',
    field: 'address',
    component: 'Input',
    componentProps: {
      autoComplete: 'off',
      maxLength: 50,
    },
    required: true,
  },
  {
    label: '业务员',
    field: 'salesman',
    component: 'ApiSelect',
    componentProps: {
      api: getUserAllList,
      valueField: 'id',
      labelField: 'realname',
      resultField: 'record',
      placeholder: '请选择业务员',
      showSearch: true,
    },
    required: true,
  },
  {
    label: '',
    field: 'list',
    component: 'Input',
    show: false,
  },
];

export const bankFormSchema: FormSchema[] = [
  ...commonFormSchema,
  {
    label: '开户行',
    field: 'bankOpen',
    component: 'Input',
    required: true,
    componentProps: {
      maxlength: 20,
    },
    itemProps: {
      wrapperCol: { sm: 18 },
    },
    dynamicDisabled: ({ values }) => values.isAuditEdit || values.labelKey === 'audit',
  },
  {
    label: '银行账号',
    field: 'bankAccount',
    component: 'Input',
    required: true,
    componentProps: ({ formModel }) => {
      return {
        maxlength: 30,
        allowClear: true,
        onInput: (e: any) => {
          const rawVal = e.target.value;
          const numericVal = rawVal.replace(/\D/g, '');
          // 更新表单值，只保留数字
          formModel.bankAccount = numericVal;
        },
      };
    },
    rules: [
      {
        required: true,
        message: '请输入银行账号',
      },
    ],
    itemProps: {
      wrapperCol: { sm: 18 },
    },
    dynamicDisabled: ({ values }) => values.isAuditEdit || values.labelKey === 'audit',
  },
];

export const auditFormSchema: FormSchema[] = [
  ...commonFormSchema,
  {
    label: '审核结果',
    field: 'reviewStatus',
    component: 'ApiRadioGroup',
    componentProps: {
      api: initDictOptions,
      params: 'key_account_audit_result',
    },
    rules: [
      {
        required: true,
        message: '请选择审核结果',
      },
    ],
  },
  {
    label: '审核意见',
    field: 'remark',
    component: 'InputTextArea',
    componentProps: {
      maxlength: 500,
    },
    required: false,
  },
  {
    label: '审核日志',
    field: 'auditLog',
    component: 'Input',
    slot: 'table',
    colProps: { span: 20 },
    itemProps: {
      wrapperCol: { sm: 24 },
    },
  },
];

export const auditColumns: BasicColumn[] = [
  {
    title: '操作时间',
    dataIndex: 'createTime',
  },
  {
    title: '操作人',
    dataIndex: 'createBy',
  },
  {
    title: '操作日志',
    dataIndex: 'reviewStatusStr',
  },
  {
    title: '审核意见',
    dataIndex: 'remark',
    customRender: ({ text }) => text || '-',
  },
];

export const subEnterColumns: BasicColumn[] = [
  {
    title: '序号',
    dataIndex: 'dataIndex',
    key: 'dataIndex',
    customRender: ({ index }) => index + 1,
    width: 60,
  },
  {
    title: '企业ID',
    dataIndex: 'id',
    key: 'id',
    ellipsis: true,
    customRender: ({ text }) => text || '-',
  },
  {
    title: '企业名称',
    dataIndex: 'subBusinessName',
    key: 'subBusinessName',
    ellipsis: true,
    customRender: ({ text }) => text || '-',
  },
  {
    title: '统一社会信用代码',
    dataIndex: 'socialCreditCode',
    key: 'socialCreditCode',
    customRender: ({ text }) => text || '-',
    ellipsis: true,
  },
  {
    title: '行业',
    dataIndex: 'industry',
    key: 'industry',
    customRender: ({ text }) => text || '-',
    ellipsis: true,
    width: 120,
  },
  {
    title: '开户行',
    dataIndex: 'bankOpen',
    key: 'bankOpen',
    width: 150,
    ellipsis: true,
    customRender: ({ text }) => text || '-',
  },
  {
    title: '银行账号',
    dataIndex: 'bankAccount',
    key: 'bankAccount',
    width: 180,
    customRender: ({ text }) => text || '-',
  },
  {
    title: '企业地址',
    dataIndex: 'address',
    key: 'address',
    ellipsis: true,
    customRender: ({ text }) => text || '-',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    customRender: ({ text }) => text || '-',
    width: 160,
  },
  {
    title: '操作',
    dataIndex: 'opt',
    key: 'opt',
    width: 150,
  },
];

export const editSubEnterSchema: FormSchema[] = [
  {
    label: '',
    field: 'id',
    component: 'Input',
    ifShow: false,
  },
  {
    label: '企业名称',
    field: 'subBusinessName',
    component: 'Input',
    componentProps: {
      placeholder: '请输入企业名称',
    },
    required: true,
    dynamicRules: ({ model }) => {
      console.log('model', model);
      return [
        {
          required: true,
          trigger: 'blur',
          validator: async (_, value) => {
            if (!value) {
              return Promise.reject('请输入企业名称');
            }

            const { subList } = model;
            if (subList && subList.length > 0) {
              const sameNameIndex = subList.findIndex((item: any) => item.subBusinessName === value);
              return sameNameIndex > -1 ? Promise.reject('该企业名称与导入的子企业名称重复') : Promise.resolve();
            } else {
              return Promise.resolve();
            }
          },
        },
        {
          validator: rules.duplicateCheckRule('dis_basic_info', 'basic_name', model, null, false, { delFlag: 0 })[0]
            .validator,
        },
        {
          validator: rules.duplicateCheckRule('dis_basic_info', 'basic_name', model, null, false, { delFlag: 0 })[0]
            .validator,
        },
        {
          validator: rules.duplicateCheckRule('mdm_key_customers', 'business_name', model, null, false, {
            delFlag: 0,
          })[0].validator,
        },
        {
          validator: rules.duplicateCheckRule('mdm_key_customers_sub', 'sub_business_name', model, null, false, {
            delFlag: 0,
          })[0].validator,
        },
      ];
    },
    itemProps: {
      validateTrigger: 'blur',
    },
  },
  {
    label: '社会信用代码',
    field: 'socialCreditCode',
    component: 'Input',
    componentProps: {
      placeholder: '请输入社会信用代码',
    },
    required: true,
    dynamicRules: ({ model }) => {
      console.log('model', model);
      return [
        {
          required: true,
          trigger: 'blur',
          validator: async (_, value) => {
            if (!value) {
              return Promise.reject('请输入社会信用代码');
            }

            const { subList } = model;
            if (subList && subList.length > 0) {
              const sameSocialCreditCodeIndex = subList.findIndex((item: any) => item.socialCreditCode === value);
              return sameSocialCreditCodeIndex > -1
                ? Promise.reject('该社会信用代码与导入的子企业社会信用代码重复')
                : Promise.resolve();
            } else {
              return Promise.resolve();
            }
          },
        },
        {
          validator: rules.duplicateCheckRule('dis_basic_info', 'basic_social_credit_code', model, null, false, {
            delFlag: 0,
          })[0].validator,
        },
        {
          validator: rules.duplicateCheckRule('dis_basic_info', 'basic_social_credit_code', model, null, false, {
            delFlag: 0,
          })[0].validator,
        },
        {
          validator: rules.duplicateCheckRule('mdm_key_customers', 'social_credit_code', model, null, false, {
            delFlag: 0,
          })[0].validator,
        },
        {
          validator: rules.duplicateCheckRule('mdm_key_customers_sub', 'social_credit_code', model, null, false, {
            delFlag: 0,
          })[0].validator,
        },
      ];
    },
  },
  {
    label: '行业',
    field: 'industry',
    component: 'Input',
    componentProps: {
      placeholder: '请输入行业',
    },
  },
  {
    label: '开户行',
    field: 'bankOpen',
    component: 'Input',
    componentProps: {
      placeholder: '请输入开户行',
    },
    required: true,
  },
  {
    label: '银行账号',
    field: 'bankAccount',
    component: 'Input',
    componentProps: {
      placeholder: '请输入银行账号',
    },
    required: true,
  },
  {
    label: '企业地址',
    field: 'address',
    component: 'Input',
    componentProps: {
      placeholder: '请输入企业地址',
    },
    required: true,
  },
];
