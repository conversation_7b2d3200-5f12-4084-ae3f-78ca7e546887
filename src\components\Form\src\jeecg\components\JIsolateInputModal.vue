<template>
  <a-input :class="`${prefixCls}-input`" :value="innerValue" :disabled="disabled" v-bind="attrs" @change="onInputChange">
    <template #suffix>
      <Icon :icon="isOk ? 'ant-design:check-circle' : 'ant-design:plus-outlined'" :color="isOk ? '#3A78F9' : ''" @click.stop="onShowPopup" />
    </template>
  </a-input>
  <a-modal v-model:visible="visible" :title="modalTitle" @ok="handleOk" :closable="false" @cancel="handleClose">
    <a-form layout="horizontal" name="form_in_modal">
      <a-form-item :label="textareaLabel" name="pass" v-bind="validateInfos.innerTextareaValue" :label-col="{ span: labelCol }" :wrapper-col="{ span: wrapperCol }">
        <div style="position: relative">
          <a-textarea :placeholder="textareaPlaceholder" :rows="6" :showCount="false" v-model:value="modelRef.innerTextareaValue" :disabled="disabled" :ref="textareaRef" />
          <span v-if="showCount" style="position: absolute; right: 20px; bottom: 5px; z-index: 1; font-size: 16px; user-select: none">
            <span
              :style="{
                color: currentValueRows > textareaMaxValueRows ? 'red' : '',
                fontWeight: currentValueRows > textareaMaxValueRows ? 'bold' : '',
              }"
              >{{ currentValueRows }}</span
            >
            / {{ textareaMaxValueRows }}
          </span>
        </div>
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts" setup>
  // example:
  // {
  //   label: '车架号',
  //   field: 'vin',
  //   component: 'JIsolateInputModal',
  //   componentProps: ({formActionType}) => {
  //     return {
  //       textareaLabel: '车架号', // 弹出框标题
  //       placeholder: '点击加号，批量搜索',
  //       textareaPlaceholder: '批量查询，用换行分割',
  //       textareaBindField: 'fuzzyVin', //  绑定的查询字段
  //       formActionType, // 获取form的action
  //     }
  //   },
  //   ...
  // },
  import { FormActionType, FormSchema } from '@/components/Form';
  import { Form } from 'ant-design-vue';
  import type { PropType } from 'vue';
  import { computed, nextTick, onMounted, reactive, ref, watch } from 'vue';
  import Icon from '/@/components/Icon/src/Icon.vue';
  import { useAttrs } from '/@/hooks/core/useAttrs';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { propTypes } from '/@/utils/propTypes';
  const useForm = Form.useForm;

  const { prefixCls } = useDesign('j-isolate-input-modal');
  const props = defineProps({
    // v-model:value
    value: propTypes.string.def(''),
    title: propTypes.string.def(''),
    // 弹出框显示位置
    modalTitle: propTypes.string.def('批量查询'),
    labelWidth: propTypes.number.def(70),
    // height: propTypes.number.def(150),
    disabled: propTypes.bool.def(false),
    // 表单action
    formActionType: Object as PropType<FormActionType>,
    // 绑定的查询字段
    textareaBindField: propTypes.string.def(''),
    textareaLabel: propTypes.string.def('车牌号'),
    textareaPlaceholder: propTypes.string.def(''),
    textareaMaxLength: propTypes.number.def(1000),
    labelCol: propTypes.number.def(4),
    wrapperCol: propTypes.number.def(20),

    showCount: propTypes.bool.def(true),
    textareaMaxValueRows: propTypes.number.def(1000),
    // 弹出框挂载的元素ID
    popContainer: propTypes.oneOfType([propTypes.string, propTypes.func]).def(''),
  });
  const attrs = useAttrs();
  const emit = defineEmits(['change', 'update:value', 'textareaChange']);

  const visible = ref<boolean>(false);
  const innerValue = ref<string>('');
  // textarea ref对象
  const textareaRef = ref();
  const modelRef = reactive({
    innerTextareaValue: '',
  });
  const isOk = ref(false);
  const rulesRef = reactive({
    innerTextareaValue: [
      {
        required: true,
        validator: () => {
          if (currentValueRows.value > props.textareaMaxValueRows) {
            return Promise.reject('最多输入' + props.textareaMaxValueRows + '行');
          }
          if (!modelRef.innerTextareaValue.length) {
            return Promise.reject('请正确输入' + props.textareaLabel);
          }
          return Promise.resolve();
        },
      },
    ],
  });

  watch(
    () => props.value,
    value => {
      if (value && value.length > 0) {
        innerValue.value = value;
      } else {
        innerValue.value = '';
      }
    },
    { immediate: true }
  );

  watch(
    async () => {
      const { getFieldsValue } = props.formActionType;
      return await getFieldsValue()[props.textareaBindField];
    },
    async () => {
      const { getFieldsValue } = props.formActionType;
      isOk.value = !!getFieldsValue()[props.textareaBindField];
      modelRef.innerTextareaValue = getFieldsValue()[props.textareaBindField] || '';
    },
    { immediate: true }
  );

  const currentValueRows = computed(() => {
    const lines = modelRef.innerTextareaValue.split('\n');
    if (lines.length > 0) {
      return lines.length;
    }
    return 0;
  });

  onMounted(() => {
    if (props.formActionType) {
      const { appendSchemaByField } = props.formActionType;
      if (appendSchemaByField && props.textareaBindField && props.textareaBindField.length > 0) {
        const params: FormSchema = {
          field: props.textareaBindField,
          label: props.textareaLabel,
          show: false,
          component: 'Input',
        };
        appendSchemaByField(params, '');
      }
    }
  });
  function onInputChange(event) {
    isOk.value = false;
    const { setFieldsValue } = props.formActionType || {};
    innerValue.value = event.target.value;
    modelRef.innerTextareaValue = '';
    emit('textareaChange', '');
    if (setFieldsValue && props.textareaBindField && props.textareaBindField.length > 0) {
      setFieldsValue({ [props.textareaBindField]: '' });
    }
    emitValue(innerValue.value);
  }

  const { validate, validateInfos, clearValidate } = useForm(modelRef, rulesRef);
  function handleOk() {
    validate()
      .then(async () => {
        isOk.value = true;
        const { setFieldsValue } = props.formActionType || {};
        if (setFieldsValue && props.textareaBindField && props.textareaBindField.length > 0) {
          await setFieldsValue({ [props.textareaBindField]: modelRef.innerTextareaValue });
        }
        // 置空输入框();
        emitValue('');
        visible.value = false;
      })
      .catch(info => {
        isOk.value = false;
        console.log('Validate Failed:', info);
      });
  }

  function handleClose() {
    isOk.value = isOk.value && modelRef.innerTextareaValue.length > 0;
  }

  async function onShowPopup() {
    clearValidate();
    visible.value = true;
    await nextTick();
    textareaRef.value?.focus();
  }
  function emitValue(value) {
    emit('change', value);
    emit('update:value', value);
  }
</script>

<style lang="less" scoped>
  //noinspection LessUnresolvedVariable
  @prefix-cls: ~'@{namespace}-j-isolate-input-modal';

  .@{prefix-cls} {
    &-popover {
    }

    &-input {
      .app-iconify {
        cursor: pointer;
        color: #666666;
        transition: color 0.3s;

        &:hover {
          color: black;
        }
      }
    }
  }
  :deep(.ant-form-item) {
    padding: 10px !important;
  }
</style>
