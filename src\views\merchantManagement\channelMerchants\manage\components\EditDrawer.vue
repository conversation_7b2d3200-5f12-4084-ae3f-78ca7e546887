<template>
  <BasicDrawer class="manage-list__edit-drawer" v-bind="$attrs" @register="registerDrawer" width="90%" destroyOnClose @close="handleCancel">
    <a-row :gutter="10">
      <a-col :span="2">
        <a-tabs class="edit-drawer__tabs" v-model:activeKey="state.activeKey" @change="handleChange" tabPosition="left">
          <a-tab-pane v-for="item in state.tabs" :key="item.key" :tab="item.title" :disabled="item.disabled" v-bind="item" />
        </a-tabs>
      </a-col>
      <a-col :span="22">
        <div v-show="state.activeKey == 'account'">
          <BasicForm name="account" @register="accountRegisterForm" />
        </div>
        <div v-show="state.activeKey == 'basic'">
          <BasicForm name="basic" @register="basicRegisterForm">
            <template #SelectRelate="{ model, field }">
              <!-- 审核后，另一状态不展示 -->
              <a-button v-if="!canSetRelate || model[field] == 2" :type="model[field] == 2 ? 'primary' : ''" @click="setRelate(2, field,model[field])">不关联</a-button>
              <a-button v-if="!canSetRelate || model[field] == 1" :type="model[field] == 1 ? 'primary' : ''" @click="setRelate(1, field,model[field])">关联</a-button>
            </template>
            <template #title="{ model, field }">
              <a-typography-title :level="5"> <span class="red_start">*</span>{{ model[field] }}</a-typography-title>
            </template>
            <template #latlng>
              <div class="map-slot">
                <div id="container" ref="mapEl"></div>
                <div id="result">
                  详细的地址：<div>{{ state.address.text }}</div> 经纬度：<div>{{ state.address.lnglat }}</div>
                </div>
              </div>
            </template>
          </BasicForm>
        </div>
        <div v-show="state.activeKey == 'finance'">
          <BasicForm name="finance" @register="financeRegisterForm" />
        </div>
        <div v-show="state.activeKey == 'contact'">
          <BasicForm name="contact" @register="contactRegisterForm">
            <template #emptyPlaceholder>
              <div class="emptyPlaceholder"></div>
            </template>
            <template #updateImage="{ schema, model }">
              <UploadImage
                v-bind="schema.buss"
                :schema="schema"
                :model="model"
                bizPath="/dis/acc/con"
                :disabled="!showFooter || state.approvedStatus || state.auditing"
                :exampleList="state.annexExampleList"
                @change="handleUploadChange"
              />
            </template>
          </BasicForm>
        </div>
        <div v-show="state.activeKey == 'annex'">
          <BasicForm name="annex" @register="annexRegisterForm">
            <template #title="{ model, field }">
              <a-typography-title :level="5">{{ model[field] }}</a-typography-title>
            </template>
            <template #updateImage="{ schema, model }">
              <UploadImage
                v-bind="schema.buss"
                :schema="schema"
                :model="model"
                bizPath="/dis/acc/annex"
                :disabled="!showFooter || state.approvedStatus || state.auditing"
                :exampleList="state.annexExampleList"
              />
            </template>
          </BasicForm>
        </div>
        <div v-show="state.activeKey == 'audit'">
          <BasicForm name="audit" @register="auditRegisterForm">
            <template #table>
              <BasicTable @register="registerTable" />
            </template>
          </BasicForm>
        </div>
      </a-col>
    </a-row>
    <template #footer v-if="showFooter">
      <template v-if="state.auditing">
        <a-button @click="handleCancel">暂不审核</a-button>
        <a-button v-auth="'dis:audit:confirm'" type="primary" @click="handleConfirmAudit">确认审核</a-button>
      </template>
      <template v-else>
        <a-button v-show="state.activeKey !== 'basic'" @click="handlePrev">上一步</a-button>
        <a-button v-show="state.activeKey == 'basic'" @click="handleCancel">取消</a-button>
        <a-button v-auth="'dis:acc:save'" v-if="state.activeKey == 'annex' && state.approvedStatus" type="primary" @click="handleSave">确认</a-button>
        <a-button v-auth="'dis:acc:save'" v-else @click="handleSave">保存</a-button>
        <a-button v-auth="'dis:audit:submit'" v-show="state.activeKey == 'annex' && !state.approvedStatus" type="primary" @click="handleSubmitAudit">提交审核</a-button>
        <a-button v-auth="'dis:acc:save'" v-show="!(state.activeKey == 'annex' || state.activeKey == 'audit')" type="primary" @click="handleNext">下一步</a-button>
      </template>
    </template>
  </BasicDrawer>
</template>
<script setup lang="ts">
  import { reactive, ref, computed, unref } from 'vue';
  import { findIndex, isEqual } from 'lodash-es';
  import { BasicDrawer, useDrawerInner } from '@/components/Drawer';
  import { BasicForm, useForm } from '@/components/Form';
  import { accountFormSchema, annexFormSchema, auditColumns, auditFormSchema, basicFormSchema, contactFormSchema, financeFormSchema } from '../index.data';
  import { confirmAudit, getAnnexExampleList, getIDInfo, logList, queryByIdApi, saveOrUpdate, submitAudit, listSer } from '../index.api';
  import UploadImage from './UploadImage.vue';
  import { BasicTable } from '@/components/Table';
  import { useListPage } from '@/hooks/system/useListPage';
  import { isArray, isEmpty, isNullOrUnDef } from '@/utils/is';
  import { regionDataPlus } from '@/components/Form/src/utils/areaDataUtil';
  import { useMaps } from '@/views/merchantManagement/channelMerchants/manage/components/useMap';

  const emit = defineEmits(['success', 'register', 'cancel']);
  const isUpdate = ref(true);
  const showFooter = ref(true);
  const map = ref(null);
  const mapEl = ref(null);

  const state = reactive({
    defaultTabs: [
      {
        title: '账号信息',
        key: 'account',
        disabled: false,
      },
      {
        title: '基本信息',
        key: 'basic',
        disabled: false,
      },
      {
        title: '财务信息',
        key: 'finance',
        disabled: false,
      },
      {
        title: '联系信息',
        key: 'contact',
        disabled: false,
      },
      {
        title: '材料附件',
        key: 'annex',
        disabled: false,
      },
      {
        title: '审核意见',
        key: 'audit',
        disabled: false,
      },
    ],
    tabs: [],
    activeKey: 'basic',
    record: {},
    auditStatus: null,
    isAuditStatus: false, //是否已经审核
    approvedStatus: false, //是否审核通过
    auditing: false, //进行审核
    createId: null, //创建时点击下一步生成的id
    annexExampleList: [],
    detailRes: {},
    address: {
      text: '',
      lnglat: '',
    },
    labelKey: null,
    currentChannelId: null,
    currentChannelInfo: null,
  });

  const [accountRegisterForm, accountFormMethods] = useForm({
    // name: 'basic',
    labelWidth: 120,
    schemas: accountFormSchema,
    showActionButtonGroup: false,
    baseColProps: {
      span: 12,
    },
  });

  const [basicRegisterForm, basicFormMethods] = useForm({
    // name: 'basic',
    labelWidth: 120,
    schemas: basicFormSchema,
    showActionButtonGroup: false,
    baseColProps: {
      span: 12,
    },
  });

  const [financeRegisterForm, financeFormMethods] = useForm({
    // name: 'finance',
    labelWidth: 120,
    schemas: financeFormSchema,
    showActionButtonGroup: false,
    baseColProps: {
      span: 12,
    },
    wrapperCol: {
      sm: 24,
    },
  });
  const [contactRegisterForm, contactFormMethods] = useForm({
    // name: 'contact',
    labelWidth: 130,
    schemas: contactFormSchema,
    showActionButtonGroup: false,
    baseColProps: {
      span: 12,
    },
    wrapperCol: {
      sm: 24,
    },
  });
  const [annexRegisterForm, annexFormMethods] = useForm({
    // name: 'annex',
    // labelWidth: 220,
    schemas: annexFormSchema,
    showActionButtonGroup: false,
    baseColProps: {
      span: 24,
    },
    labelCol: {
      span: 24,
    },
  });
  const [auditRegisterForm, auditFormMethods] = useForm({
    // name: 'audit',
    labelWidth: 120,
    schemas: auditFormSchema,
    showActionButtonGroup: false,
  });

  // 列表页面公共参数、方法
  const { tableContext, createMessage } = useListPage({
    tableProps: {
      api: logList,
      beforeFetch: params => {
        params.infoId = state.record?.id;
      },
      rowKey: 'id',
      columns: auditColumns,
      showActionColumn: false,
      useSearchForm: false,
      immediate: false,
    },
  });

  //注册table数据
  const [registerTable, { reload }] = tableContext;
  const { creatMapTianDi } = useMaps();
  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async data => {
    const isAuditStatus = data.record?.auditStatus == 3 || data.record?.auditStatus == 4; //审核后
    
    // const isAuditStatus = data.record?.auditStatus == 4; //审核通过后
    const approvedStatus = data.record?.auditStatus == 4; //审核通过后
    state.labelKey = data?.labelKey || 'detail';
    state.address.lnglat = '';
    state.address.text = '';
    state.auditStatus = data.record?.auditStatus;
    isUpdate.value = !!data?.isUpdate;
    showFooter.value = !!data?.showFooter;
    const defaultActiveKey = !unref(isUpdate) && !unref(showFooter) && isAuditStatus ? 'account' : 'basic';
    state.activeKey = data?.activeKey || defaultActiveKey;
    state.record = data?.record || {};
    getSerList(); // state.record 赋值后在调用getSerlist
    state.auditing = data?.activeKey === 'audit';
    let titleStr = '';
    if (state.auditing) {
      titleStr = '渠道审核页';
    } else if (unref(isUpdate)) {
      titleStr = '编辑渠道商';
    } else if (unref(showFooter)) {
      titleStr = '新增渠道商';
    } else {
      titleStr = '渠道商详情';
    }
    await setDrawerProps({ confirmLoading: false, title: titleStr, showFooter: unref(showFooter) });
    state.approvedStatus = approvedStatus;
    state.isAuditStatus = isAuditStatus;
    await getAnnexExample();
    await initMap();
    // console.log(data, 'data.record12121212');
    //审核tab只有在审核之后的详情和点击审核进来显示
    let tabsArr = [];
    if (!unref(isUpdate) && !unref(showFooter) && isAuditStatus) {
      tabsArr = state.defaultTabs;
    } else if (state.auditing) {
      tabsArr = state.defaultTabs.filter(tab => tab.key !== 'account');
    } else {
      tabsArr = state.defaultTabs.filter(tab => tab.key !== 'audit' && tab.key !== 'account');
    }
    //不是审核页和不是详情页
    (tabsArr || []).map(item => (item.disabled = !state.auditing && unref(showFooter)));
    state.tabs = tabsArr;
    await handleChange();
  });
  const initMap = async () => {
    map.value = await creatMapTianDi({ wrapRef: unref(mapEl) });
    if (!unref(showFooter) || state.approvedStatus || state.auditing) {
      return null;
    }
    let geoc = new T.Geocoder();
    unref(map).addEventListener('click', e => handleSetLngLat(e, geoc));
  };
  const handleGetLngLat = async () => {
    const { validateFields, setFieldsValue } = getFormActive();
    try {
      let values = await validateFields(['basicRegisteredAddress']);
      const province = (regionDataPlus || []).filter(region => region.value === values.basicRegisteredAddress[0])[0];
      const city = (province?.children || []).filter(region => region.value === values.basicRegisteredAddress[1])[0];
      const area = (city?.children || []).filter(region => region.value === values.basicRegisteredAddress[2])[0];
      let adressStr = `${province?.label}${city?.label}${area?.label}`;

      // console.log(regionDataPlus, province, city, area, 1212);
      // console.log(values, adressStr);
      //创建地址解析器实例
      let myGeo = new T.Geocoder();
      //清除地图上所有的覆盖物
      unref(map).clearOverLays();
      // 将地址解析结果显示在地图上，并调整地图视野
      myGeo.getPoint(adressStr, function (result) {
        // console.log(result, result.getStatus(), result.getLocationPoint(), 1212);
        if (result.status == 0) {
          unref(map).panTo(
            {
              lat: result.location.lat,
              lng: result.location.lon,
            },
            16
          );
          state.address.text = adressStr;
          state.address.lnglat = `${result.location.lon},${result.location.lat}`;
          setFieldsValue({
            basicAddressLngLat: `${result.location.lon},${result.location.lat}`,
          });
        } else {
          createMessage.info('您选择的地址没有解析到结果！');
        }
      });
    } catch (err) {
      console.log(err, 'err');
    }
  };
  const handleSetLngLat = async (e, geoc, uploadValue = true) => {
    const { setFieldsValue } = getFormActive();
    //清除地图上所有的覆盖物
    unref(map).clearOverLays();
    // console.log(e);
    let pt = e.lnglat;
    const point = new T.LngLat(pt.lng, pt.lat);
    const marker = new T.Marker(point);
    unref(map).addOverLay(marker);
    await geoc.getLocation(point, function (rs) {
      let addComp = rs?.addressComponent;
      // console.log(rs, 'rs');
      if (!addComp) return null;
      let text = `${addComp.province}`;
      text += addComp?.city !== '' ? `, ${addComp.city}` : '';
      text += `, ${addComp.county}`;
      text += addComp?.town ? `, ${addComp.town}` : '';
      text += addComp?.poi ? `, ${addComp.poi}` : '';
      state.address.text = text;
      state.address.lnglat = `${pt.lng}, ${pt.lat}`;
      if (uploadValue) {
        const addCompCity = addComp.city == '' ? '市辖区' : addComp.city;
        const province = (regionDataPlus || []).filter(region => region.label === addComp.province)[0];
        const city = (province?.children || []).filter(region => region.label === addCompCity)[0];
        const area = (city?.children || []).filter(region => region.label === addComp.county)[0];
        setFieldsValue({
          basicRegisteredAddress: [province?.value, city?.value, area?.value],
          basicAddressLngLat: `${pt.lng},${pt.lat}`,
        });
      }
    });
  };
  const getFormActive = () => {
    let formMethods = basicFormMethods;
    switch (state.activeKey) {
      case 'account':
        formMethods = accountFormMethods;
        break;
      case 'finance':
        formMethods = financeFormMethods;
        break;
      case 'contact':
        formMethods = contactFormMethods;
        break;
      case 'annex':
        formMethods = annexFormMethods;
        break;
      case 'audit':
        formMethods = auditFormMethods;
        break;
      default:
        formMethods = basicFormMethods;
        break;
    }
    return formMethods;
  };
  //处理为空的数据
  const handleEmptyFields = obj => {
    return Object.entries(obj).reduce((acc, [key, value]) => {
      if (!isNullOrUnDef(value)) {
        acc[key] = value;
      }
      return acc;
    }, {});
  };
  const handleDetailInfo = res => {
    let values = res;
    if (!res) {
      values = {
        infoId: state?.record?.id,
      };
    } else if (state.activeKey == 'basic') {
      let arr = (res?.basicAddressLngLat || '').split(',');
      let lng = Number(arr[0]) || 116.404,
        lat = Number(arr[1]) || 39.915,
        zoom = arr?.length == 2 ? 16 : 10;
      let geoc = new T.Geocoder();
      let point = new T.LngLat(lng, lat); // 创建点坐标
      unref(map).centerAndZoom(point, zoom);
      handleSetLngLat(
        {
          lnglat: {
            lng: lng,
            lat: lat,
          },
        },
        geoc,
        false
      );
      // console.log(res?.basicAddressLngLat, lng, lat, zoom, 'initMap');
      values = {
        ...res,
        confirmPassword: res.basicPassword,
        basicRegisteredAddress: res?.basicRegisteredAddress ? (res?.basicRegisteredAddress || '').split(',') : null,
      };
    } else if (state.activeKey == 'finance') {
      values = {
        ...res,
        financeInvoicingAddress: res?.financeInvoicingAddress ? (res?.financeInvoicingAddress || '').split(',') : null,
      };
    } else if (state.activeKey == 'contact') {
      values = {
        ...res,
        contactDeliveryAddress: res?.contactDeliveryAddress ? (res?.contactDeliveryAddress || '').split(',') : null,
        contactPickAddress: res?.contactPickAddress ? (res?.contactPickAddress || '').split(',') : null,
      };
    } else if (state.auditing && state.activeKey == 'audit') {
      //审核失败再审核，不能赋值ps:准入审核页不能赋值
      values = {
        id: res?.id,
      };
    }
    let fieldsValue = handleEmptyFields(values);
    return fieldsValue;
  };
  const handleUploadChange = async (val, item) => {
    if (!val || item.key !== 'contactActualIdUrl1') {
      return null;
    }
    const { setFieldsValue, getFieldsValue } = getFormActive();
    const values = getFieldsValue();
    try {
      const res = await getIDInfo({
        fileUrl: val,
      });
      if (values.contactActualController !== res?.name) {
        setFieldsValue({
          contactActualController: res?.name,
        });
      }
      console.log(res, 'handleUploadChange');
    } catch (err) {
      console.log(err, 'handleUploadChange');
    }
    console.log(val, item, 'handleUploadChange');
  };
  const getDetailById = async () => {
    if (!state.record?.id) {
      return null;
    }
    // '基本信息', '财务信息', 联系信息', '材料附件', '审核意见',
    // 'basic','finance', 'contact', 'annex', 'audit',
    const { setFieldsValue, updateSchema } = getFormActive();
    try {
      const res = await queryByIdApi({ infoId: state.record.id }, state.activeKey);
      let fieldsValue = handleDetailInfo(res);
      state.detailRes[state.activeKey] = fieldsValue;
      if (!isEmpty(fieldsValue)) {
        state.currentChannelId = fieldsValue.serId || null;
        await setFieldsValue({
          ...fieldsValue,
        });
        initSerItem();
      }
      if (state.activeKey == 'annex') {
        updateSchema({
          field: 'annexAdditionalInformation',
          ifShow: () => {
            return fieldsValue?.annexAdditionalInformation?.length || (unref(showFooter) && !state.auditing);
          },
        });
      }
      if (state.activeKey === 'basic' && !isEmpty(fieldsValue) && !fieldsValue?.basicAddressLngLat) {
        handleGetLngLat();
      }
    } catch (err) {
      console.log(err, 'err');
    }
  };

  const getAnnexExample = async () => {
    try {
      const res = await getAnnexExampleList();
      state.annexExampleList = res;
    } catch (err) {
      console.log(err, 'err');
    }
  };

  const handleChange = async () => {
    let isAuditEdit = (!isEmpty(state?.record) && !unref(isUpdate)) || state.auditing;
    const { resetFields, setProps, setFieldsValue, updateSchema, getFieldsValue } = getFormActive();
    await resetFields();
    let defaultValue: any = {
      isAuditEdit: isAuditEdit,
      approvedStatus: state.approvedStatus,
      auditStatus: state.auditStatus
    };
    if (state.createId) {
      defaultValue = {
        ...defaultValue,
        infoId: state.createId,
      };
    }
    await setFieldsValue({
      ...defaultValue,
    });
    //禁用表单 -- 审核时，禁用其他表单填写功能
    setProps({ disabled: !unref(showFooter) || state.approvedStatus || (state.auditing && state.activeKey !== 'audit') });

    if (state.activeKey == 'basic') {
      updateSchema([
        {
          field: 'serId',
          componentProps: {
            options: serOptions.value,
            onChange: value => {
              const item = serOptions.value.find(item => item.id === value);
              if (item) {
                setFieldsValue({
                  basicPhone: item?.basicPhone,
                  basicRealName: item?.basicRealName,
                  basicSocialCreditCode: item?.basicSocialCreditCode,
                });
                initSerDefaultValue(item, getFieldsValue, setFieldsValue);
              }
            },
          },
        },
        {
          field: 'basicPhone',
          show: undefined,
          ifShow: ({ values }) => {
            // 审核后不展示，仅在 账号信息 tab 展示
            return (unref(showFooter) || !state.isAuditStatus) && !(values.relatedSer != 1 && values.relatedSer != 2); // 关联信息没填，不展示
          },
        },
        {
          field: 'basicRealName',
          show: undefined,
          ifShow: ({ values }) => {
            // 审核后不展示，仅在 账号信息 tab 展示
            return (unref(showFooter) || !state.isAuditStatus) && !(values.relatedSer != 1 && values.relatedSer != 2); // 关联信息没填，不展示
          },
        },
        {
          field: 'basicPassword',
          show: undefined,
          ifShow: ({ values }) => {
            // 审核后不展示
            return unref(showFooter) && !state.approvedStatus && values.relatedSer == 2; // 不关联，展示
          },
        },
        {
          field: 'confirmPassword',
          show: undefined,
          ifShow: ({ values }) => {
            // 审核后不展示
            return unref(showFooter) && !state.approvedStatus && values.relatedSer == 2; // 不关联，展示
          },
        },
        {
          field: 'basicBusinessStatus',
          show: undefined,
          ifShow: () => {
            return state.approvedStatus;
          },
        },
        {
          field: 'basicRegisteredAddress',
          componentProps: ({}) => {
            return {
              placeholder: '请选择省市区',
              onChange: handleGetLngLat,
            };
          },
        },
      ]);
    }
    // 处理财务信息和材料附件信息
    if (state.activeKey == 'finance') {
      initSerDefaultValue(state.currentChannelInfo, getFieldsValue, setFieldsValue);
    }
    if (state.activeKey == 'annex') {
      initSerDefaultValue(state.currentChannelInfo, getFieldsValue, setFieldsValue);
    }
    if (state.activeKey == 'audit') {
      reload({ page: 1 });
    }
    await getDetailById();
  };

  const handlePrev = async () => {
    let index = findIndex(state.tabs, ['key', state.activeKey]);
    index--;
    if (index >= 0) {
      state.activeKey = state.tabs[index].key;
      // handleChange(state.activeKey);
    }
  };
  const handleFormValueIsChange = value => {
    //过滤本地的isAuditEdit
    const { isAuditEdit, ...filterNull } = handleEmptyFields(value);
    //将值为数组改为json
    const detailRes = Object.entries(state.detailRes[state.activeKey] || {}).reduce((acc, [key, value]) => {
      if (isArray(value)) {
        acc[key] = value.toString();
      } else {
        acc[key] = value;
      }
      return acc;
    }, {});
    //添加一些表单中没有用到的返回值
    const addItemObj = {
      ...detailRes,
      ...filterNull,
    };
    return !isEqual(detailRes, addItemObj);
  };
  const handleNext = async () => {
    const { validate, setFieldsValue } = getFormActive();
    let index = findIndex(state.tabs, ['key', state.activeKey]);
    try {
      const values = await validate();
      const params = {
        ...values,
        annexLitFinMore: values?.annexLitFinMore ? (values?.annexLitFinMore || '').split(',') : undefined,
        annexCarRentalMore: values?.annexCarRentalMore ? (values?.annexCarRentalMore || '').split(',') : undefined,
        annexControllerCreditReportMore: values?.annexControllerCreditReportMore ? (values?.annexControllerCreditReportMore || '').split(',') : undefined,
        annexEquityProxyAgreementMore: values?.annexEquityProxyAgreementMore ? (values?.annexEquityProxyAgreementMore || '').split(',') : undefined,
        annexInventorySalesLedgerMore: values?.annexInventorySalesLedgerMore ? (values?.annexInventorySalesLedgerMore || '').split(',') : undefined,
        annexCorporateAssetsMore: values?.annexCorporateAssetsMore ? (values?.annexCorporateAssetsMore || '').split(',') : undefined,
        annexPersonalAssetsMore: values?.annexPersonalAssetsMore ? (values?.annexPersonalAssetsMore || '').split(',') : undefined,
        annexIdMarriageMore: values?.annexIdMarriageMore ? (values?.annexIdMarriageMore || '').split(',') : undefined,
        annexAdditionalInformation: values?.annexAdditionalInformation ? (values?.annexAdditionalInformation || '').split(',') : undefined,
        annexCompanyRuleMore: values?.annexCompanyRuleMore ? (values?.annexCompanyRuleMore || '').split(',') : undefined,
        annexCreditReportMore: values?.annexCreditReportMore ? (values?.annexCreditReportMore || '').split(',') : undefined,
        annexBusinessCaseMore: values?.annexBusinessCaseMore ? (values?.annexBusinessCaseMore || '').split(',') : undefined,
        annexOrgStructureMore: values?.annexOrgStructureMore ? (values?.annexOrgStructureMore || '').split(',') : undefined,
        annexLeaseAgreementMore: values?.annexLeaseAgreementMore ? (values?.annexLeaseAgreementMore || '').split(',') : undefined,
        annexSitePhotoMore: values?.annexSitePhotoMore ? (values?.annexSitePhotoMore || '').split(',') : undefined,
      };
      delete params.emptyPlaceholder;
      //审核后才编辑，审核前全是保存
      setDrawerProps({ confirmLoading: true });
      //不相同进行保存
      let createId = state?.detailRes?.id || null;
      if (handleFormValueIsChange(params)) {
        const res = await saveOrUpdate(params, state.activeKey);
        createMessage.success('保存成功');
        createId = res?.id || null;
        let fieldsValue = handleDetailInfo(res);
        state.detailRes[state.activeKey] = fieldsValue;
        setFieldsValue({ id: res?.id });
      }
      if (!values?.id && state.activeKey == 'basic') {
        state.createId = createId;
      }
      index++;
      if (index <= state.tabs.length - 1) {
        state.activeKey = state.tabs[index].key;
        handleChange();
      }
    } catch (err) {
      console.log(err, 'err');
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  };
  const handleCancel = () => {
    closeDrawer();
    emit('cancel');
  };
  const handleSave = async () => {
    try {
      const { getFieldsValue } = getFormActive();
      const values = getFieldsValue();
      const valuesKeys = Object.keys(values);
      //新增的时候不允许不填写;
      const defaultKeysLength = state.activeKey == 'basic' ? 1 : 2;
      if (valuesKeys?.length === defaultKeysLength) {
        createMessage.info('请填写表单');
        return null;
      }
      // const values = await validate();
      const params = {
        ...values,
        annexLitFinMore: values?.annexLitFinMore ? (values?.annexLitFinMore || '').split(',') : undefined,
        annexCarRentalMore: values?.annexCarRentalMore ? (values?.annexCarRentalMore || '').split(',') : undefined,
        annexControllerCreditReportMore: values?.annexControllerCreditReportMore ? (values?.annexControllerCreditReportMore || '').split(',') : undefined,
        annexEquityProxyAgreementMore: values?.annexEquityProxyAgreementMore ? (values?.annexEquityProxyAgreementMore || '').split(',') : undefined,
        annexInventorySalesLedgerMore: values?.annexInventorySalesLedgerMore ? (values?.annexInventorySalesLedgerMore || '').split(',') : undefined,
        annexCorporateAssetsMore: values?.annexCorporateAssetsMore ? (values?.annexCorporateAssetsMore || '').split(',') : undefined,
        annexPersonalAssetsMore: values?.annexPersonalAssetsMore ? (values?.annexPersonalAssetsMore || '').split(',') : undefined,
        annexIdMarriageMore: values?.annexIdMarriageMore ? (values?.annexIdMarriageMore || '').split(',') : undefined,
        annexAdditionalInformation: values?.annexAdditionalInformation ? (values?.annexAdditionalInformation || '').split(',') : undefined,
        annexCompanyRuleMore: values?.annexCompanyRuleMore ? (values?.annexCompanyRuleMore || '').split(',') : undefined,
        annexCreditReportMore: values?.annexCreditReportMore ? (values?.annexCreditReportMore || '').split(',') : undefined,
        annexBusinessCaseMore: values?.annexBusinessCaseMore ? (values?.annexBusinessCaseMore || '').split(',') : undefined,
        annexOrgStructureMore: values?.annexOrgStructureMore ? (values?.annexOrgStructureMore || '').split(',') : undefined,
        annexLeaseAgreementMore: values?.annexLeaseAgreementMore ? (values?.annexLeaseAgreementMore || '').split(',') : undefined,
        annexSitePhotoMore: values?.annexSitePhotoMore ? (values?.annexSitePhotoMore || '').split(',') : undefined,
      };
      delete params.emptyPlaceholder;
      //审核后才编辑，审核前全是保存
      setDrawerProps({ confirmLoading: true });
      await saveOrUpdate(params, state.activeKey);
      // await saveOrUpdate(params, state.record?.auditStatus);
      createMessage.success('保存成功');
      closeDrawer();
      emit('success');
    } catch (err) {
      console.log(err, 'err');
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  };
  const handleSubmitAudit = async () => {
    const { validate } = getFormActive();
    try {
      const values = await validate();
      const params = {
        ...values,
        annexLitFinMore: values?.annexLitFinMore ? (values?.annexLitFinMore || '').split(',') : undefined,
        annexCarRentalMore: values?.annexCarRentalMore ? (values?.annexCarRentalMore || '').split(',') : undefined,
        annexControllerCreditReportMore: values?.annexControllerCreditReportMore ? (values?.annexControllerCreditReportMore || '').split(',') : undefined,
        annexEquityProxyAgreementMore: values?.annexEquityProxyAgreementMore ? (values?.annexEquityProxyAgreementMore || '').split(',') : undefined,
        annexInventorySalesLedgerMore: values?.annexInventorySalesLedgerMore ? (values?.annexInventorySalesLedgerMore || '').split(',') : undefined,
        annexCorporateAssetsMore: values?.annexCorporateAssetsMore ? (values?.annexCorporateAssetsMore || '').split(',') : undefined,
        annexPersonalAssetsMore: values?.annexPersonalAssetsMore ? (values?.annexPersonalAssetsMore || '').split(',') : undefined,
        annexIdMarriageMore: values?.annexIdMarriageMore ? (values?.annexIdMarriageMore || '').split(',') : undefined,
        annexAdditionalInformation: values?.annexAdditionalInformation ? (values?.annexAdditionalInformation || '').split(',') : undefined,
        annexCompanyRuleMore: values?.annexCompanyRuleMore ? (values?.annexCompanyRuleMore || '').split(',') : undefined,
        annexCreditReportMore: values?.annexCreditReportMore ? (values?.annexCreditReportMore || '').split(',') : undefined,
        annexBusinessCaseMore: values?.annexBusinessCaseMore ? (values?.annexBusinessCaseMore || '').split(',') : undefined,
        annexOrgStructureMore: values?.annexOrgStructureMore ? (values?.annexOrgStructureMore || '').split(',') : undefined,
        annexLeaseAgreementMore: values?.annexLeaseAgreementMore ? (values?.annexLeaseAgreementMore || '').split(',') : undefined,
        annexSitePhotoMore: values?.annexSitePhotoMore ? (values?.annexSitePhotoMore || '').split(',') : undefined,
      };
      //审核后才编辑，审核前全是保存
      setDrawerProps({ confirmLoading: true });
      if (handleFormValueIsChange(params)) {
        await saveOrUpdate(params, state.activeKey);
      }
      await submitAudit({ infoId: state?.record?.id || state.createId });
      createMessage.success('提交审核成功');
      closeDrawer();
      emit('success');
    } catch (err) {
      console.log(err, 'err');
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  };
  const handleConfirmAudit = async () => {
    state.activeKey = 'audit';
    const { validate } = getFormActive();
    try {
      const values = await validate();
      setDrawerProps({ confirmLoading: true });
      await confirmAudit(values, () => {
        createMessage.success('审核成功');
        closeDrawer();
        emit('success');
      });
    } catch (err) {
      console.log(err, 'err');
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  };
  // 是否隐藏关联关系切换按钮
  const canSetRelate = computed(() => {
    // 1. 审核通过后，不能修改；2. 审核中，不能修改；3.详情状态不能修改 -- 满足一项就不能修改
    return state.approvedStatus || state.auditing || state.labelKey == 'detail';
  });
  // 设置关联信息 -- 关联和不关联
  function setRelate(value, field,currenValue) {
    // 如果是审核通过后，不支持修改关联信息
    if (canSetRelate.value|| currenValue == value) {
      return null;
    }
    let opt = {}
    if(field === 'relatedSer' ){
      opt = {
        serId: null,
        basicPhone:null,
        basicRealName:null,
        basicSocialCreditCode:null,
      }
      state.currentChannelId = null;
      state.currentChannelInfo = null;
    }
    const { setFieldsValue } = getFormActive();
    setFieldsValue({
      [field]: value,
      ...opt
    });
  }

  const serOptions = ref<any>([]);
  // 获取渠道商列表
  const getSerList = async () => {
    const res = await listSer({ infoId: state.record?.id || null });
    if (res && res.length > 0) {
      serOptions.value = res?.map(item => {
        return {
          ...item,
          label: item.basicName,
          value: item.id,
          basicRegisteredAddress: item.storeAddress,
          basicFullAddress: item.storeFullAddress,
        };
      });
    }
    await refreshSelectOptions('serId', serOptions.value);
    initSerItem();
  };
  // 刷新表单下拉选项
  const refreshSelectOptions = async (field, options) => {
    if (state.activeKey !== 'basic') {
      return null;
    }
    const { updateSchema } = basicFormMethods;
    await updateSchema([
      {
        field,
        componentProps: {
          options,
        },
      },
    ]);
  };
  // 初始化当前选中的渠道商Item
  function initSerItem() {
    if (state.activeKey == 'basic' && state.currentChannelId && serOptions.value.length > 0) {
      const item = serOptions.value.find(item => item.id === state.currentChannelId);
      if (item) {
        state.currentChannelInfo = item;
      }
    }
  }
  const basicFields = ['basicName', 'basicLegalPerson', 'basicLegalPersonCode', 'basicContactNumber', 'basicOpeningTime', 'basicRegisteredAddress', 'basicFullAddress'];
  const financeFields = ['financeInvoicingCompany', 'financeTaxpayerCode', 'financeInvoicingPhone', 'financeBankAccount', 'financeInvoicingAddress', 'financeInvoicingFullAddress', 'financeOpenBank'];
  const annexFields = ['annexBusinessLicense', 'annexLegalPersonId1', 'annexLegalPersonId2'];

  // 用户切换关联渠道商 -- 修改表单默认值
  async function initSerDefaultValue(item, getFieldsValue, setFieldsValue) {
    if (item) {
      state.currentChannelInfo = item;
    }
    const value = getFieldsValue();
    const opt = {};
    let list = basicFields;
    switch (state.activeKey) {
      case 'basic':
        list = basicFields;
        break;
      case 'finance':
        list = financeFields;
        break;
      case 'annex':
        list = annexFields;
        break;
      default:
        return null;
    }
    list.forEach(field => {
      if (!value[field] && item[field]) {
        opt[field] = item[field];
      }
    });
    await setFieldsValue({
      ...opt,
    });
  }
</script>
<style lang="less" scoped>
  .manage-list__edit-drawer {
    div :deep(.ant-picker) {
      width: 100%;
    }
    .emptyPlaceholder {
      width: 100%;
    }
    .map-slot {
      position: relative;
      #container {
        width: 90%;
        height: 400px;
        margin: 0 auto;
        overflow: hidden;
        :deep(img) {
          max-width: inherit;
        }
      }
      #result {
        position: absolute;
        bottom: 10px;
        right: 100px;
        padding: 0px 7px;
        min-width: 350px;
        height: 140px;
        line-height: 28px;
        background: #fff;
        box-shadow: 0 2px 6px 0 rgba(27, 142, 236, 0.5);
        border-radius: 7px;
        z-index: 400;
      }
    }
  }

  .red_start {
    color: #ff4d4f;
    display: inline-block;
    margin-right: 4px;
    font-size: 14px;
  }
</style>
