/**
* @Name useTimePeriod
* @Description：根据当前时间获取当前是什么时间段
* <AUTHOR>
* @Email: <EMAIL>
* @Date: 2023/9/7 15:58
* @FilePath: src\hooks\web\useTimePeriod.ts
*/

import { ref } from 'vue';
import dayjs, { Dayjs } from 'dayjs';

export enum TimePeriod {
  EarlyMorning = 'EarlyMorning',
  Morning = 'Morning',
  LateMorning = 'LateMorning',
  Noon = 'Noon',
  Afternoon = 'Afternoon',
  Evening = 'Evening',
}

interface TimePeriodStrategy {
  (now: Dayjs): boolean;
}

const timePeriodStrategies: Record<TimePeriod, TimePeriodStrategy> = {
  [TimePeriod.EarlyMorning]: (now: Dayjs) => now.hour() >= 0 && now.hour() < 6,
  [TimePeriod.Morning]: (now: Dayjs) => now.hour() >= 6 && now.hour() < 9,
  [TimePeriod.LateMorning]: (now: Dayjs) => now.hour() >= 9 && now.hour() < 12,
  [TimePeriod.Noon]: (now: Dayjs) => now.hour() >= 12 && now.hour() < 14,
  [TimePeriod.Afternoon]: (now: Dayjs) => now.hour() >= 14 && now.hour() < 18,
  [TimePeriod.Evening]: (now: Dayjs) => now.hour() >= 18 && now.hour() < 24,
};

export default function useTimePeriod() {
  const timePeriod = ref<TimePeriod>(TimePeriod.EarlyMorning);
  function getTimePeriod() {
    const now: Dayjs = dayjs();
    for (const period in timePeriodStrategies) {
      if (timePeriodStrategies[period](now)) {
        timePeriod.value = period as TimePeriod;
        break;
      }
    }
  }
  getTimePeriod();
  return {
    timePeriod,
  };
}
