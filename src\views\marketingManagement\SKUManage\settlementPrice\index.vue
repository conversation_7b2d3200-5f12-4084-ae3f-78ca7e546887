<template>
  <div>
    <BasicTable @register="registerTable">
      <template #form-treeSelect="{ model, field }">
        <a-tree-select
          v-model:value="model[field]"
          show-search
          style="width: 100%"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          placeholder="请选择类目"
          allow-clear
          :tree-data="treeData"
          tree-node-filter-prop="value"
        >
          <template #title="{ value, label }">
            {{ `${value}${label}` }}
          </template>
        </a-tree-select>
      </template>
      <template #recordStatus="{ record }">
        <template v-if="[0, 1].includes(record.status)">
          <StateBox :text="statusObj[record.status].text" :stateBC="statusObj[record.status].stateBC"></StateBox>
        </template>
        <template v-else>
          {{ record.status }}
        </template>
      </template>
      <!-- <template #SKUbianma="{ record }">
        <a-button type="link" @click="handleEdit(null, OpenDrawerType.detail)">{{ record.SKUbianma }}</a-button>
      </template> -->
      <!-- 配件属性 -->
      <template #accessoryType="{ record }">
        {{ record.accessoryType }}
      </template>
      <template #tableTitle>
        <!-- 导入 -->
        <a-button type="primary" @click="handleImport">批量新增</a-button>
        <a-button type="primary" @click="handleExportXLS">导出</a-button>
      </template>
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
    <ProjectDrawer @register="registerDrawer" @success="handleSuccess" />
    <ImportDrawer @register="registerImportDrawer" @success="handleSuccess"></ImportDrawer>
  </div>
</template>
<script lang="ts" setup name="marketingManagement-attributeManagement-pool">
  import { list, downloadXls, deleteItemById } from './index.api';
  import ProjectDrawer from './projectDrawer.vue';
  import ImportDrawer from './ImportDrawer.vue';
  import { columns, searchFormSchema, OpenDrawerType } from './index.data';
  import { ActionItem, BasicTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useDrawer } from '/@/components/Drawer';
  import { Modal } from 'ant-design-vue';
  import StateBox from '@/components/common/StateBox.vue';
  import { message } from 'ant-design-vue';

  const statusObj = {
    '0': {
      text: '禁用',
      stateBC: '#f59b25',
    },
    '1': {
      text: '正常',
      stateBC: '#2cdc9c',
    },
  };

  // #region 搜索表单
  import type { TreeSelectProps } from 'ant-design-vue';
  import { ref } from 'vue';

  const treeData = ref<TreeSelectProps['treeData']>([
    {
      label: 'parent 1',
      value: 'xx',
      disabled: true,
      children: [
        {
          label: 'parent 1-0',
          value: 'xx1',
        },
        {
          label: 'parent 1-1',
          value: 'xx2',
        },
      ],
    },
  ]);
  // #endregion
  // #region 表格
  const { tableContext } = useListPage({
    tableProps: {
      title: '',
      showIndexColumn: true,
      api: list,
      columns: columns,
      size: 'small',
      formConfig: {
        schemas: searchFormSchema,
      },
      actionColumn: {
        width: 220,
        fixed: 'right',
      },
    },
  });
  //注册table数据
  const [registerTable, { reload, getForm }] = tableContext;

  /**
   *@description 表格操作
   */
  function getTableAction(record): ActionItem[] {
    return [
      {
        auth: 'market:pool:edit',
        label: '修改价格',
        onClick: handleEdit.bind(null, record, OpenDrawerType.edit),
      },
      {
        auth: 'market:pool:delete',
        label: '历史价格',
        onClick: handleEdit.bind(null, record, OpenDrawerType.detail),
      },
    ];
  }
  // 导出
  async function handleExportXLS() {
    const { validate } = getForm();
    const params = await validate();
    downloadXls(params);
  }

  /**
   * 导入
   */
  function handleImport() {
    openImportDrawer(true, {
      isUpdate: false,
      showFooter: true,
      tenantSaas: false,
    });
  }
  // #endregion 表格

  // #region 弹框 -- 编辑、新增
  const [registerDrawer, { openDrawer }] = useDrawer();
  /**
   *@description 编辑事件
   */
  function handleEdit(record, openType: OpenDrawerType) {
    openDrawer(true, {
      record,
      isUpdate: true,
      showFooter: true,
      openType,
    });
  }

  /**
   *@description 成功回调
   */
  function handleSuccess() {
    reload();
  }
  // #endregion
  // #region 弹框 -- 批量新增
  const [registerImportDrawer, { openDrawer: openImportDrawer }] = useDrawer();
  // #endregion
</script>
<style lang="less"></style>
