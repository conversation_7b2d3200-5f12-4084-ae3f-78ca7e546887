export enum FormTypeEnum {

    input = "0",
    select = "1",
}

export enum InputTypeEnum {
    single = "0",
    multiple = "1",
}

export enum IsMustEnum {
    yes = "1",
    no = "0",
}



export enum CarProductStatusEnum {
    hasDrafted = "0",//已起草
    noPutaway = "1",//未上架
    hasPutawayed = "2",//已上架

}

export enum MarketProductTypeEnum {
    carProduct = "1",//车辆产品
    serviceProduct = "2", //服务产品
    combinationProduct = "3", //组合产品
}

export enum ProductTypeEnum {
    carProduct = 1,//车辆产品
    serviceProduct = 2, //服务产品

}
