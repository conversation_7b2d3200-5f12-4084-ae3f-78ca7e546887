import { BasicColumn, FormSchema } from '@/components/Table';
import dayjs from 'dayjs';
import { DescItem } from '@/components/Description';
import { supplierListApi } from './index.api';

function customValidator(value, maxlength) {
  if (value?.trim()?.length > 0 && value?.trim()?.length < maxlength) {
    return Promise.reject();
  }
  return Promise.resolve();
}
const withEmptyCellHandler = columns => {
  return columns.map(col => {
    if (!col.customRender) {
      col.customRender = ({ text }: any) => {
        return text !== null ? text : '-';
      };
    }
    return col;
  });
};

const indexColumns: BasicColumn[] = [
  {
    title: '车牌号',
    dataIndex: 'carPlate',
    key: 'carPlate',
    width: 150,
    fixed: 'left',
  },
  {
    title: '车架号',
    dataIndex: 'vin',
    key: 'vin',
    fixed: 'left',
    width: 210,
  },
  {
    title: '车辆品牌',
    dataIndex: 'brand',
    key: 'brand',
    width: 150,
  },
  {
    title: '车辆型号',
    dataIndex: 'model',
    key: 'model',
    width: 150,
  },
  {
    title: 'GPS供应商',
    dataIndex: 'supplierName',
    key: 'supplierName',
    width: 200,
  },
  {
    title: '最后位置',
    dataIndex: 'latestAddress',
  },
  {
    title: '运营城市',
    dataIndex: 'operationCity',
    width: 150,
  },
  {
    title: '运营归属',
    dataIndex: 'operationOwnership',
  },
  {
    title: 'GPS接入时间',
    dataIndex: 'installationTime',
    width: 200,
  },
  {
    title: '定位时间',
    dataIndex: 'positioningTime',
    width: 200,
  },
];
export const columns = withEmptyCellHandler(indexColumns);
export const searchFormSchema: FormSchema[] = [
  {
    label: '车牌号',
    labelWidth: 90,
    field: 'carPlateList1',
    component: 'JIsolateInputModal',
    colProps: { span: 7 },
    componentProps: ({ formActionType }) => {
      return {
        textareaLabel: '车牌号', // 弹出框标题
        textareaBindField: 'carPlateList', //  绑定的查询字段
        formActionType, // 获取form的action
      };
    },
    rules: [
      {
        message: '车牌号模糊搜索至少输入3个字符',
        trigger: 'blur',
        validator: (_rule, value) => customValidator(value, 3),
      },
    ],
  },
  {
    label: '车架号',
    labelWidth: 90,
    field: 'vinList1',
    component: 'JIsolateInputModal',
    colProps: { span: 7 },
    componentProps: ({ formActionType }) => {
      return {
        textareaLabel: '车架号', // 弹出框标题
        textareaBindField: 'vinList', //  绑定的查询字段
        formActionType, // 获取form的action
      };
    },
    rules: [
      {
        message: '车架号模糊搜索至少输入6个字符',
        trigger: 'blur',
        validator: (_rule, value) => customValidator(value, 6),
      },
    ],
  },
  {
    label: 'GPS供应商',
    field: 'supllierId',
    component: 'ApiSelect',
    colProps: { span: 7 },
    componentProps: {
      api: supplierListApi,
      labelField: 'name',
      valueField: 'id',
      resultField: 'records',
      immediate: false,
      placeholder: '请选择GPS供应商',
      showSearch: true,
    },
  },
  {
    field: 'quickSearchFirst',
    isQuickSearch: true,
    componentList: [
      {
        label: '',
        field: 'brandId',
        hideLabel: true,
        component: 'JSelectInputText',
        componentProps: {
          options: [],
          placeholder: '车辆品牌',
          showSearch: false,
        },
        colProps: {
          style: { marginLeft: '-11px' },
          span: 2,
        },
      },
      {
        label: '',
        hideLabel: true,
        field: 'modelId',
        component: 'JSelectInputText',
        componentProps: {
          options: [],
          placeholder: '车辆型号',
          showSearch: false,
        },
        colProps: {
          span: 2,
        },
      },
      {
        label: '',
        hideLabel: true,
        field: 'operationCityCode',
        component: 'CityDropdownSelectText',
        componentProps: {
          isAreaSelect: true,
          showArea: false,
          placeholder: '运营城市',
        },
        colProps: { span: 2 },
      },
      {
        label: '',
        hideLabel: true,
        field: 'operationOwnershipId',
        component: 'JSelectInputText',
        componentProps: {
          options: [],
          placeholder: '运营归属',
          showSearch: false,
        },
        colProps: {
          span: 2,
        },
      },
    ],
  },
  {
    field: 'quickSearchSecond',
    isQuickSearch: true,
    componentList: [
      {
        label: 'GPS接入时间',
        labelWidth: 100,
        field: 'installationTime',
        component: 'RangePicker',
        componentProps: {
          //是否显示时间
          // showTime: true,
          //日期格式化
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          //范围文本描述用集合
          // placeholder: ['请选择日期范围', ''],
          style: { width: '100%' },
        },
        colProps: { span: 7 },
      },
      {
        label: '定位时间',
        labelWidth: 100,
        field: 'positioningTime',
        component: 'RangePicker',
        componentProps: ({ formModel }) => {
          return {
            format: 'YYYY-MM-DD',
            valueFormat: 'YYYY-MM-DD',
            style: { width: '100%' },
            // 初始化默认值：结束日期 = 今天，开始日期 = 今天 - 89 天

            disabledDate: current => {
              if (!current) return false;

              const [s, e] = formModel.positioningTime || [];
              const start = s ? dayjs(s) : null;
              const end = e ? dayjs(e) : null;

              // 双方都有 -> 保证 [s, e] ≤ 90天，并允许前后浮动
              if (start && end) {
                const diff = end.diff(start, 'day');
                if (diff > 90) {
                  // 超过 90 天禁止
                  return true;
                }
                const extra = 90 - diff;
                const min = start.subtract(extra, 'day').startOf('day');
                const max = end.add(extra, 'day').endOf('day');
                return current.isBefore(min) || current.isAfter(max);
              }

              // 只有开始 -> 限制 [start, start+90]
              if (start && !end) {
                const max = start.add(90, 'day').endOf('day');
                return current.isBefore(start.startOf('day')) || current.isAfter(max);
              }

              // 只有结束 -> 限制 [end-90, end]
              if (!start && end) {
                const min = end.subtract(90, 'day').startOf('day');
                return current.isBefore(min) || current.isAfter(end.endOf('day'));
              }

              return false;
            },

            // 选中回调，用于动态记录正在选的范围（可选）
            onCalendarChange: (dates, dateStrings, info) => {
              // info.range: "start" | "end"
              formModel.positioningTime = dates ?? [];
            },
          };
        },
        colProps: { span: 7 },
      },
    ],
  },
];
export const detailFormSchema: FormSchema[] = [
  {
    label: '车牌号',
    field: 'carPlate',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '车架号',
    field: 'vin',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '定位数据源',
    field: 'supplierId',
    labelWidth: 100,
    component: 'Select',
    colProps: { span: 6 },
  },
  // {
  //   label: '定位数据源',
  //   field: 'supplierId',
  //   labelWidth: 100,
  //   component: 'ApiSelect',
  //   componentProps: {
  //     api: getSupplierList,
  //     labelField: 'name',
  //     valueField: 'id',
  //     immediate: false,
  //     showSearch: true,
  //     allowClear: false,
  //   },
  //   colProps: { span: 6 },
  // },
];
export const detailDescirptSchema: DescItem[] = [
  {
    field: 'vin',
    label: '车架号',
    render: val => val ?? '-',
  },
  {
    field: 'brand',
    label: '车辆品牌',
    render: val => val ?? '-',
  },
  {
    field: 'model',
    label: '车辆型号',
    render: val => val ?? '-',
  },
  {
    field: 'latitude',
    label: '经度',
    render: val => val ?? '-',
  },
  {
    field: 'longitude',
    label: '纬度',
    render: val => val ?? '-',
  },
  {
    field: 'latestAddress',
    label: '地址',
    render: val => val ?? '-',
  },
  {
    field: 'positioningTime',
    label: '最新定位时间',
    render: val => val ?? '-',
  },
];
