<template>
  <div>
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <!-- v-auth="'market:pool:add'" -->
        <a-button v-if="hasPermission(`market:category:add`)" type="primary" @click="handleEdit(null)">新增类目</a-button>
      </template>
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
    <drawer @register="registerDrawer" @success="handleSuccess" />
  </div>
</template>
<script lang="ts" setup name="marketingManagement-attributeManagement-pool">
  import { list, deleteItemById, deleteSPUById } from './index.api';
  import drawer from './drawer.vue';
  import { columns, CATEGORY_TYPE, searchFormSchema } from './index.data';
  import { ActionItem, BasicTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useDrawer } from '/@/components/Drawer';
  import { Modal } from 'ant-design-vue';
  import { message } from 'ant-design-vue';
  import { usePermission } from '/@/hooks/web/usePermission';
  const { hasPermission } = usePermission();

  const childrenColumnName = 'spuList';
  // #region 表格
  const { tableContext } = useListPage({
    tableProps: {
      title: '',
      showIndexColumn: true,
      pagination: false,
      expandIconColumnIndex: 1,
      api: list,
      childrenColumnName,
      columns: columns,
      size: 'small',
      formConfig: {
        schemas: searchFormSchema,
      },
      actionColumn: {
        width: 200,
        fixed: 'right',
      },
      afterFetch: async (res: any) => {
        if (res.length > 0) {
          res.forEach(item => {
            item.id = item.categoryId;
          });
        }
        return res;
      },
    },
  });
  //注册table数据
  const [registerTable, { reload }] = tableContext;

  /**
   *@description 删除事件
   */
  async function handleDelete(record) {
    if (record.spuName || record.categoryTwoName) {
      await deleteSPUById({ id: record.id }, reload);
    } else {
      await deleteItemById({ id: record.id }, reload);
    }
  }
  function handleDeleteCheck(record) {
    if (record && record[childrenColumnName]?.length > 0) {
      // 否则提示请先删除二级类目。
      message.warning('请先删除二级类目');
      return;
    }
    Modal.confirm({
      title: '温馨提示',
      content: '删除后无法恢复，请确认是否继续删除操作？',
      onOk() {
        handleDelete(record);
      },
    });
  }
  /**
   *@description 表格操作
   */
  function getTableAction(record): ActionItem[] {
    return [
      {
        auth: 'market:pool:edit',
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        ifShow: hasPermission(`market:category:update`),
      },
      {
        auth: 'market:pool:delete',
        label: '删除',
        onClick: handleDeleteCheck.bind(null, record),
        ifShow: hasPermission(`market:category:delete`),
      },
    ];
  }
  // #endregion 表格

  // #region 弹框
  const [registerDrawer, { openDrawer }] = useDrawer();
  /**
   *@description 编辑事件
   */
  function handleEdit(record) {
    console.log('🚀 ~ handleEdit ~ record:', record);
    openDrawer(true, {
      record,
      menuType: record?.categoryTwoName ? CATEGORY_TYPE.LEVEL_2 : CATEGORY_TYPE.LEVEL_1,
      isUpdate: record ? true : false,
      showFooter: true,
    });
  }

  /**
   *@description 成功回调
   */
  function handleSuccess() {
    reload();
  }
  // #endregion
</script>
<style lang="less"></style>
