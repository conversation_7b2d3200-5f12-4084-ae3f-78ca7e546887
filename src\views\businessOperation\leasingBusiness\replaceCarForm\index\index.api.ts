import { defHttp } from '/@/utils/http/axios';

enum Api {
  list = '/boReplaceOrder/list',
  changeStatus = '/boReplaceOrder/changeStatus',
  batchReturn = '/boReplaceOrder/return',
  reasonList = '/bo/returnReason/all',
  reminder = '/bo/vehiclePickupTask/reminder',
  reassignment = '/bo/vehiclePickupTask/reassignment',
  station = '/veh/asset/allStation',
  exportPdf = '/boReplaceOrder/exportPdf',
  submitApproval = '/boReplaceOrder/submitApproval',
  personList = '/system/sysStation/getUserListByStationId', // 根据场站id查询员工
}

// 导出接口
export const exportXlsUrl = '/boReplaceOrder/export';
// 导出pdf接口
export const exportPdfUrl = Api.exportPdf;
/**
 * 列表接口
 * @param params
 */
export const list = params => defHttp.get({ url: Api.list, params });

/**
 * 提车任务单-状态流转
 * @param params
 */
export const statusChange = params => {
  return defHttp.get({ url: Api.changeStatus, params });
};

/**
 * 退车原因列表
 * @param params
 */
export const getReasonList = params => defHttp.get({ url: Api.reasonList, params });

/**
 * 退车
 * @param params
 */
export const handleReturnBack = params => defHttp.post({ url: Api.batchReturn, params });

/**
 * 催单
 * @param params
 */
export const handleSendReminder = params => defHttp.post({ url: Api.reminder, params });

/**
 * 改派
 * @param params
 */
export const handleSendReassignment = params => defHttp.post({ url: Api.reassignment, params });


export const getStations = () => defHttp.get({ url: Api.station });

/**
 * 替换车单-钉钉提审
 * @param params
 */
export const submitDingTalkApproval = params => defHttp.post({ url: Api.submitApproval, params });
/**
 * 根据场站id查询员工
 * @param params
 */
export const getPersonByStationId = params => defHttp.get({ url: Api.personList, params });