<template>
  <!-- 工单-验车信息 -->
  <div class="info-wrapper">
    <div class="info-wrapper__top">
      <div class="info-wrapper__top-item">
        <div class="info-wrapper__top-item__label">工单编号:</div>
        <div class="info-wrapper__top-item__value">
          {{ formData.orderCode || formData?.workOrder?.orderCode || '-' }}
        </div>
      </div>
      <div class="info-wrapper__top-item">
        <div class="info-wrapper__top-item__label">工单状态:</div>
        <div class="info-wrapper__top-item__value">{{ formData?.workOrder?.workOrderStatus_dictText || '-' }}</div>
      </div>
      <div class="info-wrapper__top-item">
        <div class="info-wrapper__top-item__label">处理时长:</div>
        <div class="info-wrapper__top-item__value">{{ formData.duration || '-' }}</div>
      </div>
    </div>
    <a-form ref="pickupApplicationFormRef" name="pickupApplicationForm" :model="newFormData" :rules="rules" autocomplete="off" class="box-steps-content">
      <div class="info-wrapper__item">
        <div class="info-wrapper__item-title">验车信息</div>
        <div v-for="(item, key) in labelMap" :key="key" class="info-wrapper__item-content m-b-24px">
          <div class="info-wrapper__item-content__label">{{ item }}：</div>
          <div class="info-wrapper__item-content__value">
            <span> {{ newFormData[key] }} </span>
          </div>
        </div>
        <template v-if="!detail">
          <div class="info-wrapper__item-content label-w-80" style="padding-right: 20px">
            <a-form-item label="车牌号" name="carPlate">
              <a-select
                v-model:value="newFormData.vehAssetId"
                showSearch
                allowClear
                placeholder="请选择车牌号"
                @change="
                  (carPlate: string) => {
                    handleCarChange(carPlate, 'id');
                  }
                "
                :filterOption="handleFilterOption"
              >
                <a-select-option v-for="item in carPlateList" :value="item.id" :key="item.id">
                  {{ item.carPlate }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </div>
          <div class="info-wrapper__item-content label-w-80" style="padding-right: 20px">
            <a-form-item label="车架号" name="vin">
              <a-select
                v-model:value="newFormData.vin"
                showSearch
                allowClear
                placeholder="请选择车架号"
                @change="
                  (vin: string) => {
                    handleCarChange(vin, 'vin');
                  }
                "
                :filterOption="handleFilterOptionVin"
              >
                <a-select-option v-for="item in carPlateList" :value="item.vin" :key="item.vin">
                  {{ item.vin }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </div>
          <div class="info-wrapper__item-content label-w-80" style="padding-right: 20px">
            <a-form-item label="车身颜色" name="vehicleColor">
              <a-input v-model:value="newFormData.vehicleColor" placeholder="请输入车身颜色" disabled />
            </a-form-item>
          </div>
          <div class="info-wrapper__item-content label-w-80" style="padding-right: 20px">
            <a-form-item label="货箱类型" name="vehicleColor">
              <a-input v-model:value="newFormData.crates" placeholder="请输入车身颜色" disabled />
            </a-form-item>
          </div>
        </template>
        <template v-else>
          <div class="info-wrapper__item-content">
            <div class="info-wrapper__item-content__label">车牌号：</div>
            <div class="info-wrapper__item-content__value">
              <span> {{ newFormData.carPlate }}</span>
            </div>
          </div>
          <div class="info-wrapper__item-content">
            <div class="info-wrapper__item-content__label">车架号：</div>
            <div class="info-wrapper__item-content__value">
              <span> {{ newFormData.vin }}</span>
            </div>
          </div>
          <div class="info-wrapper__item-content">
            <div class="info-wrapper__item-content__label">车身颜色：</div>
            <div class="info-wrapper__item-content__value">
              <span> {{ newFormData.vehicleColor }}</span>
            </div>
          </div>
          <div class="info-wrapper__item-content">
            <div class="info-wrapper__item-content__label">货箱类型：</div>
            <div class="info-wrapper__item-content__value">
              <span> {{ newFormData.crates }}</span>
            </div>
          </div>
        </template>
      </div>
      <div class="info-wrapper__item">
        <div class="info-wrapper__item-title">验车项目</div>
        <div class="info-wrapper__item-content label-w-100" v-for="(item, index) in newFormData.vehicleInspectionItems" :key="item.id">
          <a-form-item
            v-if="!detail"
            :label="`${item.itemName}：`"
            :name="['vehicleInspectionItems', index, 'itemValue']"
            :rules="{
              required: true,
              message: `${item.itemName}不能为空`,
              trigger: 'change',
            }"
          >
            <a-radio-group v-model:value="item.itemValue" name="vehicleInspectionProject">
              <a-radio :value="1">有</a-radio>
              <a-radio :value="0">无</a-radio>
            </a-radio-group>
          </a-form-item>
          <template v-else>
            <div class="info-wrapper__item-content__label">{{ item.itemName }}：</div>
            <div class="info-wrapper__item-content__value">
              <img v-if="item.itemValue === 1" src="@/assets/images/has.jpg" alt="" />
              <img v-else src="@/assets/images/no.jpg" alt="" />
            </div>
          </template>
        </div>
      </div>
      <div class="info-wrapper__item">
        <div class="info-wrapper__item-title img-title">外观检查记录</div>
        <div class="info-wrapper__item-content wgjl-box" v-for="(item, key) in visualCheckRecords" :key="key">
          <a-form-item v-if="!detail" :label-col="{ span: 0 }" :name="key" htmlFor="file">
            <JImageUpload text="图片上传" bizPath="/bo/pickupApplication/workOrder" v-model:value="newFormData[key]" :file-max="1" :file-size="30" />
            <div class="ant-form-item-label m-t--15px">
              <label class="ant-form-item-required">{{ item }}</label>
              <BasicHelp placement="top" class="mx-1" text="提示：上传文件大小在30M以内，支持图片格式" />
            </div>
          </a-form-item>
          <template v-else>
            <div class="flex flex-col">
              <div class="info-wrapper__item-content__value">
                <img
                  v-if="newFormData[key]"
                  :src="newFormData[key]"
                  :alt="item"
                  class="image"
                  @click="
                    () => {
                      handlePreview([newFormData[key]], 0);
                    }
                  "
                />
                <span v-else>-</span>
              </div>
              <div class="info-wrapper__item-content__label">{{ item }}：</div>
            </div>
          </template>
        </div>
      </div>
      <div v-if="!detail" class="info-wrapper__item">
        <div class="info-wrapper__item-content label-w-100" style="padding-right: 20px">
          <a-form-item label="车辆里程(km)" name="vehicleMileage">
            <a-input-number :min="0" :max="1000000000" v-model:value="newFormData.vehicleMileage" style="width: 100%" :precision="2" placeholder="请输入车辆里程" />
          </a-form-item>
        </div>
        <div class="info-wrapper__item-content label-w-100" style="padding-right: 20px">
          <a-form-item label="电池电量(%)" name="batteryLevel">
            <a-input-number :min="0" :max="100" v-model:value="newFormData.batteryLevel" style="width: 100%" :precision="0" placeholder="请输入电池电量" />
          </a-form-item>
        </div>
      </div>
      <div v-else class="info-wrapper__item">
        <div class="info-wrapper__item-content">
          <div class="info-wrapper__item-content__label">车辆里程(km)：</div>
          <div class="info-wrapper__item-content__value">
            <span> {{ newFormData.vehicleMileage }}</span>
          </div>
        </div>
        <div class="info-wrapper__item-content">
          <div class="info-wrapper__item-content__label">电池电量(%)：</div>
          <div class="info-wrapper__item-content__value">
            <span> {{ newFormData.batteryLevel }}</span>
          </div>
        </div>
      </div>
    </a-form>
    <div v-if="!detail" class="m-t-20px flex items-center">
      <a-button class="min-w-100" @click="handleCancel" :loading="btnLoading">取消</a-button>
      <a-button class="min-w-100" @click="handleSave" type="primary" ghost :loading="btnLoading">暂存</a-button>
      <a-button class="min-w-100" @click="handleSubmit" type="primary" :loading="btnLoading">提交</a-button>
    </div>
  </div>
</template>

<script lang="ts" name="base-info" setup>
  import type { FormInstance } from 'ant-design-vue';
  import type { Rule } from 'ant-design-vue/es/form';
  import { ref, watch } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { getCarList, getTempVins, saveOrderInfo, saveOrderTemp } from './index.api';
  import BasicHelp from '/@/components/Basic/src/BasicHelp.vue';
  import JImageUpload from '/@/components/Form/src/jeecg/components/JImageUpload.vue';
  import { createImgPreview } from '/@/components/Preview/index';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { useMultipleTabStore } from '/@/store/modules/multipleTab';
  import { EContractType } from '/@/views/businessOperation/leasingBusiness/leaseContract/index/index.api';

  const { createMessage } = useMessage();

  const labelMap = {
    brandModel: '品牌型号',
    customerName: '客户姓名',
    pickupTime: '提车时间',
    customerPhone: '客户电话',
    pickupStation: '交付场站',
  };

  // 外观检查记录
  const visualCheckRecords = {
    frontBody: '车身正面',
    rearBody: '车身背面',
    leftSideBody: '车身左侧',
    rightSideBody: '车身右侧',
    topBody: '车身顶部',
    batteryPack: '电池包',
    tbox: '仪表盘照片',
    handoverPhotos: '车辆确认交接单照片',
  };

  const props = defineProps<{
    id: string; // 工单id
    formData: any;
    myContractType: EContractType; // 租赁合同/以租代售合同/展示车合同
    detail?: boolean; // 是否是详情
  }>();
  const carPlateList = ref<any[]>([]); // 车牌号列表
  const pickupApplicationFormRef = ref<FormInstance>();
  const hasBrandAndModel = ref(false); // 是否有品牌型号
  const newFormData = ref<any>({
    id: '',
    orderCode: '',
    workOrderStatus_dictText: '',
    brandModel: '',
    customerName: '',
    pickupTime: '',
    carPlate: '',
    vehAssetId: '',
    customerPhone: '',
    pickupStation: '',
    stationId: '',
    vin: '',
    vehicleColor: '',
    frontBody: '',
    leftSideBody: '',
    topBody: '',
    rearBody: '',
    rightSideBody: '',
    handoverPhotos: '',
    tbox: '',
    batteryPack: '',
    crates: '',
    vehicleMileage: '',
    batteryLevel: '',
    // 验车项目
    vehicleInspectionItems: [],
  });
  const rules: Record<string, (Rule & { messageInfo?: string })[]> = {
    vin: [{ required: true, message: '请选择车架号', trigger: 'change' }],
    vehicleColor: [{ required: true, message: '请输入车身颜色', trigger: 'blur' }],
    frontBody: [{ required: true, message: '请选择正面照片', trigger: 'change' }],
    rearBody: [{ required: true, message: '请选择背面照片', trigger: 'change' }],
    leftSideBody: [{ required: true, message: '请选择车身左侧照片', trigger: 'change' }],
    rightSideBody: [{ required: true, message: '请选择车身右侧照片', trigger: 'change' }],
    topBody: [{ required: true, message: '请选择车身顶部照片', trigger: 'change' }],
    batteryPack: [{ required: true, message: '请选择电池包', trigger: 'change' }],
    tbox: [{ required: true, message: '请选择仪表盘照片', trigger: 'change' }],
    handoverPhotos: [{ required: true, message: '请选择车辆确认交接单照片', trigger: 'change' }],
    vehicleMileage: [{ required: true, message: '请输入车辆里程', trigger: 'blur' }],
    batteryLevel: [{ required: true, message: '请输入电池电量', trigger: 'blur' }],
  }; // 表单验证规则

  // 数据过滤
  const handleFilterOption = (input, option) => {
    const label = carPlateList.value.find(item => item.id === option.value)?.carPlate || '';
    return label.toLowerCase().includes(input.toLowerCase());
  };

  /**
   * 下拉选择改变，根据选择的配置的车牌号获取对应的车架号，车身颜色
   * @param val 选中的车架号/车牌号
   * @param attr 根据此属性来获取当前选中的数据信息
   */
  const handleCarChange = (val: string, attr: string) => {
    const chooseCar: any = carPlateList.value.find(item => item[attr] === val) || {};
    newFormData.value.vehAssetId = chooseCar?.id; // 资产id
    newFormData.value.carPlate = chooseCar?.carPlate; // 车牌号
    newFormData.value.vin = chooseCar?.vin; // 车架号
    newFormData.value.vehicleColor = chooseCar?.vehicleColor_dictText; // 车身颜色
    newFormData.value.crates = chooseCar?.crates_dictText; // 货箱类型
    if (!hasBrandAndModel.value) {
      // 品牌型号没有值，则按照选择的车辆信息来
      newFormData.value.brandModel = `${chooseCar?.brand}-${chooseCar?.model}`;
    }
    // 校验车身颜色是否为空
    pickupApplicationFormRef.value?.validateFields(['vin', 'vehicleColor']);
  };
  // 车架号数据过滤
  const handleFilterOptionVin = (input, option) => {
    const label = carPlateList.value.find(item => item.vin === option.value)?.vin || '';
    return label.toLowerCase().includes(input.toLowerCase());
  };
  watch(
    () => props.formData,
    val => {
      if (val && val.id) {
        for (let key in newFormData.value) {
          if (newFormData.value.hasOwnProperty(key)) {
            newFormData.value[key] =
              val?.workOrder?.[key] || val?.workOrder?.[key] === 0 ? `${val?.workOrder?.[key]}` : newFormData.value[key] || (key === 'vehAssetId' || key === 'vin' ? undefined : '');
            // 设置验车项目
            if (key === 'vehicleInspectionItems' && typeof newFormData.value.vehicleInspectionItems === 'string') {
              newFormData.value.vehicleInspectionItems = JSON.parse(val.workOrder.vehicleInspectionItems || '[]');
            }
          }
        }

        hasBrandAndModel.value = newFormData.value.brandModel !== '-';
        if (!props.detail) {
          // 先查询暂存的车架号，在查询车牌号
          getTempVins({ id: newFormData.value.id }).then(res => {
            // 根据品牌，型号查询车牌号
            getCarList({
              brandModel: hasBrandAndModel.value ? newFormData.value.brandModel : '',
              crates: newFormData.value.crates,
              stationId: newFormData.value.stationId,
              vinStr: res,
            }).then(res => {
              carPlateList.value = res;
            });
          });
        }
      }
    },
    {
      immediate: true,
      deep: true,
    }
  );

  // 路由前缀映射
  const routePathMap = {
    [`${EContractType.LEASE_CONTRACT}`]: 'leasingBusiness',
    [`${EContractType.SHOW_CAR_CONTRACT}`]: 'showCarBusiness',
    [`${EContractType.LEASE_PURCHASE_CONTRACT}`]: 'leasePurchase',
  };

  // 提交
  const btnLoading = ref<boolean>(false);
  // 取消
  const router = useRouter();
  const route = useRoute();
  const tabStore = useMultipleTabStore(); // 删除当前标签
  const handleCancel = () => {
    if (!props.formData.replaceOrderNo) {
      const path: string = `/businessOperation/${routePathMap[props.myContractType]}/pickupTaskForm`;
      const id: string = route.params.id as string;
      router.replace({ path }).then(() => {
        // 关闭新增/编辑页面
        const url: string = `/businessOperation/leasingBusiness/pickupTaskForm/edit/${props.myContractType}/${id}`;
        tabStore.closeTabByKey(url, router);
      });
    } else {
      const path: string = `/businessOperation/${routePathMap[props.myContractType]}/replaceCarForm`;
      const id: string = route.params.id as string;
      router.replace({ path }).then(() => {
        // 关闭新增/编辑页面
        const url: string = `/businessOperation/leasingBusiness/replaceCarForm/edit/${props.myContractType}/${id}`;
        tabStore.closeTabByKey(url, router);
      });
    }
  };
  // 提交
  const handleSubmit = () => {
    pickupApplicationFormRef.value?.validate().then(valid => {
      if (!valid.errorFields) {
        btnLoading.value = true;
        let saveData = JSON.parse(JSON.stringify(newFormData.value));
        // 设置验车项目转字符串
        saveData.vehicleInspectionItems = JSON.stringify(saveData.vehicleInspectionItems);
        saveOrderInfo(saveData)
          .then(() => {
            createMessage.success('提交成功');
            handleCancel();
          })
          .catch(() => {
            btnLoading.value = false;
          });
      }
    });
  };
  // 暂存
  const handleSave = () => {
    btnLoading.value = true;
    let saveData = JSON.parse(JSON.stringify(newFormData.value));
    // 设置验车项目转字符串
    saveData.vehicleInspectionItems = JSON.stringify(saveData.vehicleInspectionItems);
    saveOrderTemp(saveData)
      .then(() => {
        createMessage.success('暂存成功');
        handleCancel();
      })
      .catch(() => {
        btnLoading.value = false;
      });
  };

  // 图片预览
  function handlePreview(images, index) {
    createImgPreview({ imageList: images, defaultWidth: 700, rememberState: true, index, maskClosable: true });
  }
</script>
<style lang="less" scoped>
  .info-wrapper {
    &__top {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      width: 100%;
      margin-bottom: 20px;

      &-item {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        font-size: 16px;
        flex: 0 0 380px;

        &__label {
          color: #000;
          font-weight: 600;
          flex: 0 0 100px;
        }

        &__value {
          color: rgb(102 102 102);
        }
      }
    }

    &__item {
      display: flex;
      justify-content: flex-start;
      margin-bottom: 15px;
      flex-wrap: wrap;

      &-title {
        font-size: 14px;
        color: #000;
        font-weight: 600;
        margin-bottom: 8px;
        flex: 0 0 100%;

        &.img-title {
          margin-bottom: 15px;
        }
      }

      &-content {
        display: flex;
        justify-content: flex-start;
        color: #333;
        flex: 0 0 380px;
        line-height: 32px;

        &.img-flex {
          align-items: flex-start;
          line-height: 1;
          margin-bottom: 25px;
        }

        &__label {
          flex: 0 0 100px;
          color: rgb(51 51 51);
        }

        &__value {
          color: rgb(102 102 102);

          & > .image {
            width: 104px;
            height: 104px;
            cursor: zoom-in;
          }

          .image + .image {
            margin-left: 10px;
          }
        }
      }
    }

    :deep(.ant-form-item) {
      width: 100%;
    }

    .label-w-100 {
      :deep(.ant-form-item-label) {
        width: 110px;
      }
    }

    .label-w-80 {
      :deep(.ant-form-item-label) {
        width: 80px;
      }
    }

    .wgjl-box {
      align-items: flex-start !important;
    }

    .min-w-100 {
      min-width: 100px;
    }
  }
</style>
