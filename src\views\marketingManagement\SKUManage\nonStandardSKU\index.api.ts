import { defHttp } from '/@/utils/http/axios';
import { downloadXlsTools } from '/@/utils/file/download';

enum Api {
  list = '/market/attribute/pool/list',
  edit = '/market/attribute/pool/edit',
  save = '/market/attribute/pool/add',
  delete = '/market/attribute/pool/delete',
  getDetailbyId = '/market/attribute/pool/queryById',
  brandList = '/veh/brand/all', // 品牌列表
  carTypeList = '/veh/model/all', // 车型列表
  getDictItems = '/sys/dict/getDictItems/', // 字典列表
  userAll = '/sys/user/listAll',
  exportExcel = '/veh/vehViolateSync/exportExcel', // post
  disabledOrEnable = '/market/attribute/pool/disabledOrEnable',
  
  checkExcel = '/ov/api/veh/asset/check/importExcel',
  excelModel = '/sys/minio/download'
}

// 列表接口
export const list = (params: Record<string, any>) => defHttp.get({ url: Api.userAll, params });

//更新接口
export const saveOrUpdate = (params: Record<string, any>, isUpdate: Boolean) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params });
};

//删除接口
export const deleteItemById = async (params: Record<string, any>, handleSuccess: Fn<any>) => {
  await defHttp.delete({ url: Api.delete, params }, { joinParamsToUrl: true });
  handleSuccess();
};

// 根据id获取详情
export const getDetailbyId = (params: Record<string, any>) => defHttp.get({ url: Api.getDetailbyId, params });

// 品牌列表
export const brandList = (params: Record<string, any>) => defHttp.get({ url: Api.brandList, params });

// 车型列表
export const carTypeList = (params: Record<string, any>) => defHttp.get({ url: Api.carTypeList, params });

// 字典列表
export const getDictItemsApi = (dictCode: string) => defHttp.get({ url: Api.getDictItems + dictCode, params: { dictCode: dictCode } });

/**
 * 导出接口
 * @param params
 */
export const downloadXls = params => {
  downloadXlsTools(Api.exportExcel, params, '项目SKU');
};

//禁用、恢复接口
export const disabledOrEnableDate = async (params: Record<string, any>) => {
  await defHttp.post({ url: Api.disabledOrEnable, params });
}


import { useMessage } from '/@/hooks/web/useMessage';
const { createMessage, createWarningModal } = useMessage();
import { useGlobSetting } from '/@/hooks/setting';

const glob = useGlobSetting();
// 检查导入表格
export const checkImportExcel = (data, success) => {
  const isReturn = fileInfo => {
    console.log(fileInfo);

    try {
      if (fileInfo.code === 201) {
        const {
          message,
          result: { msg, fileUrl, fileName },
        } = fileInfo;
        const href = glob.uploadUrl + fileUrl;
        createWarningModal({
          title: message,
          centered: false,
          content: `<div>
                              <span>${msg}</span><br/> 
                              <span>具体详情请<a href = ${href} download = ${fileName}> 点击下载 </a> </span> 
                            </div>`,
        });
        //update-begin---author:wangshuai ---date:20221121  for：[VUEN-2827]导入无权限，提示图标错误------------
      } else if (fileInfo.code === 500 || fileInfo.code === 510) {
        createMessage.error(fileInfo.message || `${data.name} 导入失败`);
        //update-end---author:wangshuai ---date:20221121  for：[VUEN-2827]导入无权限，提示图标错误------------
      } else {
        createMessage.success(fileInfo.message || `${data.name} 文件上传成功`);
      }
    } catch (error) {
      console.log('导入的数据异常', error);
    } finally {
      typeof success === 'function' ? success(fileInfo) : '';
    }
  };
  return defHttp.uploadFile({ url: Api.checkExcel }, { file: data }, { success: isReturn });
};

// 下载模板
export const getExcelModel = params =>
  defHttp.get({ url: Api.excelModel, params, responseType: 'blob' }, { isTransformResponse: false });
