import { defHttp } from '/@/utils/http/axios';

enum Api {
  //  柱状图
  bar = '/ov/api/biAssetsOnline/onlineBarChart',
  //  表格数据
  table = '/ov/api/biAssetsOnline/onlineTableChart',
}

/**
 * 柱状图
 * @param params
 */
export const getBarData = (params = {}) =>
  defHttp.get({
    url: Api.bar,
    params,
  });

/**
 * 表格数据
 * @param params
 */
export const getTableData = (params = {}) =>
  defHttp.get({
    url: Api.table,
    params,
  });
