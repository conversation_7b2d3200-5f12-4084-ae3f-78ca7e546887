<template>
  <BasicDrawer v-bind="$attrs" @register="registerDrawer" title="申请退车" :width="adaptiveWidth" @ok="handleSubmit" :showFooter="true" destroyOnClose>
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts" setup>
  import { ref } from 'vue';
  import { getPersonByStationId } from '../index.api';
  import { getReasonList, getStations, handleReturnBack } from '../index.api';
  import { returnFormSchema } from '../index.data';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { useDrawerAdaptiveWidth } from '/@/hooks/jeecg/useAdaptiveWidth';

  const emit = defineEmits(['success', 'register']);
  const reasonList = ref([]); // 退车原因列表

  //表单配置
  const [registerForm, { resetFields, validate, setFieldsValue, getFieldsValue, updateSchema }] = useForm({
    schemas: returnFormSchema,
    labelWidth: 100,
    showActionButtonGroup: false,
  });

  // 更新交车人字段
  const updatePersonSchema = (stationId: string) => {
    // 新交车员工-根据场站id-查询，此处更新查询员工接口参数
    updateSchema({
      field: 'deliveryPerson',
      componentProps: ({ formModel }) => {
        return {
          api: getPersonByStationId,
          params: {
            stationId,
          },
          labelField: 'realname',
          valueField: 'id',
          onChange: (_, o) => {
            if (o) {
              // 拼接工号
              formModel.realName = o.label + (o.workNo ? '/' + o.workNo : '');
            } else {
              formModel.realName = '';
            }
          },
        };
      },
    });
  };

  //表单赋值
  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async data => {
    console.log('data', data);
    await resetFields();
    const reasons = await getReasonList({ status: '1' });
    const allStations = await getStations();
    reasonList.value = reasons || [];
    setFieldsValue({
      id: data.record.id, // 工单id
    });
    // 批量退车也使用此弹框，如果id有多个，则默认不设置停放场站
    if (data.record.id.indexOf(',') === -1) {
      const findStationName = allStations.find(item => item.id === data.record.stationId) || {};
      setFieldsValue({
        stationId: data.record.stationId,
        stationName: findStationName.stationName,
      });
      updatePersonSchema(data.record.stationId);
    }
    updateSchema({
      field: 'stationId',
      componentProps: {
        options: allStations,
        onChange: e => {
          setFieldsValue({
            deliveryPerson: '',
            stationName: '',
          });
          if (e) {
            const findStationName = allStations.find(item => item.id === e) || {};
            setFieldsValue({
              stationName: findStationName.stationName,
            });
            updatePersonSchema(e);
          } else {
            updatePersonSchema('');
          }
        },
      },
    });
    updateSchema({
      field: 'tcReason',
      componentProps: {
        options: reasonList.value,
      },
    });
  });

  const { adaptiveWidth } = useDrawerAdaptiveWidth();

  //提交事件
  async function handleSubmit() {
    try {
      await validate();
      let values = getFieldsValue();
      setDrawerProps({ confirmLoading: true });

      //提交表单
      await handleReturnBack({
        deliveryPersonId: values.deliveryPerson,
        deliveryPerson: values.realName,
        ids: values.id,
        reason: values.tcReason.replace(/,/g, '/'),
        stationId: values.stationId,
        stationName: values.stationName,
      });
      //关闭弹窗
      closeDrawer();
      //刷新列表
      emit('success');
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
