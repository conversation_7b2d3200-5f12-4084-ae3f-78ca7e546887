#dragEngineBox {
  .mobileBox {
    .mobileContent {
      .vue-grid-layout {
        & > .vue-grid-item {
          width: 100%!important;
          //height: auto!important;
          //position: relative!important;
          //transform: none!important;
        }
      }
    }
  }
}

//mobile预览情况下 - 排除组件间间距, 但排序会出现问题
//#dragEngineBox {
//  & .custom-page-content {
//    & > div:nth-child(1) {
//      & .mobileContent {
//        & .vue-grid-layout {
//          height: auto!important;
//          & .vue-grid-item {
//            height: auto!important;
//            position: relative!important;
//            transform: none!important;
//          }
//        }
//      }
//    }
//  }
//}
