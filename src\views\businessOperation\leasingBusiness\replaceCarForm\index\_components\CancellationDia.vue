<template>
  <!-- 作废弹框 -->
  <a-modal
    v-model:visible="visible"
    :title="status == '4' ? '作废' : '退车'"
    :confirmLoading="btnLoading"
    @cancel="handleClose"
    @ok="handleOk"
    :maskClosable="false"
    width="500px"
    :bodyStyle="{ 'min-height': '30vh' }"
  >
    <div class="p-20px">
      <a-form ref="formRef" name="confirmForm" :model="formData" :label-col="{ span: 4 }" autocomplete="off">
        <a-form-item
          v-if="status == '4'"
          label="作废原因"
          name="zfReason"
          :rules="[{ required: true, message: '请输入作废原因' }]"
        >
          <a-textarea
            v-model:value="formData.zfReason"
            show-count
            :maxlength="200"
            :autoSize="{ minRows: 8 }"
            placeholder="请输入作废原因"
          />
        </a-form-item>
        <a-form-item
          v-else
          label="退车原因"
          name="tcReason"
          :rules="[{ required: true, message: '请选择退车原因', trigger: 'change' }]"
        >
          <a-cascader
            v-model:value="formData.tcReason"
            :options="reasonList"
            :fieldNames="fieldNames"
            placeholder="请选择退车原因"
          />
        </a-form-item>
      </a-form>
    </div>
  </a-modal>
</template>

<script lang="ts" name="base-info" setup>
  import { useVModel } from '@vueuse/core';
  import { onMounted, ref } from 'vue';
  import { getReasonList, handleReturnBack, statusChange } from '../index.api';
  import { useMessage } from '/@/hooks/web/useMessage';

  const { createMessage } = useMessage();

  const props = defineProps<{
    id: string; // 当前记录id
    status: string; // 要改变的状态值 4 为作废 5 为退车
    modelValue: boolean;
  }>();
  // 弹框显示控制
  const emits = defineEmits(['success', 'update:modelValue']);
  const visible = useVModel(props, 'modelValue', emits);

  const reasonList = ref([]); // 退车原因列表
  // 退车选择字段对应
  const fieldNames = {
    label: 'reason',
    value: 'reason',
  };
  const btnLoading = ref<boolean>(false); // 加载状态
  const formRef = ref();
  const formData = ref({
    id: '',
    status: '', // 状态
    zfReason: '', //作废原因
    tcReason: [], // 退车原因
  });

  // 弹框关闭
  const handleClose = () => {
    visible.value = false;
    formRef.value?.resetFields();
    btnLoading.value = false;
  };

  // 弹框确定
  const handleOk = () => {
    formRef.value?.validate().then(async valid => {
      if (!valid.errorFields) {
        btnLoading.value = true;
        const api = props.status == '4' ? statusChange : handleReturnBack;
        const saveData =
          props.status == '4'
            ? {
                id: props.id,
                status: props.status,
                reason: formData.value.zfReason,
              }
            : {
                ids: props.id,
                reason: formData.value.tcReason.join('/'),
              };
        api(saveData)
          .then(() => {
            createMessage.success(`${props.status == '4' ? '作废' : '退车'}成功`);
            emits('success');
            handleClose();
          })
          .catch(() => {
            btnLoading.value = false;
          });
      }
    });
  };

  onMounted(() => {
    getReasonList({}).then(res => {
      reasonList.value = res;
    });
  });
</script>
