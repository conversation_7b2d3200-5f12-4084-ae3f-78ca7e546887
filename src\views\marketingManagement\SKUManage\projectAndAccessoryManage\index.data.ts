import { BasicColumn, FormSchema } from '/@/components/Table';

export const getColumns: (skuTypeBool: boolean) => BasicColumn[] = skuTypeBool => {
  return [
    {
      title: '一级类目',
      dataIndex: 'categoryName',
      key: 'categoryName',
      fixed: 'left',
    },
    {
      title: '二级类目',
      dataIndex: 'categoryTwoName',
      key: 'categoryTwoName',
    },
    {
      title: '类目SPU',
      dataIndex: 'spuName',
      key: 'spuName',
    },
    {
      title: skuTypeBool ? '项目名称' : '配件名称',
      dataIndex: 'skuName',
      key: 'skuName',
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      slots: { customRender: 'recordStatus' },
    },
    ...(skuTypeBool
      ? [
          {
            title: '工时定额',
            dataIndex: 'timeQuota',
            key: 'timeQuota',
          },
        ]
      : []),
    {
      title: 'SKU编码',
      dataIndex: 'skuCode',
      key: 'skuCode',
      slots: { customRender: 'skuCode' },
    },
    ...(!skuTypeBool
      ? [
          {
            title: '原厂编码',
            dataIndex: 'originalFactoryCode',
            key: 'originalFactoryCode',
          },
          {
            title: '配件属性',
            dataIndex: 'attribute',
            key: 'attribute',
            slots: { customRender: 'attribute' },
          },
        ]
      : []),
    {
      title: '适用车型',
      dataIndex: 'applicableModels',
      key: 'applicableModels',
    },
    {
      title: '规格',
      dataIndex: 'specifications',
      key: 'specifications',
    },
    ...(!skuTypeBool
      ? [
          {
            title: '单位',
            dataIndex: 'unit',
            key: 'unit',
          },
        ]
      : []),
    {
      title: '结算价',
      dataIndex: 'settlementPrice',
      key: 'settlementPrice',
    },
    {
      title: '更新人',
      dataIndex: 'updateBy',
      key: 'updateBy',
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
    },
  ];
};

export const getSearchFormSchema: (skuTypeBool: boolean) => FormSchema[] = skuTypeBool => [
  {
    label: '类目名称',
    field: 'categoryName',
    component: 'Input',
    colProps: { span: 6 },
    slot: 'treeSelect',
  },
  {
    label: '类目SPU',
    field: 'spuName',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: skuTypeBool ? '项目名称' : '配件名称',
    field: 'skuName',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: 'SKU编码',
    field: 'skuCode',
    component: 'Input',
    colProps: { span: 6 },
  },
];

export enum OpenDrawerType {
  add = 'add',
  edit = 'edit',
  detail = 'detail',
}
export enum CATEGORY_TYPE {
  LEVEL_1 = '1',
  LEVEL_2 = '2',
}
export enum Consumables {
  IsConsumables = 1,
  Consumables = 2,
}
export const ConsumablesObj = {
  [Consumables.IsConsumables]: '易耗品',
  [Consumables.Consumables]: '非易耗品',
};
export const getFormSchema: (skuTypeBool: boolean) => FormSchema[] = skuTypeBool => [
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    label: '',
    field: 'openType', // 编辑、新增、详情
    component: 'Input',
    show: false,
  },
  {
    label: '',
    field: 'skuType', // 配件 or 项目
    component: 'Input',
    show: false,
  },
  {
    field: 'attribute',
    label: '配件属性',
    component: 'RadioButtonGroup',
    defaultValue: '1',
    labelWidth: 80,
    ifShow: !skuTypeBool,
    componentProps: {
      options: [
        { label: '易耗品', value: Consumables.IsConsumables },
        { label: '非易耗品', value: Consumables.Consumables },
      ],
    },
    required: true,
  },
  {
    label: '类目名称',
    field: 'spuId',
    component: 'Input',
    labelWidth: 80,
    colProps: { span: 22 },
    slot: 'treeSelectLevel2',
    rules: [{ required: true, message: '请选择类目' }],
    dynamicDisabled: ({ values }) => values.openType == OpenDrawerType.detail,
  },
  {
    label: '类目SPU',
    field: 'spuName',
    component: 'Input',
    labelWidth: 80,
    colProps: { span: 22 },
    componentProps: {
      maxlength: 30,
    },
    dynamicDisabled: () => true,
  },
  {
    label: skuTypeBool ? '项目名称' : '配件名称',
    field: 'skuName',
    component: 'Input',
    labelWidth: 80,
    colProps: { span: 22 },
    componentProps: {
      maxlength: 50,
    },
    rules: [{ required: true, message: `请输入${skuTypeBool ? '项目名称' : '配件名称'}` }],
    dynamicDisabled: ({ values }) => values.openType == OpenDrawerType.detail,
  },
  {
    label: 'SKU编号',
    field: 'skuCode',
    component: 'Input',
    labelWidth: 80,
    colProps: { span: 22 },
    componentProps: {
      maxlength: 20,
    },
    dynamicDisabled: ({ values }) => values.id || values.openType == OpenDrawerType.detail,
    rules: [
      { required: true, message: '请输入SKU编号' },
      {
        pattern: /^[a-zA-Z0-9]+$/,
        message: 'SKU编号仅支持数字和字母',
      },
    ],
  },
  {
    label: '工时定额',
    field: 'timeQuota',
    component: 'Input',
    labelWidth: 80,
    colProps: { span: 22 },
    ifShow: skuTypeBool,
    rules: [
      { required: true, message: '请输入工时定额' },
      {
        validator: (_, value, callback) => {
          if (!/^(0|[1-9]\d*)(\.\d)?$/.test(value)) {
            callback('请输入数字，最多保留一位小数');
          } else {
            const num = parseFloat(value);
            if (num < 0 || num > 1000) {
              callback('工时定额范围应为0-1000');
            } else {
              callback();
            }
          }
        },
      },
    ],
    dynamicDisabled: ({ values }) => values.openType == OpenDrawerType.detail,
  },
  {
    label: '原厂编码',
    field: 'originalFactoryCode',
    component: 'Input',
    labelWidth: 80,
    colProps: { span: 22 },
    ifShow: !skuTypeBool,
    componentProps: {
      maxlength: 30,
    },
    rules: [
      { required: true, message: '请输入原厂编码' },
      {
        pattern: /^[a-zA-Z0-9]+$/,
        message: '原厂编码仅支持数字和字母',
      },
    ],
    dynamicDisabled: ({ values }) => values.openType == OpenDrawerType.detail,
  },
  {
    label: '单位',
    field: 'unit',
    component: 'JDictSelectTag',
    ifShow: !skuTypeBool,
    slot: 'unit',
    labelWidth: 80,
    required: true,
    colProps: { span: 22 },
  },
  {
    label: '结算价',
    field: 'settlementPrice',
    component: 'Input',
    labelWidth: 80,
    colProps: { span: 22 },
    componentProps: {
      maxlength: 50,
    },
    rules: [
      { required: true, message: '请输入结算价' },
      {
        validator: (_, value, callback) => {
          if (!/^(0|[1-9]\d*)(\.\d{1,2})?$/.test(value)) {
            callback('请输入数字，最多保留两位小数');
          } else {
            const num = parseFloat(value);
            if (num < 0 || num > 9999) {
              callback('结算价范围应为0-9999');
            } else {
              callback();
            }
          }
        },
      },
    ],
    dynamicDisabled: ({ values }) => values.openType == OpenDrawerType.detail,
  },
  {
    label: '适用车型',
    field: 'applicableModels',
    component: 'Input',
    labelWidth: 80,
    colProps: { span: 22 },
    slot: 'applications',
    dynamicDisabled: ({ values }) => values.openType == OpenDrawerType.detail,
  },
  {
    label: '规格',
    field: 'specifications',
    component: 'InputTextArea',
    labelWidth: 80,
    colProps: { span: 22 },
    componentProps: {
      maxlength: 200,
      rows: 4,
    },
    required: false,
    dynamicDisabled: ({ values }) => values.openType == OpenDrawerType.detail,
  },
];
