<template>
  <BasicDrawer
    v-bind="$attrs"
    @register="registerDrawer"
    title="申请替换车"
    :width="adaptiveWidth"
    @ok="handleSubmit"
    :showFooter="true"
    @close="handleClose"
    destroyOnClose
  >
    <BasicForm @register="registerForm">
      <template #applicationType="{ model, field }">
        <a-select v-model:value="model[field]" :options="applyTypeList" placeholder="请选择申请类型" />
      </template>
      <template #city="{ model, field }">
        <a-select
          ref="city"
          show-search
          v-model:value="model[field]"
          :options="cityAndStation.cityList"
          placeholder="请选择城市"
          @change="handleCityChange"
        />
      </template>
      <template #station="{ model, field }">
        <a-select
          ref="station"
          show-search
          v-model:value="model[field]"
          :options="cityAndStation.stationList"
          :disabled="cityAndStation.stationList.length === 0"
          placeholder="请选择场站"
          @change="handleStationChange"
        />
      </template>
    </BasicForm>
  </BasicDrawer>
</template>
<script lang="ts" setup>
  import { reactive, ref } from 'vue';
  import { getCityAndStation, handleReplaceCar } from '../index.api';
  import { replaceCarFormSchema } from '../index.data';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { useDrawerAdaptiveWidth } from '/@/hooks/jeecg/useAdaptiveWidth';
  import { initDictOptions } from '/@/utils/dict/JDictSelectUtil';
  import dayjs from 'dayjs';
  const emit = defineEmits(['success', 'register']);
  const applyTypeList = ref<any[]>([]);
  const cityAndStation = reactive({
    cityList: [] as any[],
    stationList: [] as any[],
    targetCity: {} as any,
    targetStation: {} as any,
  });

  initDictOptions('post_delivery_apply_type').then(res => {
    applyTypeList.value = res;
  });

  // 获取场站数据
  const initCityAndStation = async () => {
    // 获取城市数据
    await getCityAndStation({}).then(res => {
      const cityList = res.map(item => {
        return {
          ...item,
          label: item.operationCity,
          value: item.id,
        };
      });
      cityAndStation.cityList = cityList;
    });
  };
  // 城市变动
  const handleCityChange = values => {
    cityAndStation.targetCity = cityAndStation.cityList.find((item: any) => item.id === values) || '';
    setFieldsValue({ cityName: cityAndStation.targetCity.operationCity, stationId: undefined });
    // @ts-ignore
    const target = cityAndStation.targetCity?.stationVOList;
    if (target.length) {
      cityAndStation.stationList = target.map(item => {
        return {
          ...item,
          label: item.stationName,
          value: item.id,
        };
      });
    }
  };

  // 场站变动
  const handleStationChange = values => {
    cityAndStation.targetStation = cityAndStation.stationList.find((item: any) => item.id === values) || '';
    setFieldsValue({ stationName: cityAndStation.targetStation.stationName });
  };
  //表单配置
  const [registerForm, { resetFields, validate, setFieldsValue }] = useForm({
    schemas: replaceCarFormSchema,
    labelWidth: 120,
    showActionButtonGroup: false,
  });

  //表单赋值
  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async data => {
    await resetFields();
    const formData = data?.record;
    await initCityAndStation();
    setFieldsValue({
      pickupTaskId: formData.id,
      vehiclePlate: formData.vehiclePlate,
      vin: formData.vin,
      lesseeName: formData.lesseeName,
    });
    // 批量退车也使用此弹框，如果id有多个，则默认不设置停放场站
  });

  const { adaptiveWidth } = useDrawerAdaptiveWidth();

  //提交事件
  async function handleSubmit() {
    try {
      let values = await validate();
      const saveData: any = Object.assign(
        {},
        { ...values, requestPickupDate: dayjs(values.requestPickupDate).format('YYYY-MM-DD') }
      );
      setDrawerProps({ loading: true, confirmLoading: true });
      //提交表单
      await handleReplaceCar({ ...saveData });
      //关闭弹窗
      closeDrawer();
      handleClose();
      //刷新列表
      emit('success');
    } finally {
      setDrawerProps({ loading: false, confirmLoading: false });
    }
  }
  const handleClose = () => {
    resetFields();
    cityAndStation.cityList = [];
    cityAndStation.stationList = [];
    cityAndStation.targetCity = {};
    cityAndStation.targetStation = {};
  };
</script>
