@import './pagination.less';
@import './input.less';
@import './btn.less';
// @import './table.less';

// TODO beta.11 fix
.ant-col {
  width: 100%;
}

.ant-image-preview-root {
  img {
    display: unset;
  }
}

//update-begin---author:scott ---date:2023-08-28  for：【QQYUN-6374】UnoCSS替代windicss导致应用样式问题--
/*span.anticon:not(.app-iconify) {
  vertical-align: 0.125em !important;
}*/
//update-end---author:scott ---date::2023-08-28  for：【QQYUN-6374】UnoCSS替代windicss导致应用样式问题--

.ant-back-top {
  right: 20px;
  bottom: 20px;
}

.collapse-container__body {
  > .ant-descriptions {
    margin-left: 6px;
  }
}

.ant-image-preview-operations {
  background-color: rgba(0, 0, 0, 0.3);
}

.ant-popover {
  &-content {
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  }
}

// =================================
// ==============modal message======
// =================================
.modal-icon-warning {
  color: @warning-color !important;
}

.modal-icon-success {
  color: @success-color !important;
}

.modal-icon-error {
  color: @error-color !important;
}

.modal-icon-info {
  color: @primary-color !important;
}

.ant-checkbox-checked .ant-checkbox-inner::after,
.ant-tree-checkbox-checked .ant-tree-checkbox-inner::after {
  border-top: 0 !important;
  border-left: 0 !important;
}
