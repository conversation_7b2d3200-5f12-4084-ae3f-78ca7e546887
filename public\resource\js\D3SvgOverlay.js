
T.D3Overlay = T.Overlay.extend({

    initialize: function (init, redraw, options) {
        this.uid = new Date().getTime();
        this.init = init;
        this.redraw = redraw;
        if (options)
            this.options = options;
        d3.select("head")
            .append("style").attr("type", "text/css")

    },

    _zoomChange: function () {
        if (!this.redraw)
            this.init(this.selection, this.transform);
        else
            this.redraw(this.selection, this.transform);
    },

    onAdd: function (map) {
        this.map = map;
        var self = this;
        this._svg = new T.SVG();
        map.addLayer(this._svg);
        this._rootGroup = d3.select(this._svg._rootGroup).classed("d3-overlay", true);
        this.selection = this._rootGroup;
        this.transform = {
            LngLatToD3Point: function (a, b) {
                var _lnglat = a instanceof T.LngLat ? a : new T.LngLat(a, b);
                var point = self.map.lngLatToLayerPoint(_lnglat);
                this.stream.point(point.x, point.y);
            },
            unitsPerMeter: function () {
                return 256 * Math.pow(2, map.getZoom()) / 40075017
            },
            map: self.map,
            layer: self

        };
        this.transform.pathFromGeojson =
            d3.geo.path().projection(d3.geo.transform({
                point: this.transform.LngLatToD3Point
            }));
        this.init(this.selection, this.transform);
        if (this.redraw)
            this.redraw(this.selection, this.transform);

        map.addEventListener("zoomend", this._zoomChange, this);


    },


    onRemove: function (map) {
        map.removeEventListener("zoomend", this._zoomChange, this);
        this._rootGroup.remove();
        map.removeOverLay(this._svg)
    },


    bringToFront: function () {
        if (this._svg && this._svg._rootGroup) {
            var el = this._svg._rootGroup.parentNode;
            el.parentNode.appendChild(el);

        }
        return this;
    },



    bringToBack: function () {
        if (this._svg && this._svg._rootGroup) {
            var el = this._svg._rootGroup.parentNode;
            var parent = el.parentNode;
            parent.insertBefore(el, parent.firstChild);

        }
        return this;
    },


})
;


