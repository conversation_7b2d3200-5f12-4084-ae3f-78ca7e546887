import { useScript } from '/@/hooks/web/useScript';
import { useGlobSetting } from '@/hooks/setting';
import { nextTick } from 'vue';
export function useMaps() {
  const globSetting = useGlobSetting();

  const BAI_DU_MAP_URL = `https://api.map.baidu.com/getscript?v=3.0&ak=${globSetting.mapAk}&services=&t=20210201100830&s=1`;
  const BAI_DU_MAP_URL_WEBGL = `https://api.map.baidu.com/getscript?v=3.0&ak=${globSetting.mapAk}&type=webgl`;
  const TIAN_DI_MAP_URL_WEBGL = `https://api.tianditu.gov.cn/api?v=4.0&tk=${globSetting.tiandiMapTk}`;

  // const { toPromise } = useScript({ src: BAI_DU_MAP_URL });
  // const { toPromise: toPromiseWebgl } = useScript({ src: BAI_DU_MAP_URL_WEBGL });
  const { toPromise: toPromiseTianDi } = useScript({ src: TIAN_DI_MAP_URL_WEBGL });
  // async function creatMap({ wrapRef = null, lng = 116.404, lat = 39.915, zoom = 10, centerAndZoomBoolean = true, enableScrollWheelZoomBoolean = true }) {
  //   await toPromise();
  //   await nextTick();
  //   const wrapEl = wrapRef;
  //   if (!wrapEl) return;
  //   const BMap = (window as any).BMap;
  //
  //   const map = new BMap.Map(wrapEl);
  //   const point = new BMap.Point(lng, lat);
  //   centerAndZoomBoolean && map.centerAndZoom(point, zoom);
  //   enableScrollWheelZoomBoolean && map.enableScrollWheelZoom(true); // 启用滚轮放大缩小
  //   return map;
  // }
  // async function creatMapGL({ wrapRef = null, lng = 116.404, lat = 39.915, zoom = 10, centerAndZoomBoolean = true, enableScrollWheelZoomBoolean = true }) {
  //   await toPromiseWebgl();
  //   await nextTick();
  //   const wrapEl = wrapRef;
  //   if (!wrapEl) return;
  //   const BMapGL = (window as any).BMapGL;
  //
  //   const map = new BMapGL.Map(wrapEl);
  //   const point = new BMapGL.Point(lng, lat);
  //   centerAndZoomBoolean && map.centerAndZoom(point, zoom);
  //   enableScrollWheelZoomBoolean && map.enableScrollWheelZoom(true); // 启用滚轮放大缩小
  //   return map;
  // }
  async function creatMapTianDi({ wrapRef = null, lng = 116.40969, lat = 39.89945, zoom = 12, centerAndZoomBoolean = true, disableScrollWheelZoomBoolean = false }) {
    await toPromiseTianDi();
    await nextTick();
    const wrapEl = wrapRef;
    if (!wrapEl) return;
    const T = (window as any).T;
    const map = new T.Map(wrapEl);
    const lnglat = new T.LngLat(lng, lat);
    centerAndZoomBoolean && map.centerAndZoom(lnglat, zoom);
    disableScrollWheelZoomBoolean && map.disableScrollWheelZoom(); // 启用滚轮放大缩小
    return map;
  }
  return { creatMapTianDi };
}
