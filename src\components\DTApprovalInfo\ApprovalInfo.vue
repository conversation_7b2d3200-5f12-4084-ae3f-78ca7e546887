<script lang="ts" setup>
  import { onMounted, ref } from 'vue';
  import { initDictOptions } from '/@/utils/dict';
  import { EContractType } from '/@/views/businessOperation/leasingBusiness/leaseContract/index/index.api';
  import FilePreview from '/@/views/businessOperation/leasingBusiness/component/FilePreview.vue';
  import {
    ClockCircleOutlined,
    ClockCircleFilled,
    CheckCircleOutlined,
    CheckCircleFilled,
    CloseCircleOutlined,
    CloseCircleFilled,
  } from '@ant-design/icons-vue';

  /**
   * @params infoData 外部传入的审批数据
   * @params loading loading状态
   */
  const props = defineProps<{
    infoData: any;
    loading: boolean;
  }>();

  // const loading = ref<boolean>(false);
  const statusList = ref<any[]>([]);
  const approvalId = ref<string>('');
  const applicant = ref<string>('');
  const detail = ref<any[]>([]);
  const comments = ref<any[]>([]);
  const nodeList = ref<any[]>([]);

  initDictOptions('bo_contract_status').then((res: any) => {
    statusList.value = res;
  });

  // 处理状态文字展示
  const getTextByStatus = (status: string | undefined) => {
    if (!status) return '';
    return statusList.value.find((item: any) => +item.value === +status)?.text;
  };

  // 处理
  const getTextByType = (type: EContractType) => {
    switch (type) {
      case EContractType.LEASE_CONTRACT:
      case EContractType.NON_STANDARD_CONTRACT:
        return '租赁';
      case EContractType.LEASE_PURCHASE_CONTRACT:
        return '以租代售';
      case EContractType.SHOW_CAR_CONTRACT:
        return '展示车';
    }
  };

  // 判断节点是否处于等待状态
  const isPending = (node: any, index: number) => {
    // 找到第一个未处理的审批人节点索引
    const firstNoneIndex = nodeList.value.findIndex(i => i.result === 'NONE' && i.activityName.includes('审批人'));

    if (firstNoneIndex !== -1) {
      // 之前的节点都正常显示
      if (index <= firstNoneIndex) return false;
      // 之后的节点都处于 pending 状态
      return true;
    }

    // 抄送节点永远不会pending
    if (node.activityName.includes('抄送')) {
      return false;
    }

    return false;
  };

  //处理显示名字
  const getNameInitial = (name: string) => {
    if (!name) return '';
    return name.length > 2 ? name.slice(-2) : name;
  };

  /**
   * 处理图片数据
   * @param urlString 传递的图片url
   * @param returnType 返回的类型： array：[{ filePath: string; fileName: string }] | object: { filePath: string; fileName: string }
   */
  const handleCertUrl = (urlString: string, returnType: 'array' | 'object' = 'array') => {
    if (!urlString) return returnType === 'array' ? [] : null;
    const urls = urlString.match(/https?:\/\/[^,]+/g) || [urlString];
    const mapped = urls.map((url: string) => ({
      filePath: url,
      fileName: (() => {
        try {
          return url ? decodeURIComponent(new URL(url).pathname.split('/').pop() || '') : null;
        } catch (e) {
          return null;
        }
      })(),
    }));
    if (returnType === 'object') {
      return mapped[0] || null;
    }

    return mapped;
  };

  // 附件预览
  const showPreviewDia = ref<boolean>(false);
  const fileInfo = ref<{ filePath: string; fileName: string }>();
  const handleFileView = (item: any) => {
    fileInfo.value = item;
    showPreviewDia.value = true;
  };

  // 下载文件
  const handleDownload = (url: string) => {
    window.open(url, '_blank');
  };

  // 初始化数据
  const initData = async res => {
    const { approvalNo, detailsList, commentList, nodeList: nList } = res;
    approvalId.value = approvalNo;
    // 审批处理详情数据
    // formType 1、文本 2、单个文件 3、多个文件 4、文本(可点击跳转)
    // 将文件类型的数据拆分出来放到最后面
    const filterData = detailsList
      ?.filter(item => [2, 3].includes(item.formType))
      .map((item: any) => {
        return {
          label: item.formName,
          value: item.formValue
            ? item.formType === 3
              ? JSON.parse(item.formValue).map((item: any) => {
                  return handleCertUrl(item, 'object');
                })
              : item.formType === 2
              ? handleCertUrl(item.formValue)
              : []
            : [],
          type: item.formType,
        };
      });
    detail.value = detailsList
      ?.filter(ele => ![2, 3].includes(ele.formType))
      .map((item: any) => {
        if (item.formName === '申请人') applicant.value = item.formValue;
        return {
          label: item.formName,
          value: item.formValue,
          type: item.formType,
        };
      });
    detail.value = detail.value?.concat(filterData);
    // 处理审批节点
    const refuseIndex = nList?.findIndex(item => item.result === 'REFUSE');
    if (refuseIndex > -1) {
      nodeList.value = nList?.slice(0, refuseIndex + 1);
    } else {
      nodeList.value = nList;
    }
    comments.value = commentList || [];
  };

  // 初始化数据
  onMounted(async () => await initData(props.infoData));

  // 暴露获取审批详情，用于刷新审批详情数据
  defineExpose({
    initData,
  });
</script>

<template>
  <a-spin :spinning="loading">
    <div class="approval_wrap">
      <!-- 详情 -->
      <div class="sub-title">审核详情</div>
      <a-row class="detail-box-info" :gutter="20">
        <a-col :span="12" v-for="item in detail" :key="item.label">
          <div class="flex info-row" v-if="![2, 3].includes(item.type)">
            <div class="info-label">{{ item.label }}:</div>
            <div class="info-text">
              {{ item.type === 2 ? item.value.fileName : item.value || '-' }}
            </div>
          </div>
          <div class="flex info-row" v-else>
            <div class="info-label">{{ item.label }}:</div>
            <div class="info-text unwidth">
              <div class="file-url flex center" v-for="(file, index) in item.value" :key="index">
                <span class="file-link" @click="handleFileView(file)">{{ file.fileName || '-' }}</span>
                <span class="download-btn" @click="handleDownload(file.filePath)">下载</span>
              </div>
            </div>
          </div>
        </a-col>
      </a-row>
      <!-- 流程节点 -->
      <div class="sub-title">审核记录</div>
      <div class="node_wrap">
        <a-timeline>
          <a-timeline-item v-for="(node, index) in nodeList" :key="node.activityId">
            <template #dot>
              <img
                class="copy-img"
                v-if="isPending(node, index)"
                src="https://sales.9fcar.com.cn/ov-public/pending.png"
              />
              <ClockCircleFilled
                style="color: #1890ff"
                v-else-if="node.result === 'NONE' && !node.activityName.includes('抄送')"
              />
              <img
                class="copy-img"
                v-else-if="node.result === 'NONE' && node.activityName.includes('抄送')"
                src="https://sales.9fcar.com.cn/ov-public/copyfor.png"
              />
              <CheckCircleFilled
                style="color: #52c41a"
                v-else-if="node.result !== 'REFUSE' || node.activityId === 'sid-startevent'"
              />
              <CloseCircleFilled style="color: #f55448" v-else />
            </template>
            <div :class="['node_box', isPending(node, index) ? 'pedding-mask' : '']">
              <div class="node_top_info flex center">
                <div class="title">{{ node.activityName }}</div>
                <div class="date">{{ node.finishDate }}</div>
              </div>
              <div class="node_middle_info">
                <div class="flex">
                  <div :class="['content', node?.executeList?.some(item => item.remark) ? 'column' : '']">
                    <div class="applicant" v-for="(item, idx) in node.executeList" :key="idx">
                      <div class="name_wrap flex column center">
                        <div class="avatar">
                          <img class="avatar-img" :src="item.avatar" v-if="item.avatar" />
                          <div v-else class="avatar-placeholder">
                            {{ getNameInitial(item.name) || '' }}
                          </div>
                          <div class="result" v-if="!isPending(node, index)">
                            <CheckCircleOutlined
                              v-if="item.result === 'AGREE' || node.activityName.includes('抄送')"
                              style="color: #52c41a"
                            />
                            <CloseCircleOutlined v-else-if="item.result === 'REFUSE'" style="color: #f55448" />
                            <ClockCircleOutlined v-else style="color: #1890ff" />
                          </div>
                        </div>
                        <div class="name-date flex center">
                          <div class="name">{{ item.name }}</div>
                        </div>
                      </div>
                      <div class="remark" v-if="item.remark" :title="item.remark">备注：{{ item.remark }}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </a-timeline-item>
        </a-timeline>
      </div>
      <div class="sub-title" v-if="comments.length">全文评论</div>
      <div class="comment-box flex column">
        <div class="comment-item flex" v-for="(comment, index) in comments" :key="index">
          <div class="avatar avatar-box">
            <img class="avatar-img" :src="comment.avatar" v-if="comment.avatar" />
            <div v-else class="avatar-placeholder">
              {{ getNameInitial(comment.name) || '' }}
            </div>
          </div>
          <div class="comment-box flex column">
            <div class="name-time flex">
              <div class="name">{{ comment.name }}</div>
              <div class="time">{{ comment.date }}</div>
            </div>
            <div class="remark">{{ comment.remark }}</div>
          </div>
        </div>
      </div>
    </div>
    <FilePreview v-model="showPreviewDia" :file-info="fileInfo" />
  </a-spin>
</template>

<style lang="less" scoped>
  .info-row + .info-row {
    margin-top: 16px;
  }

  .info-label {
    min-width: 120px;
    font-family: PingFangSC-Regular;
    font-weight: 400;
    font-size: 15px;
    color: #333;
    line-height: 21px;
  }

  .info-text {
    line-height: 21px;
    font-weight: 400;
    font-size: 15px;
    color: #999;
    flex: 1;
    margin-left: 16px;
    max-width: 301px;
  }

  .detail-box-info {
    .info-label {
      width: 150px;
    }

    .info-text {
      margin-left: 20px;
    }

    :deep(.ant-col:nth-child(n + 3)) {
      margin-top: 33px;
    }
  }

  .file-item {
    cursor: pointer;
    color: #1890ff;
  }

  .no-wrap {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .file-item + .file-item {
    margin-top: 5px;
  }

  .contract-btns {
    .ant-btn {
      border-radius: 4px;
    }

    .ant-btn-link {
      margin-right: 0;
      padding: 0;
    }
  }

  .approval_wrap {
    .top_wrap {
      .top_box {
        width: 100%;
        padding: 0px 0 6px 0;
        border-bottom: 2px solid #e9e9e9;
        margin-bottom: 24px;
        .title {
          font-size: 16px;
          color: #333;
          font-weight: 200;
        }
      }
      .top_info {
        width: 100%;
        padding-bottom: 24px;
        border-bottom: 4px solid #e9e9e9;
        margin-bottom: 24px;
        .title_wrap {
          display: flex;
          align-items: center;
          padding: 0 0 24px 0;
          .title {
            font-size: 24px;
            font-weight: 600;
          }
          .status {
            margin-top: 2px;
            margin-left: 16px;
            font-size: 14px;
            height: 28px;
            display: flex;
            align-items: center;
            padding: 4px 6px;
            border-radius: 8px;
          }
          .status-1 {
            background-color: #ffe1e1;
            color: #cf1322;
          }
          .status-2 {
            background-color: #fff7e6;
            color: #fa8c16;
          }
          .status-4 {
            background-color: #ffe1e1;
            color: #cf1322;
          }
          .status-5 {
            background-color: #d2f4d2;
            color: #389e0d;
          }
          .status-6 {
            background-color: #c6e2ff;
            color: #0088fe;
          }
          .status-7 {
            background-color: #ffe1e1;
            color: #cf1322;
          }
        }
        .info-label {
          // width: 120px;
          min-width: unset;
        }
        .apply_date {
          color: #333;
          margin-left: 24px;
          max-width: unset;
          min-width: unset;
        }
      }
    }
    .sub-title {
      display: flex;
      align-items: center;
      font-size: 22px;
      font-weight: 600;
      padding: 16px 0;
      position: relative;
      &::before {
        content: '';
        position: absolute;
        width: 4px;
        height: 26px;
        background: #0079fe;
        left: -14px;
        // top: 0;
      }
    }
    .node_wrap {
      margin-top: 16px;
      ::v-deep(.ant-timeline-item-content) {
        top: -9px;
        width: 50%;
      }
      ::v-deep(.ant-timeline-item-head-custom) {
        font-size: 26px;
      }
      .copy-img {
        width: 28px;
        height: 28px;
      }
    }
    .comment-box {
      width: 100%;
      .comment-item {
        width: 50%;
        background-color: #efeff0;
        padding: 12px 24px;
        margin-bottom: 24px;
        border-radius: 12px;
        .avatar-box {
          .avatar-placeholder,
          .avatar-img {
            width: 42px;
            height: 42px;
            border-radius: 50%;
          }
          .avatar-placeholder {
            background: linear-gradient(#00d7ba, #00c0a6);
          }
        }
        .comment-box {
          margin-left: 8px;
          justify-content: space-between;
          .name-time {
            font-size: 15px;
            margin-bottom: 3px;
            .name {
              font-size: 16px;
              font-weight: 600;
              color: #333;
            }
            .time {
              margin-left: 16px;
              color: #666666;
            }
          }
          .remark {
            font-size: 14px;
            color: #333;
          }
        }
      }
    }
    .unwidth {
      max-width: unset;
    }
  }
  .node_box {
    display: flex;
    flex-direction: column;
    .node_top_info {
      .title {
        font-size: 18px;
        min-width: 320px;
      }
      .date {
        font-size: 15px;
        color: #999999;
      }
    }
    .node_middle_info {
      padding: 12px 0;
      display: flex;

      .content {
        display: flex;
        align-items: flex-start;
        .applicant-name {
          font-size: 15px;
          color: #666666;
          min-width: 320px;
        }
        .name_wrap {
          flex-direction: column;
          position: relative;

          .name-date {
            .name {
              margin-top: 6px;
              font-size: 15px;
              color: #666666;
            }
          }
        }
        .remark {
          margin-top: 6px;
          margin-left: 16px;
          font-size: 15px;
          color: #999999;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          overflow: hidden;
          text-overflow: ellipsis;
          -webkit-line-clamp: 3; /* 显示2行，超出部分显示... */
        }
        .finish_date {
          margin-left: 0;
        }
        .applicant {
          display: flex;
          align-items: flex-end;
        }
      }
    }
  }
  .pedding-mask {
    opacity: 0.5;
  }
  .avatar {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    position: relative;
    .avatar-img {
      width: 40px;
      height: 40px;
      border-radius: 6px;
    }
    .avatar-placeholder {
      background-color: #1890ff;
      width: 40px;
      height: 40px;
      border-radius: 6px;
      font-weight: bold;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
    }
    .result {
      position: absolute;
      left: 32px;
      top: 32px;
      width: 18px;
      height: 18px;
      background-color: #fff;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .column {
    flex-direction: column;
  }
  .row {
    flex-direction: row;
  }
  .center {
    align-items: center;
  }
  .file-url {
    margin-top: 12px;
    &:first-child {
      margin-top: 0;
    }
  }
  .file-link {
    display: inline-block;
    color: #0079fe !important;
    cursor: pointer;
    width: 400px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .download-btn {
    // margin-left: 60px;
    color: #0079fe !important;
    cursor: pointer;
  }
  .info-label {
    min-width: 160px;
  }
</style>
