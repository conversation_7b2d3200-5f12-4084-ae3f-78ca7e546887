<template>
  <div class="image-item-wrap">
    <img class="image-item__img" :src="getUrl" :alt="props.src" />
    <div class="image-item__previewMask">
      <Icon class="icon" size="18" icon="ant-design:eye-outlined" title="预览文件" @click="handlePreview" />
      <Icon v-if="!disabled" class="icon" size="18" icon="ant-design:delete-outlined" title="删除文件" @click="handleDelete" />
    </div>
  </div>
  <a-modal :visible="state.previewVisible" :footer="null" @cancel="handleCancel()">
    <img alt="example" style="width: 100%" :src="props.src" />
  </a-modal>
</template>
<script setup lang="ts">
  import pdf from '@/assets/images/pdf.png';
  import zip from '@/assets/images/zip.png';
  import { computed, reactive } from 'vue';
  import { useGlobSetting } from '@/hooks/setting';
  import { Base64 } from 'js-base64';
  import { downloadByUrl } from '@/utils/file/download';
  // 声明Emits
  const emit = defineEmits(['delete']);
  const props = defineProps({
    src: {
      type: String,
      default: '',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  });
  // const em
  const state = reactive({
    previewVisible: false,
  });

  const globSetting = useGlobSetting();
  const filePreviewUrl = globSetting.filePreview;

  const getUrl = computed(() => {
    let url = props.src;
    let arrObj = props.src.split('?');
    let filePath = arrObj[0];
    //文档采用pdf预览高级模式
    if (filePath.endsWith('.pdf')) {
      url = pdf;
    }
    if (filePath.endsWith('.zip')) {
      url = zip;
    }
    return url;
  });
  const handlePreview = () => {
    let arrObj = (props.src || '').split('?');
    let filePath = arrObj[0];
    //文档采用pdf预览高级模式
    if (filePath.endsWith('.pdf')) {
      let url = `${filePreviewUrl}/onlinePreview?url=${encodeURIComponent(Base64.encode(props.src))}`;
      window.open(url, '_blank');
    } else if (filePath.endsWith('.zip')) {
      downloadByUrl({
        url: props.src,
        target: '_blank',
      });
    } else {
      state.previewVisible = true;
    }
  };
  const handleCancel = () => {
    state.previewVisible = false;
  };
  const handleDelete = () => {
    emit('delete', props.src);
  };
</script>
<style scoped lang="less">
  .image-item-wrap {
    width: 244px;
    height: 155px;
    padding: 8px;
    border: 1px solid #d9d9d9;
    border-radius: 2px;
    position: relative;
    overflow: hidden;
    &:hover {
      .image-item__previewMask {
        opacity: 1;
      }
    }
    .image-item {
      &__img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
      &__previewMask {
        display: flex;
        justify-content: center;
        align-items: center;
        width: calc(100% - 16px);
        height: calc(100% - 16px);
        color: #fff;
        //color: rgba(255, 255, 255, 0.85);
        opacity: 0;
        background-color: rgba(0, 0, 0, 0.5);
        transition: all 0.3s;
        position: absolute;
        top: 8px;
        left: 8px;
        z-index: 3;
        .icon {
          margin: 0 4px;
          cursor: pointer;
        }
      }
    }
  }
</style>
