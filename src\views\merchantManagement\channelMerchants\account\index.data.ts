import { BasicColumn, FormSchema } from '@/components/Table';
// import {phone} from '@/views/channelMerchants/manage/index.data'
const phone = (required, schema, model) => {
  return [
    {
      required: required,
      validator: async (_, value) => {
        if (required && !value) {
          return Promise.reject(`请输入${schema.label}`);
        }
        if (!/^1[3456789]\d{9}$/.test(value)) {
          return Promise.reject(`${schema.label}格式有误`);
        }
        return Promise.resolve();
      },
      trigger: 'change',
    },
  ];
};
export const columns: BasicColumn[] = [
  {
    title: '渠道名称',
    dataIndex: 'basicName',
    width: 280,
  },
  {
    title: '类型',
    dataIndex: 'auditType_dictText',
    width: 50,
  },
  {
    title: '管理员姓名',
    dataIndex: 'basicRealName',
    width: 100,
  },
  {
    title: '管理员手机号',
    dataIndex: 'basicPhone',
    width: 100,
  },
  {
    title: '审核时间',
    dataIndex: 'auditTime',
    width: 100,
  },
  {
    title: '账号状态',
    dataIndex: 'accountStatus',
    width: 120,
    slots: { customRender: 'accountStatus' },
    // customRender: ({ text }) => text == 0 ?'正常':'禁用',
  },
];
export const searchFormSchema: FormSchema[] = [
  {
    label: '渠道商名称',
    field: 'basicName',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '管理员姓名',
    field: 'basicRealName',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '管理员手机号',
    field: 'basicPhone',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '账号状态',
    field: 'accountStatus',
    component: 'Select',
    colProps: { span: 6 },
    componentProps: {
      options: [
        {
          label: '正常',
          value: 0,
        },
        {
          label: '禁用',
          value: 1,
        },
      ],
    },
  },
];

export const formSchema: FormSchema[] = [
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    label: '渠道名称',
    field: 'basicName',
    component: 'Input',
    slot: 'basicNameText',
  },
  {
    label: '管理员姓名',
    field: 'basicRealName_Text',
    component: 'Input',
    slot: 'basicRealNameText',
  },
  {
    label: '管理员手机号',
    field: 'basicPhone_Text',
    component: 'Input',
    slot: 'basicPhoneText',
  },
  {
    label: '手机号码',
    field: 'basicPhone',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      if (model.relatedSer === 1) {
        return [
          {
            required: true,
            trigger: 'change',
          },
        ];
      }
      return phone(true, schema, model);
    },
  },
  {
    field: 'basicPassword',
    label: '登录密码',
    component: 'StrengthMeter',
    componentProps: {
      placeholder: '请输入登录密码',
      maxlength: 12,
    },
    helpMessage: ['密码强度校验5-12位,', '①至少一个大写字母、', '②至少一个小写字母、', '③至少一个数字'],
    required: true,
    rules: [
      {
        pattern: /^\S*(?=\S{5,})(?=\S*\d)(?=\S*[A-Z])(?=\S*[a-z])\S*$/,
        message: '请按要求填写密码',
        required: true,
      },
    ],
  },
  {
    field: 'confirmPassword',
    label: '确认密码',
    component: 'InputPassword',
    dynamicRules: ({ model }) => {
      return [
        {
          required: true,
          validator: (_, value) => {
            if (!value) {
              return Promise.reject('请输入确认密码');
            }
            if (value != model.basicPassword) {
              return Promise.reject('两次输入的密码不一致');
            }
            return Promise.resolve();
          },
        },
      ];
    },
  },

];