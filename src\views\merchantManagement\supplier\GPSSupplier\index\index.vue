<template>
  <BasicTable @register="registerTable">
    <template #tableTitle>
      <a-button
        type="primary"
        preIcon="ant-design:plus-outlined"
        v-if="hasPermission('gpsSupplier:add')"
        @click="handleCreate"
      >
        新增
      </a-button>
    </template>
    <template #state="{ record }">
      <div class="state-box">
        <div class="state-box__wrap"
          ><span :class="['state-dot', record.state == 0 ? 'red' : 'green']"></span>
          <span>{{ record.state == 0 ? '禁用' : '正常' }}</span></div
        >
      </div>
    </template>
    <template #action="{ record }">
      <TableAction :actions="getTableAction(record)" />
    </template>
  </BasicTable>
  <EditDrawer @register="registerDrawer" @success="reload" />
</template>
<script setup lang="ts">
  import { ActionItem, TableAction, BasicTable } from '@/components/Table';
  import { columns, searchFormSchema } from './index.data';
  import { list, changeStatus, handleDelSupplier } from './index.api';
  import { useDrawer } from '@/components/Drawer';
  import { useListPage } from '@/hooks/system/useListPage';
  import EditDrawer from './components/editDrawer.vue';
  import { Modal } from 'ant-design-vue';
  import { usePermission } from '@/hooks/web/usePermission';
  const { hasPermission } = usePermission();

  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    tableProps: {
      api: list,
      rowKey: 'id',
      columns: columns,
      showIndexColumn: true,
      size: 'small',
      formConfig: {
        labelWidth: 90,
        schemas: searchFormSchema,
      },
      actionColumn: {
        width: 180,
        fixed: 'right',
      },
    },
  });
  //注册table数据
  const [registerTable, { reload }] = tableContext;

  /**
   * 操作栏
   */
  function getTableAction(record): ActionItem[] {
    //这里应该是最多显示3个操作按钮，如果操作＞3个时，第三个按钮变成更多，点击更多展开其它操作按钮，这样可以保证更多按钮展开时显示至少两个按钮
    //按照现有逻辑没有更多
    return [
      {
        label: `${record.state === 0 ? '启用' : '禁用'}`,
        onClick: handleStatusChange.bind(null, record),
        ifShow: hasPermission('gpsSupplier:enable') || hasPermission('gpsSupplier:able'),
      },
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        ifShow: hasPermission('gpsSupplier:edit'),
      },
      {
        label: '删除',
        popConfirm: {
          title: '是否确认删除该供应商?',
          confirm: handleDelete.bind(null, record),
        },
        ifShow: record.canDel !== 0 && hasPermission('gpsSupplier:del'),
      },
    ];
  }
  const [registerDrawer, { openDrawer }] = useDrawer();

  // 新增
  const handleCreate = record => {
    openDrawer(true, {
      record,
      isUpdate: false,
      showFooter: true,
      labelKey: 'edit',
    });
  };
  // 编辑
  const handleEdit = record => {
    openDrawer(true, {
      record,
      isUpdate: true,
      showFooter: true,
      labelKey: 'edit',
    });
  };

  const handleDelete = async (record: Recordable) => {
    await handleDelSupplier({ id: record.id });
    await reload();
  };

  const handleStatusChange = async (record, v) => {
    Modal.confirm({
      title: `当前操作将${record.state === 0 ? '启用' : '禁用'}此渠道商`,
      content: `请注意：${
        record.state === 0 ? '供应商被恢复后可被正常获取GPS数据' : '供应商被禁用后将不可获取GPS数据'
      }`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        await changeStatus({ id: record.id, enable: record.state === 1 ? 0 : 1 });
        reload();
      },
      onCancel: () => {
        reload();
      },
    });
    await reload();
  };
</script>
<style scoped lang="less">
  .state-box {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    text-align: left;

    &__wrap {
      display: flex;
      align-items: center;
      width: 30%;
      min-width: 59.5px;
    }
    .state-dot {
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 15%;

      &.red {
        background-color: rgba(227, 96, 80, 1);
      }

      &.green {
        background-color: rgba(107, 217, 169, 1);
      }
    }
  }
</style>
