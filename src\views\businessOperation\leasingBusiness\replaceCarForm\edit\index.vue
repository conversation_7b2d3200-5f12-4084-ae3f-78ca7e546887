<template>
  <a-spin :spinning="loading" wrapperClassName="detail-spinning">
    <div class="common-detail-container">
      <div class="detail-box">
        <div class="box-title">基本信息</div>
        <BaseInfo :formData="formData" :myContractType="myContractType" />
      </div>
      <!-- 已作废、已退车后，提车任务单明细不显示任何内容 -->
      <div class="detail-box m-t-10px flex-1">
        <div class="box-title">提车任务单明细</div>
        <div v-if="!['4', '5'].includes(formData.status)" class="p-15px">
          <a-tabs v-model:activeKey="activeKey" tab-position="left">
            <a-tab-pane key="info" tab="工单信息">
              <VehicleInspectionInformationEdit :id="formData.id" :formData="formData" :myContractType="myContractType" />
            </a-tab-pane>
            <a-tab-pane key="logs" tab="工单日志">
              <TicketLogs :id="formData.orderCode" :history="formData?.workOrderLogs || []" />
            </a-tab-pane>
          </a-tabs>
        </div>
      </div>
    </div>
  </a-spin>
</template>
<!-- 交车状态: 1、待交车 2、交车中 3、已交车  4、已作废 5、已退车,6.退车中 -->
<script lang="ts" name="businessOperation-leasingBusiness-pickupTaskForm-detail" setup>
  import { onMounted, ref } from 'vue';
  import { useRoute } from 'vue-router';
  import { ITaskInfo } from '../detail/index.data';
  import TicketLogs from '@/views/businessOperation/leasingBusiness/pickupTaskForm/workOrderComponents/TicketLogs.vue'; // 工单日志
  import VehicleInspectionInformationEdit from '@/views/businessOperation/leasingBusiness/pickupTaskForm/workOrderComponents/VehicleInspectionInformationEdit.vue'; // 验车信息
  import BaseInfo from './_components/BaseInfo.vue'; // 基本信息
  import { getDetailInfo } from './index.api';
  import { EContractType } from '/@/views/businessOperation/leasingBusiness/leaseContract/index/index.api';

  const route = useRoute();
  const loading = ref<boolean>(false); // 加载状态
  // 当前步骤对应的值
  const activeKey = ref<string>('info');
  // 是否是租赁合同/以租代售合同/展示车合同
  const myContractType = ref<EContractType>((route.query?.type as EContractType) || EContractType.LEASE_CONTRACT);

  //表单信息
  const formData = ref<ITaskInfo>({
    id: '',
    applicationNo: '',
    orderCode: '',
    status: '',
    lesseeId: '',
    lesseeName: '',
    lesseePhone: '',
    lesseeAddress: '',
    vin: '',
    deliveryPerson: '',
    stationName: '',
    createTime: '',
    pickupDate: '',
    checkImgs: '',
    slaInfo: [],
    workOrderLogs: [],
    modelName: '',
    pickupTime: '',
    reason: '',
    vehiclePlate: '',
    history: [],
  });

  // 初始化数据
  const initData = () => {
    const id = route.query?.id as string;
    loading.value = true;
    getDetailInfo({ id })
      .then(res => {
        formData.value = Object.assign({}, formData.value, res);
      })
      .finally(() => {
        loading.value = false;
      });
  };

  onMounted(initData);
</script>
<style lang="less" scoped>
  @import url('../common/styles.less');
</style>
