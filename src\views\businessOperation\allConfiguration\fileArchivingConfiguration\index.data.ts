import { BasicColumn } from '/@/components/Table';
import { FormSchema } from '/@/components/Table';
import { render } from '@/utils/common/renderUtils';
import { ApprovalTemplateTypeOptions } from '/@/enums/contract.enum';

export const columns: BasicColumn[] = [
  {
    title: '审批类型',
    dataIndex: 'templateType',
    key: 'templateType',
    customRender: ({ text }) => {
      return text ? ApprovalTemplateTypeOptions[Number(text) - 1]?.label || '' : '';
    },
  },
  {
    title: '业务类型',
    dataIndex: 'businessType',
    key: 'businessType',
    customRender: ({ text }) => render.renderDict(text, 'bo_contract_type'),
  },
  {
    title: '运营城市',
    dataIndex: 'otherType',
    key: 'otherType',
  },
  {
    title: 'spaceId',
    dataIndex: 'spaceId',
    key: 'spaceId',
  },
  {
    title: 'folderId',
    dataIndex: 'folderId',
    key: 'folderId',
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '审批类型',
    field: 'templateType',
    component: 'Select',
    colProps: { span: 8 },
    componentProps: {
      options: ApprovalTemplateTypeOptions,
    },
  },
  {
    label: '业务类型',
    field: 'businessType',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'bo_contract_type',
      type: 'select',
      showChooseOption: false,
    },
    colProps: { span: 8 },
  },
];

export const formSchema: FormSchema[] = [
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    label: '审批类型',
    field: 'templateType',
    component: 'Select',
    labelWidth: 80,
    colProps: { span: 18 },
    componentProps: {
      options: [
        { label: '合同', value: 1 },
        { label: '非标合同', value: 2 },
      ],
    },
    required: true,
  },
  {
    label: '业务类型',
    field: 'businessType',
    component: 'Select',
    labelWidth: 80,
    colProps: { span: 18 },
    componentProps: {
      options: [],
    },
  },
  {
    label: '运营城市',
    field: 'otherType',
    component: 'Select',
    labelWidth: 80,
    colProps: { span: 18 },
    componentProps: {
      options: [],
    },
  },
  {
    label: 'spaceId',
    field: 'spaceId',
    component: 'Input',
    labelWidth: 80,
    colProps: { span: 18 },
    componentProps: {
      maxlength: 20,
    },
    required: true,
  },
  {
    label: 'folderId',
    field: 'folderId',
    component: 'Input',
    labelWidth: 80,
    colProps: { span: 18 },
    componentProps: {
      maxlength: 20,
    },
    required: true,
  },
];
