<script setup lang="ts">
  // #region
  import { ref } from 'vue';
  import dayjs from 'dayjs';
  import { submitApproval } from '@/api/common/api';
  import { useUserStore } from '/@/store/modules/user';
  import { listNormal } from '/@/views/businessOperation/leasingBusiness/component/index.api';
  import DingTalkApprovalDrawer from '@/views/businessOperation/leasingBusiness/component/DingTalkApprovalDrawer.vue';
  import type { DTApprovalDrawerRefType } from '@/views/businessOperation/leasingBusiness/component/DingTalkApprovalDrawer.vue';
  import { FormSchema } from '/@/components/Table';
  import { useDrawer } from '/@/components/Drawer';
  // @ts-ignore
  import { getDingTalkApprovalFormSchema } from './index.data.ts';
  // #endregion

  import { EContractType } from '@/enums/contract.enum';

  export type DTApprovalRefType = RefType<{
    handleDingTalkApproval: typeof handleDingTalkApproval;
  }>;

  // #region 钉钉审批
  const props = defineProps({
    myContractType: {
      type: String as PropType<EContractType>,
      default: EContractType.LEASE_CONTRACT,
    },
  });
  // 发起钉钉审批-注册弹框
  const [registerDingTalkDrawer, { openDrawer: openDingTalkDrawer }] = useDrawer();
  /**
   * 发起钉钉审批
   */
  function handleDingTalkApproval(record: any) {
    openDingTalkDrawer(true, {
      record,
      contractType: props.myContractType,
      tenantSaas: false,
      openCallBack: initDingTalkParams,
    });
  }
  const dingTalkSchemas = ref<Array<FormSchema>>(getDingTalkApprovalFormSchema(props.myContractType));
  const dingTalkApprovalDrawerRef = ref<DTApprovalDrawerRefType>(null);
  const templateOptions = ref([]);
  if (props.myContractType !== '04') {
    // 租赁订单2 以租代售订单4
    listNormal({
      templateType: props.myContractType == '01' ? '2' : '4',
    }).then(res => {
      if (res.length > 0) {
        templateOptions.value = res.map((item: any) => ({
          label: `${item.templateName} ${item.templateNo}`,
          value: item.id,
        }));
      }
    });
  }
  function initDingTalkParams(record) {
    console.log('🚀 ~ initDingTalkParams ~ record:', record);
    // 获取合同模板列表数据
    dingTalkApprovalDrawerRef.value?.updateSchema({
      field: 'templateId',
      componentProps: {
        options: templateOptions.value,
      },
    });
    dingTalkApprovalDrawerRef.value?.setFieldsValue({
      id: record.id,
      orderNo: record.orderNo,
      applicant: useUserStore().getUserInfo.realname + '/' + useUserStore().getUserInfo.workNo,
      applicationDate: dayjs().format('YYYY/MM/DD'),
      operationOwnership: record.lessorName,
      supplementaryAgreed: record.supplementaryAgreed,
      sealType: '公章',
      num: 4,
      orderProduct: record.boOrderProductList
        .map(item => `${item.productSpecifications} 租期${item.leaseTerm}个月`)
        .join('\n'),
    });
  }
  function handleValues(params) {
    try {
      if (props.myContractType === '04') {
        params.orderFileList = params.orderFileList.split(',');
      }
      params.fileList = params.fileList.split(',');
    } catch (err) {}
  }
  const emit = defineEmits(['success']);
  function success() {
    emit('success');
  }
  defineExpose({
    handleDingTalkApproval,
  });
  // #endregion 钉钉审批
</script>

<template>
  <DingTalkApprovalDrawer
    ref="dingTalkApprovalDrawerRef"
    :schemas="dingTalkSchemas"
    :submitApproval="submitApproval"
    @handleValues="handleValues"
    @register="registerDingTalkDrawer"
    @success="success"
  />
</template>

<style scoped lang="less"></style>
