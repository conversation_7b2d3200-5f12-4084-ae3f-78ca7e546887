<template>
  <BasicMenuItem v-if="!menuHasChildren(item) && getShowMenu" v-bind="$props" />

  <span v-if="menuHasChildren(item) && getShowMenu && isCustom" class="custom-top-menu">
    <div class="custom-top-menu__menulist">
      <a-menu-item-group :title="item?.title" className="custom-menu-item-group">
        <template v-for="childrenItem in item.children || []" :key="childrenItem.path">
          <CustomMenu v-bind="$props" :item="childrenItem" :isCustom="true" />
        </template>
      </a-menu-item-group>
    </div>
  </span>
</template>
<script lang="ts">
  import { Menu } from 'ant-design-vue';
  import { computed, defineComponent } from 'vue';
  import { itemProps } from '../props';
  import BasicMenuItem from './BasicMenuItem.vue';
  import MenuItemContent from './MenuItemContent.vue';
  import { useDesign } from '/@/hooks/web/useDesign';
  import type { Menu as MenuType } from '/@/router/types';
  import { checkChildrenHidden } from '/@/utils/common/compUtils';

  export default defineComponent({
    name: 'CustomMenu',
    isSubMenu: true,
    components: {
      BasicMenuItem,
      SubMenu: Menu.SubMenu,
      MenuItemContent,
    },
    props: itemProps,
    setup(props) {
      const { prefixCls } = useDesign('basic-menu-item');

      const getShowMenu = computed(() => !props.item.meta?.hideMenu);
      function menuHasChildren(menuTreeItem: MenuType): boolean {
        return !menuTreeItem.meta?.hideChildrenInMenu && Reflect.has(menuTreeItem, 'children') && !!menuTreeItem.children && menuTreeItem.children.length > 0 && checkChildrenHidden(menuTreeItem);
      }
      return {
        prefixCls,
        menuHasChildren,
        checkChildrenHidden,
        getShowMenu,
      };
    },
  });
</script>

<style lang="less">
  .custom-top-menu__menulist {
    & > .ant-menu-item-group {
      & > .ant-menu-item-group-title {
        // color: #fff;
        color: #000;
        font-weight: 600;
        position: relative;
        &::before {
          content: '';
          display: inline-block;
          width: 4px;
          height: 12px;
          //margin-top: 4px;
          border-top-right-radius: 2px;
          border-bottom-right-radius: 2px;
          background: #3979f9;
          position: absolute;
          top: 14px;
          left: 3px;
        }
      }

      & > .ant-menu-item-group-list {
        width: 100%;
        display: flex;
        justify-content: flex-start;
        flex-wrap: wrap;
        & > .ant-menu-item {
          margin: 0;
          padding-left: 16px;
          padding-right: 10px;
          min-width: 122px;
        }
      }
    }
  }
</style>
<style lang="less" scoped>
  .custom-top-menu {
    width: 100%;

    &__menulist {
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
    }
  }

  .pop-title,
  .custom-top-menu__pop-title {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
    height: 50px;
    box-sizing: border-box;
    padding: 0 16px;
    // color: black;
    color: #fff;
    font-size: 16px;
    font-weight: 600;
  }
</style>
