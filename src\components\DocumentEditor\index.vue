<!-- 基本用法 -->
<template>
  <div style="width: 100%; height: 100%">
    <!-- <DocumentEditor id="docEditor" ref="docEditor" :documentServerUrl="documentServerUrl" :config="config" /> -->
    <div id="placeholder"></div>
  </div>
</template>
<script lang="ts" setup>
  // import { DocumentEditor } from '@onlyoffice/document-editor-vue';
  import { useGlobSetting } from '/@/hooks/setting';
  import { defineProps, ref, onMounted } from 'vue';

  const props = defineProps({
    docId: {
      type: String,
      default: '',
    },
    templateUrl: {
      type: String,
      default: '',
    },
    templateName: {
      type: String,
      default: '',
    },
    mode: {
      type: String,
      default: 'view',
    },
    timeStamp: {
      type: Number,
      default: '',
    },
  });
  // 预览文件、图片
  const { onlyOfficeUrl, onlyOfficePerviewUrl } = useGlobSetting();
  console.log('🚀 ~ domainUrl:', onlyOfficePerviewUrl);

  const docId = ref(props.docId);
  const documentServerUrl = onlyOfficeUrl; // http://**************:32539/
  // const documentServerUrl = 'https://onlyoffice.9fcar.com.cn/'; // http://**************:32539/
  console.log('🚀 ~ documentServerUrl:', documentServerUrl);
  // onlyOfficePerviewUrl http://**************:9000
  onMounted(() => {
    loadOnlyOfficeScript().then(() => {
      // 调用初始化函数
      initDoc();
    });
  });

  const emit = defineEmits(['onSaveDocument']);
  const docEditor = ref(null);
  const loadOnlyOfficeScript = (): Promise<void> => {
    return new Promise((resolve, reject) => {
      if (window.DocsAPI) return resolve(); // 已加载则跳过
      const script = document.createElement('script');
      script.src = `${documentServerUrl}web-apps/apps/api/documents/api.js`;
      script.onload = () => resolve();
      script.onerror = () => reject(new Error('ONLYOFFICE脚本加载失败'));
      document.head.appendChild(script);
    });
  };

  function initDoc() {
    let config = {
      document: {
        fileType: 'docx',
        key: 'doc_' + Date.now(), // 动态key强制重新加载
        title: `${props.templateName}${new Date().getTime()}` || '文档编辑',
        url: `${props.templateUrl}`, // 预览文件的URL
        permissions: {
          edit: true, // 是否允许编辑文档
          download: true, // 是否允许下载文档
          print: true, // 是否允许打印文档
          review: false, // 是否允许评论
          fillForms: true, // 是否允许填写表单
          copy: true, // 是否允许复制内容
          chat: false, // 是否允许聊天功能
          trackChanges: false, // 是否启用修订模式
        },
      },
      events: {
        onSaveDocument: function (e) {
          emit('onSaveDocument', false); // 触发保存事件
        },
        onDocumentStateChange: function (e) {
          emit('onSaveDocument', e.data);
        }, // 文档状态变化事件
      },
      documentType: 'word',
      editorConfig: {
        // callbackUrl: 'https://www.onlyoffice.com:443/callback.ashx?from=office-suite',
        callbackUrl: `${onlyOfficePerviewUrl}/ov/api/bo/boContractExportTemplate/onlineEditing?id=${docId.value}&timeStamp=${props.timeStamp}`, // 回调URL
        lang: 'zh-CN  ', //"en-US","zh-CN"
        mode: props.mode, // 预览模式
        customization: {
          autosave: true, // 是否启用自动保存
          forcesave: false, // 是否强制保存
        },
      },
    };
    // 忽略下一行的ts错误
    // @ts-ignore
    docEditor.value = new DocsAPI.DocEditor('placeholder', config);
  }
</script>

<style scoped lang="less"></style>
