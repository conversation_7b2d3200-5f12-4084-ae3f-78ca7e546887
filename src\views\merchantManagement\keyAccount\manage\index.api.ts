import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '@/hooks/web/useMessage';

const { createConfirm } = useMessage();

enum Api {
  queryStat = '/mdmKeyCustomers/head',
  list = '/mdmKeyCustomers/list',
  save = '/mdmKeyCustomers/add',
  submit = '/mdmKeyCustomers/submit',
  edit = '/mdmKeyCustomers/edit',
  importSubEnt = '/mdmKeyCustomers/importSub',
  editSubEnt = '/mdmKeyCustomers/editSub',
  deleteSubEnt = '/mdmKeyCustomers/delSub',
  confirm = '/mdmKeyCustomers/audit',
  deleteOne = '/mdmKeyCustomers/del',
  basicQueryById = '/mdmKeyCustomers/info',
  uploadResult = '/mdm/ent/user/getLicenseInfo',
  entNameCheck = '/mdmKeyCustomers/containsName',
  excelModel = '/sys/common/getImportTemplate', //导出模板
  subList = '/mdmKeyCustomers/subList',
}

/**
 * 大客户基本信息-首页审核统计
 * @param params
 */
export const queryStat = params => defHttp.get({ url: Api.queryStat, params });

/**
 * 列表
 * @param params
 */
export const list = params => defHttp.get({ url: Api.list, params });

export const getExcelModel = params => defHttp.get({ url: Api.excelModel, params }, { successMessageMode: 'none' });

/**
 * 详情
 * @param params
 */
export const queryByIdApi = params => defHttp.get({ url: Api.basicQueryById, params });

/**
 * 子企业列表
 */
export const getSubEnterList = params => defHttp.get({ url: Api.subList, params });

/**
 * 保存或者更新大客户
 * @param params
 */
export const saveOrUpdate = (params: any, update: boolean) =>
  defHttp.post({ url: !update ? Api.save : Api.edit, params });
/**
 * 删除
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.get({ url: Api.deleteOne, params }).then(() => {
    handleSuccess();
  });
};
/**
 * 提交审核
 * @param params
 */
export const submitAudit = params => {
  return defHttp.get({ url: Api.submit, params }, { successMessageMode: 'none' });
};
/**
 *
 * 确认审核
 * @param params
 */
export const confirmAudit = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认提交审核结果吗？',
    content: '提交后，则不可以修改',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      // handleSuccess();
      return defHttp.post({ url: Api.confirm, params }).then(() => {
        handleSuccess();
      });
    },
  });
};

/**
 * 识别营业执照
 * @param params { fileUrl: string }
 * @returns
 */
export const uploadResult = params => defHttp.get({ url: Api.uploadResult, params }, { successMessageMode: 'none' });

/**
 * 企业名称校验
 * @param params { name: string }
 * @returns
 */
export const getEnterNameCheckResult = params => defHttp.get({ url: Api.entNameCheck, params });

/**
 * 导入子企业
 * @param params { name: string }
 * @returns
 */
export const importSubEnterList = params => {
  const formData = new FormData();
  formData.append('file', params.file);
  //TODO 请求怎样处理的问题
  const headers = {
    'Content-Type': 'multipart/form-data;boundary = ' + new Date().getTime(),
  };
  return defHttp.post({
    url: Api.importSubEnt,
    params: formData,
    headers,
  });
};

/**
 * 编辑子企业
 * @param params { id: string }
 * @returns
 */
export const handleEditSubEnter = params =>
  defHttp.post({ url: Api.editSubEnt, params }, { successMessageMode: 'none' });

/**
 * 删除子企业
 * @param params { id: string }
 * @returns
 */
export const handleDeleteSubEnter = params =>
  defHttp.get({ url: Api.deleteSubEnt, params }, { successMessageMode: 'none' });