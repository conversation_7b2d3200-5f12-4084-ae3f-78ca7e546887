<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <a-button type="primary" @click="handleCreate" v-if="hasPermission('vehWarningConfiguration:add')">
          新增
        </a-button>
      </template>
      <!-- 推送规则 -->
      <template #pushRules="{ record }">
        <span>{{ getPushRules(record.pushRules) + record.pushTime + '自动推送' }}</span>
      </template>
      <!-- 推送场景 -->
      <template #pushScenario="{ record }">
        <span>{{ getPushScenario(record.pushScenario) + '-' + record.groupName }}</span>
      </template>
      <!-- 状态 -->
      <template #status="{ record }">
        <div class="state-box">
          <div class="state-box__wrap">
            <span :class="['state-dot', `status${record.status}`]"></span>
            <span>{{ record.status === 0 ? '禁用' : '启用' }}</span>
          </div>
        </div>
      </template>
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>

    <EditAndAddDrawer @register="registerDrawer" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" name="riskControlManagement-warningConfig" setup>
  //ts语法
  import { onMounted, ref } from 'vue';
  import EditAndAddDrawer from './components/EditAndAddDrawer.vue';
  import { useDrawer } from '/@/components/Drawer';
  import { ActionItem, BasicTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { list, handleDelete, handleChangeStatus } from './index.api';
  import { columns, searchFormSchema } from './index.data';
  import { usePermission } from '/@/hooks/web/usePermission';
  import { initDictOptions } from '/@/utils/dict/JDictSelectUtil';

  const { hasPermission } = usePermission();

  const [registerDrawer, { openDrawer }] = useDrawer();
  const scenarioList = ref<any[]>([]);
  const ruleList = ref<any[]>([]);
  initDictOptions('warning_config_push_scenario').then(res => {
    scenarioList.value = res;
  });
  initDictOptions('warning_config_push_rules').then(res => {
    ruleList.value = res;
  });
  const getPushScenario = (value: number) => {
    return scenarioList.value.find(item => +item.value === value)?.text;
  };
  const getPushRules = (value: number) => {
    return ruleList.value.find(item => +item.value === value)?.text;
  };
  onMounted(() => {});

  //注册drawer
  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    tableProps: {
      api: list,
      rowKey: 'id',
      showIndexColumn: true,
      rowSelection: { type: 'checkbox' },
      columns: columns,
      size: 'small',
      formConfig: {
        schemas: searchFormSchema,
        labelWidth: 90,
      },
      actionColumn: {
        width: 240,
      },

      beforeFetch: params => {
        return params;
      },
      afterFetch: res => {
        console.log('res', res);
        return res.map(item => {
          return {
            ...item,
            pushTime: item.pushTime ? (item.pushTime.length > 10 ? item.pushTime.slice(11, 16) : item.pushTime) : '',
          };
        });
      },
    },
  });

  //注册table数据
  const [registerTable, { reload, getForm }] = tableContext;

  // 新增事件
  function handleCreate() {
    //打开之前先获取车架号列表
    openDrawer(true, {
      isUpdate: false,
      showFooter: true,
      scenarioList: scenarioList.value,
      ruleList: ruleList.value,
    });
  }
  /**
   * 编辑事件
   */
  async function handleEdit(record: Recordable) {
    openDrawer(true, {
      record,
      isUpdate: true,
      showFooter: true,
      scenarioList: scenarioList.value,
      ruleList: ruleList.value,
    });
  }
  const handleDeleteOne = async ({ id }) => {
    await handleDelete({ id }, () => {
      reload();
    });
  };

  /**
   * 启用/禁用
   */
  async function handleStatusChange({ id, status }) {
    await handleChangeStatus({ id, enable: status === 0 ? 1 : 0 }, () => {
      reload();
    });
  }
  /**
   * 成功回调
   */
  function handleSuccess() {
    reload();
  }

  /**
   * 操作栏
   */
  function getTableAction(record): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        ifShow: hasPermission('vehWarningConfiguration:edit'),
      },
      {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDeleteOne.bind(null, record),
        },
        ifShow: hasPermission('vehWarningConfiguration:del'),
      },
      {
        label: `${record.status === 0 ? '启用' : '禁用'}`,
        popConfirm: {
          title: `是否确认${record.status === 0 ? '启用' : '禁用'}该预警配置？`,
          confirm: handleStatusChange.bind(null, record),
        },
        ifShow: hasPermission('vehWarningConfiguration:enable') || hasPermission('vehWarningConfiguration:able'),
      },
    ];
  }
</script>

<style lang="less">
  .custom-item__width {
    width: 90px !important;
  }

  .vehicleAssets-input {
    &:hover {
      .ant-input-suffix .vehicleAssets-input__icon {
        display: inline-block;
      }
    }

    &__icon {
      cursor: pointer;
      display: none;
    }
  }

  .modal-textArea {
    &::after {
      margin-bottom: 0;
      position: absolute;
      right: 10px;
      bottom: 25px;
      z-index: 1;
      font-size: 14px;
    }
  }

  .vehicleAssets-modal {
    & > .ant-modal-content {
      & > .ant-modal-body {
        padding: 20px 0 0;
      }
    }
  }

  .custom-select {
    .ant-select-selector {
      .ant-select-selection-placeholder {
        color: black;
      }
    }

    .ant-select-arrow {
      color: black;
    }
  }
</style>
<style scoped lang="less">
  .state-box {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    text-align: left;

    &__wrap {
      display: flex;
      align-items: center;
      width: 100%;
      min-width: 59.5px;
    }

    .state-dot {
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 10px;
    }
    .status0 {
      background-color: #f55448;
    }
    .status1 {
      background-color: #2cdc9b;
    }
  }
  :deep(.ant-table-cell) {
    .ant-btn {
      padding-left: 0;
    }
  }
</style>
