@import url('transition/index.less');
@import url('var/index.less');
@import url('public.less');
@import url('ant/index.less');
@import url('./theme.less');
@import url('./entry.css');

input:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 1000px #f7f7fb inset !important;
}

:-webkit-autofill {
  -webkit-box-shadow: 0 0 0 1000px #f7f7fb inset !important;
}

html {
  overflow: hidden;
  text-size-adjust: 100%;
}

html,
body {
  width: 100%;
  height: 100%;

  &.color-weak {
    filter: invert(80%);
  }

  &.gray-mode {
    filter: grayscale(100%);
    filter: progid:dximagetransform.microsoft.basicimage(grayscale=1);
  }
}

/* 【LOWCOD-2300】【vue3】online--online表单开发，下拉框位置靠下时，点开下拉框，整屏跳 */
body {
  overflow: visible;
  overflow-x: hidden;
}

a:focus,
a:active,
button,
div,
svg,
span {
  outline: none !important;
}

// 保持 和 windi 一样的全局样式，减少升级带来的影响
ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

img,
video {
  max-width: 100%;
  height: auto;
}

// 保持 和 windi 一样的全局样式，减少升级带来的影响

// 三网一车

.ant-table-thead {
  & .ant-table-cell {
    &::before {
      display: none;
    }
  }
}

.jeecg-basic-table .ant-table-pagination-right {
  justify-content: center;
}

.jeecg-layout-header-action {
  justify-content: flex-end !important;
}

button.ant-btn {
  border-radius: 6px;
  margin-right: 10px;
}

.jeecg-basic-table {
  & .ant-form-item-label {
    text-align: left;
  }

  & .quick-container {
    & .quick-title {
      text-align: left !important;

      /* 新增-跟正常的表单label左对齐 */
      // padding-left: 11px;
    }
  }
}

// 将统一的抽屉表单关闭按钮改为右侧
.jeecg-basic-drawer{
  & .ant-drawer-header{
    & .ant-drawer-header-title{
      flex-direction: row-reverse;
    }
  }
}

// 列表统一状态样式
.common-state-box {
  display: flex;
  align-items: center;
  justify-content: flex-start;

  &__wrap {
    display: flex;
    align-items: center;
    min-width: 80px;

    span:last-child {
      font-weight: 400;
      color: #333;
    }
  }

  .state-dot {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 9px;
  }
}
