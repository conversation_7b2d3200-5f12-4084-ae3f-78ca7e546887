import { FormSchema } from '@/components/Form';
import { BasicColumn } from '@/components/Table';
import { h } from 'vue';
import dayjs from 'dayjs';
import { Icon } from '@/components/Icon';
import { logAlarmPositionApi } from './index.api';
import { rules } from '@/utils/helper/validator';
import { getSupplierList } from '/@/api/common/api';

function customValidator(value, maxlength) {
  if (value?.trim()?.length > 0 && value?.trim()?.length < maxlength) {
    return Promise.reject();
  }
  return Promise.resolve();
}
const getAddress = async (e, params) => {
  e.stopPropagation();
  const { dataset } = e.target;
  const target = e.target.parentNode.parentNode;
  // console.log(e, params, dataset.state);

  if (dataset.state == 'true' || (!params.longitude && !params.latitude)) {
    return null;
  }
  let text = '暂无数据';
  try {
    const res = await logAlarmPositionApi({ id: params?.id });
    // console.log(res,'res');
    text = res ? res : '暂无数据';
    dataset.state = true;
    target.innerHTML = text;
    target.title = text;
    target.style.color = 'rgba(0, 0, 0, 0.65)';
    target.style.cursor = 'text';
    target.style.display = 'block';
    target.style.overflow = 'hidden';
    target.style.whiteSpace = 'nowrap';
    target.style.textOverflow = 'ellipsis';
  } catch (err) {
    console.log(err, 'err');
  }
};
export const formSchema: FormSchema[] = [
  {
    label: '围栏名称',
    field: 'fenceName',
    component: 'Input',
    colProps: { span: 8 },
  },
  {
    label: '城市地区',
    field: 'cityName',
    component: 'JAreaLinkage',
    colProps: { span: 8 },
    componentProps: {
      showArea: false,
    },
  },
];

export const drawerFormSchema: FormSchema[] = [
  {
    label: '',
    field: 'id',
    labelWidth: '120px',
    component: 'Input',
    show: false,
  },
  {
    label: '围栏名称',
    field: 'fenceName',
    labelWidth: '120px',
    component: 'Input',
    componentProps: {
      maxlength: 20,
    },
    dynamicRules: ({ model, schema }) => {
      return [
        {
          required: true,
          validator: rules.duplicateCheckRule('veh_fence', 'fence_name', model, schema, true, { delFlag: 0 })[0]
            .validator,
        },
      ];
    },
  },
  {
    label: '围栏类型',
    field: 'fenceType',
    labelWidth: '120px',
    component: 'RadioGroup',
    componentProps: {
      //options里面由一个一个的radio组成,支持disabled
      // 0=多边形,1=圆形
      optionType: 'button',
      buttonStyle: 'solid',
      options: [
        { label: '圆形', value: 1 },
        { label: '多边形', value: 0 },
        { label: '行政区域', value: 2 },
      ],
    },
    defaultValue: 1,
  },
  {
    label: '',
    field: 'center',
    labelWidth: '120px',
    component: 'Input',
    slot: 'circleCenter',
    ifShow({ values }) {
      return values.fenceType == 1;
    },
  },
  {
    label: '半径',
    field: 'radius',
    labelWidth: '120px',
    component: 'Input',
    componentProps: {
      suffix: '米',
    },
    defaultValue: '50.00',
    ifShow({ values }) {
      return values.fenceType == 1;
    },
  },
  {
    label: '',
    field: 'shape',
    labelWidth: '120px',
    component: 'Input',
    slot: 'polygon',
    ifShow({ values }) {
      return values.fenceType == 0;
    },
    colProps: { span: 22 },
    itemProps: {
      wrapperCol: { sm: 24 },
    },
  },
  {
    label: '行政区域',
    field: 'area',
    labelWidth: '120px',
    component: 'Input',
    slot: 'area',
    ifShow({ values }) {
      return values.fenceType == 2;
    },
    colProps: { span: 22 },
    itemProps: {
      wrapperCol: { sm: 24 },
    },
  },
  {
    label: '关联车辆',
    field: 'assertIds',
    labelWidth: '120px',
    component: 'Input',
    // required: true,
    slot: 'selectVehicles',
  },
  {
    label: '关联渠道商',
    field: 'lesseeIds',
    labelWidth: '120px',
    component: 'Input',
    // required: true,
    slot: 'selectChannelMerchants',
  },
  {
    label: '触发警告',
    field: 'alarmType',
    labelWidth: '120px',
    component: 'CheckboxGroup',
    required: true,
    componentProps: {
      name: '进出栏',
      options: [
        { label: '入栏', value: '0' },
        { label: '出栏', value: '1' },
        { label: '栏内', value: '2' },
        { label: '栏外', value: '3' },
      ],
    },
  },
  {
    label: '栏内告警时效',
    field: 'alarmTimelinessColumn',
    labelWidth: '120px',
    component: 'Select',
    ifShow({ values }) {
      console.log('🚀 ~ ifShow ~ values:', values);
      return values.alarmType?.includes('2');
      // return true;
    },
    required: true,
    componentProps: {
      options: [
        { label: '1', value: 1 },
        { label: '2', value: 2 },
        { label: '3', value: 3 },
        { label: '6', value: 6 },
        { label: '8', value: 8 },
        { label: '12', value: 12 },
        { label: '24', value: 24 },
      ],
    },
  },
  {
    label: '栏外告警时效',
    field: 'alarmTimelinessMargin',
    labelWidth: '120px',
    component: 'Select',
    required: true,
    ifShow({ values }) {
      console.log('🚀 ~ ifShow ~ values:', values);
      return values.alarmType?.includes('3');
      // return true;
    },
    componentProps: {
      options: [
        { label: '1', value: 1 },
        { label: '2', value: 2 },
        { label: '3', value: 3 },
        { label: '6', value: 6 },
        { label: '8', value: 8 },
        { label: '12', value: 12 },
        { label: '24', value: 24 },
      ],
    },
  },
  {
    label: '备注信息',
    field: 'remark',
    labelWidth: '120px',
    component: 'InputTextArea',
    componentProps: {
      maxlength: 100,
    },
  },
];

export const logColumns: BasicColumn[] = [
  {
    title: '车牌号',
    dataIndex: 'carPlate',
    fixed: 'left',
  },
  {
    title: '车架号',
    dataIndex: 'vin',
    fixed: 'left',
    width: 210,
  },
  {
    title: '车辆品牌',
    dataIndex: 'brand',
  },
  {
    title: '车辆型号',
    dataIndex: 'model',
  },
  // {
  //   title: '终端编码',
  //   dataIndex: 'code',
  // },
  {
    title: '运营城市',
    dataIndex: 'operationCity',
  },
  {
    title: '运营归属',
    dataIndex: 'operationOwnership',
  },
  {
    title: '关联围栏',
    dataIndex: 'fenceId_dictText',
  },
  {
    title: '定位数据源',
    dataIndex: 'supplierName',
    customRender: ({ text }) => text ?? '-',
  },
  {
    title: '告警类型',
    dataIndex: 'alarmType_dictText',
  },
  {
    title: '告警位置',
    dataIndex: 'alarmType_latlog',
    customRender({ record }) {
      return `${record.longitude},${record.latitude}`;
    },
    width: 180,
  },
  {
    title: '告警地址',
    dataIndex: 'alarmType_address',
    auth: 'veh:fence_log:alarm_position_query',
    customRender({ record }) {
      // console.log(record?.alarmPosition,'record')
      const ele = record?.alarmPosition
        ? h(
            'span',
            {
              style: { display: 'block', overflow: 'hidden', whiteSpace: 'nowrap', textOverflow: 'ellipsis' },
              title: record?.alarmPosition,
            },
            record?.alarmPosition
          )
        : h(
            'span',
            { style: { cursor: 'pointer', color: '#3a78f9' } },
            h(
              'span',
              { onClick: e => getAddress(e, record), '.state': false },
              h(Icon, { icon: 'ant-design:eye-invisible-outlined', size: 18 })
            )
          );
      return ele;
    },
  },
  {
    title: '数据上报时间',
    dataIndex: 'alarmTime',
  },
];

export const logSearchFormSchema: FormSchema[] = [
  {
    label: '车牌号',
    // labelWidth: 105,
    field: 'carPlate',
    component: 'JIsolateInputModal',
    colProps: { span: 6 },
    componentProps: ({ formActionType }) => {
      return {
        textareaLabel: '车牌号', // 弹出框标题
        placeholder: '点击加号，批量搜索',
        textareaPlaceholder: '批量查询，用换行分隔',
        textareaBindField: 'carPlates', //  绑定的查询字段
        formActionType, // 获取form的action
      };
    },
    rules: [
      {
        message: '车牌号模糊搜索至少输入3个字符',
        trigger: 'blur',
        validator: (_rule, value) => customValidator(value, 3),
      },
    ],
  },
  {
    label: '车架号',
    field: 'vin',
    component: 'JIsolateInputModal',
    colProps: { span: 6 },
    componentProps: ({ formActionType }) => {
      return {
        textareaLabel: '车架号', // 弹出框标题
        placeholder: '点击加号，批量搜索',
        textareaPlaceholder: '批量查询，用换行分割',
        textareaBindField: 'vins', //  绑定的查询字段
        formActionType, // 获取form的action
      };
    },
    rules: [
      {
        message: '车架号模糊搜索至少输入6个字符',
        trigger: 'blur',
        validator: (_rule, value) => customValidator(value, 6),
      },
    ],
  },
  {
    label: '定位数据源',
    field: 'supplierId',
    component: 'ApiSelect',
    colProps: { span: 6 },
    componentProps: {
      api: getSupplierList,
      labelField: 'name',
      valueField: 'id',
      showSearch: true,
      immediate: true,
    },
  },
  {
    label: '定位时间',
    labelWidth: 100,
    field: 'reportTime',
    component: 'RangePicker',
    componentProps: ({ formModel }) => {
      return {
        format: 'YYYY-MM-DD',
        valueFormat: 'YYYY-MM-DD',
        style: { width: '94%' },
        // 初始化默认值：结束日期 = 今天，开始日期 = 今天 - 89 天

        disabledDate: current => {
          if (!current) return false;

          const today = dayjs().endOf('day'); // 全局不能选今天之后的日期
          const [start, end] = formModel.reportTime || [];
          const s = start ? dayjs(start) : null;
          const e = end ? dayjs(end) : null;

          // 全局：不能选今天之后
          if (current.isAfter(today)) return true;

          // 已选开始 & 结束
          if (s && e) {
            const diff = e.diff(s, 'day');
            const extra = 30 - diff; // 剩余可浮动天数
            const min = s.subtract(extra, 'day').startOf('day');
            const max = e.add(extra, 'day').endOf('day');
            return current.isBefore(min) || current.isAfter(max) || current.isAfter(today);
          }

          // 只有开始日期
          if (s && !e) {
            const max = s.add(30, 'day');
            const maxDate = max.isAfter(today) ? today : max; // 不超过今天
            return current.isBefore(s.startOf('day')) || current.isAfter(maxDate.endOf('day'));
          }

          // 只有结束日期
          if (!s && e) {
            const min = e.subtract(30, 'day').startOf('day');
            return current.isBefore(min) || current.isAfter(e.endOf('day'));
          }

          return false;
        },

        // 选中回调，用于动态记录正在选的范围（可选）
        onCalendarChange: (dates, dateStrings, info) => {
          // info.range: "start" | "end"
          formModel.reportTime = dates ?? [];
        },

        defaultValue: [dayjs().subtract(30, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
      };
    },
    colProps: { span: 6 },
  },
  {
    label: '关联围栏',
    field: 'fenceName',
    component: 'JSelectInput',
    colProps: { span: 6 },
    componentProps: {
      showSearch: true,
    },
  },
  // {
  //   field: 'quickSearchSecornd',
  //   isQuickSearch: true,
  //   hideQuickTitle: true,
  //   hideByQuickSearchHide: false,
  //   componentList: [
  //     {
  //       label: '定位时间',
  //       labelWidth: 100,
  //       field: 'reportTime',
  //       component: 'RangePicker',
  //       componentProps: ({ formModel }) => {
  //         return {
  //           format: 'YYYY-MM-DD',
  //           valueFormat: 'YYYY-MM-DD',
  //           style: { width: '94%' },
  //           // 初始化默认值：结束日期 = 今天，开始日期 = 今天 - 89 天

  //           disabledDate: current => {
  //             if (!current) return false;

  //             const today = dayjs().endOf('day'); // 全局不能选今天之后的日期
  //             const [start, end] = formModel.reportTime || [];
  //             const s = start ? dayjs(start) : null;
  //             const e = end ? dayjs(end) : null;

  //             // 全局：不能选今天之后
  //             if (current.isAfter(today)) return true;

  //             // 已选开始 & 结束
  //             if (s && e) {
  //               const diff = e.diff(s, 'day');
  //               const extra = 30 - diff; // 剩余可浮动天数
  //               const min = s.subtract(extra, 'day').startOf('day');
  //               const max = e.add(extra, 'day').endOf('day');
  //               return current.isBefore(min) || current.isAfter(max) || current.isAfter(today);
  //             }

  //             // 只有开始日期
  //             if (s && !e) {
  //               const max = s.add(30, 'day');
  //               const maxDate = max.isAfter(today) ? today : max; // 不超过今天
  //               return current.isBefore(s.startOf('day')) || current.isAfter(maxDate.endOf('day'));
  //             }

  //             // 只有结束日期
  //             if (!s && e) {
  //               const min = e.subtract(30, 'day').startOf('day');
  //               return current.isBefore(min) || current.isAfter(e.endOf('day'));
  //             }

  //             return false;
  //           },

  //           // 选中回调，用于动态记录正在选的范围（可选）
  //           onCalendarChange: (dates, dateStrings, info) => {
  //             // info.range: "start" | "end"
  //             formModel.reportTime = dates ?? [];
  //           },
  //         };
  //       },
  //       colProps: { span: 6 },
  //     },
  //   ],
  // },
];
