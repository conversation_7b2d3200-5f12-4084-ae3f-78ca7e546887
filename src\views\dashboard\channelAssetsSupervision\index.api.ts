import { defHttp } from '/@/utils/http/axios';

enum Api {
  //  柱状图
  bar = '/ov/api/biChannelAssets/vehicleBarChart',
  //  折线图
  line = '/ov/api/biChannelAssets/vehicleLineChart',
  //  表格数据
  table = '/ov/api/biChannelAssets/vehicleTableChart',
  // 导出表格
  export = '/ov/api/biChannelAssets/vehicleTableChartExport',
}

export const exportUrl = Api.export;

/**
 * 柱状图
 * @param params
 */
export const getBarData = (params = {}) =>
  defHttp.get({
    url: Api.bar,
    params,
  });

/**
 * 折线图
 * @param params
 */

export const getLineData = (params = {}) =>
  defHttp.get({
    url: Api.line,
    params,
  });

/**
 * 表格数据
 * @param params
 */
export const getTableData = (params = {}) =>
  defHttp.get({
    url: Api.table,
    params,
  });
