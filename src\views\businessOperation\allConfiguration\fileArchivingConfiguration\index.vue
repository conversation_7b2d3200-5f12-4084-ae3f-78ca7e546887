<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable">
      <template #vehicleState="{ record }">
        <div class="state-box">
          <div class="state-box__wrap"
            ><span :class="['state-dot', record.vehicleState == 1 ? 'green' : 'red']"></span>
            <span>{{ record.vehicleState == 1 ? '正常' : '已废弃' }}</span></div
          >
        </div>
      </template>
      <template #tableTitle>
        <a-button
          type="primary"
          v-auth="'vehicle:brand:insert'"
          preIcon="ant-design:plus-outlined"
          @click="handleCreate"
          >新增</a-button
        >
      </template>
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
    <EditDrawer @register="registerDrawer" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" name="vehicleManagement-brandManagement" setup>
  import { BasicTable, TableAction, ActionItem } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import EditDrawer from './EditDrawer.vue';
  import { useDrawer } from '/@/components/Drawer';
  import { columns, searchFormSchema } from './index.data';
  import { list, deleteSpaceConfig } from './index.api';
  import { usePermission } from '/@/hooks/web/usePermission';
  const { hasPermission } = usePermission();
  const [registerDrawer, { openDrawer }] = useDrawer();

  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    tableProps: {
      title: '',
      api: list,
      rowKey: 'id',
      rowSelection: { type: 'checkbox' },
      columns: columns,
      size: 'small',
      showIndexColumn: true,
      formConfig: {
        schemas: searchFormSchema,
      },
      actionColumn: {
        width: 170,
        fixed: 'right',
      },
      beforeFetch: params => {
        return Object.assign(params, { column: 'updateTime', order: 'desc' });
      },
    },
  });

  //注册table数据
  const [registerTable, { reload }] = tableContext;

  /**
   * 新增事件
   */
  function handleCreate() {
    openDrawer(true, {
      isUpdate: false,
      showFooter: true,
      tenantSaas: false,
    });
  }
  /**
   * 编辑事件
   */
  async function handleEdit(record: Recordable) {
    openDrawer(true, {
      record,
      isUpdate: true,
      showFooter: true,
      tenantSaas: false,
    });
  }
  /**
   * 成功回调
   */
  function handleSuccess() {
    reload();
  }

  /**
   * 操作栏
   */
  function getTableAction(record): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        auth: 'vehicle:brand:edit',
      },
      {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
        },
      },
    ];
  }
  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteSpaceConfig({ id: record.id }, reload);
  }
</script>

<style scoped lang="less">
  .state-box {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    text-align: left;

    &__wrap {
      display: flex;
      align-items: center;
      width: 30%;
      min-width: 59.5px;
    }

    .state-dot {
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 15%;

      &.red {
        background-color: rgba(227, 96, 80, 1);
      }

      &.green {
        background-color: rgba(107, 217, 169, 1);
      }
    }
  }
</style>
