<template>
  <div class="detail">
    <div class="detail-basicName">{{ businessName }}</div>
    <BasicTable @register="registerTable">
      <template #accountStatus="{ record }">
        <div class="state-box">
          <div class="state-box__wrap"
            ><span :class="['state-dot', record.accountStatus == 0 ? 'green' : 'red']"></span>
            <span>{{ record.accountStatus == 0 ? '正常' : '禁用' }}</span></div
          >
        </div>
      </template>
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
  </div>
</template>
<script setup lang="ts">
  import { ActionItem, TableAction, BasicTable } from '@/components/Table';
  import { columns, searchFormSchema } from './index.data';
  import { list, disableDis, restoreDis } from './index.api';
  import { useDrawer } from '@/components/Drawer';
  import { useListPage } from '@/hooks/system/useListPage';
  import { Modal } from 'ant-design-vue';
  import { useRoute } from 'vue-router';
  const route = useRoute();
  const id = route.query.id;
  const businessName = route.query.name;
  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    tableProps: {
      api: params => {
        return list(params);
      },
      rowKey: 'id',
      columns: columns,
      showIndexColumn: true,
      size: 'small',
      formConfig: {
        schemas: searchFormSchema,
      },
      actionColumn: {
        width: 180,
        fixed: 'right',
      },
      beforeFetch: params => {
        params.id = route.query.id;
        return false;
      },
    },
  });
  //注册table数据
  const [registerTable, { reload }] = tableContext;

  /**
   * 操作栏
   */
  function getTableAction(record): ActionItem[] {
    //这里应该是最多显示3个操作按钮，如果操作＞3个时，第三个按钮变成更多，点击更多展开其它操作按钮，这样可以保证更多按钮展开时显示至少两个按钮
    //按照现有逻辑没有更多
    return [
      {
        label: record.accountStatus == 1 ? '恢复' : '禁用',
        onClick: handleStatusChange.bind(null, record),
        auth: record.accountStatus == 1 ? 'dis:account:restoreDisAccount' : 'dis:account:disableDisAccount',
      },
    ];
  }

  const handleStatusChange = async (record, v) => {
    console.log('🚀 ~ handleStatusChange ~ record:', record);
    Modal.confirm({
      title: `当前操作将${record.accountStatus == 1 ? '恢复' : '禁用'}此大客户的管理员账号和旗下的全部员工账号。`,
      content: `请注意：${record.accountStatus == 1 ? '账号被恢复后可正常使用渠道系统' : '账号被禁用后将无法登录渠道系统'}`,
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        record?.accountStatus == 1
          ? await restoreDis({ userId: record.userId, id })
          : await disableDis({ userId: record.userId, id });
        reload();
      },
      onCancel: () => {
        reload();
      },
    });
    await reload();
  };
</script>
<style scoped lang="less">
  .detail {
    .detail-basicName {
      margin: 10px 10px 0;
      text-align: left;
      background: #fff;
      font-size: 28px;
      padding: 10px;
    }
  }

  .state-box {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    text-align: left;

    &__wrap {
      display: flex;
      align-items: center;
      width: 30%;
      min-width: 59.5px;
    }
    .state-dot {
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 15%;

      &.red {
        background-color: rgba(227, 96, 80, 1);
      }

      &.green {
        background-color: rgba(107, 217, 169, 1);
      }
    }
  }
</style>
