
import { FormSchema } from '/@/components/Table';
import { EContractType } from '@/enums/contract.enum';

// 订单审批表单
export function getDingTalkApprovalFormSchema(myContractType: EContractType): FormSchema[] {
  // myContractType 01 02 租赁，以租代售
  // myContractType 04 非标
  let schema: FormSchema[] = [
    {
      // 订单id -- request
      label: '',
      field: 'id',
      component: 'Input',
      show: false,
    },
    {
      // No
      label: '订单编码',
      field: 'orderNo',
      component: 'Input',
      dynamicDisabled: true,
    },
    {
      // 订单模板 -- request
      label: '订单模板',
      field: 'templateId',
      component: 'Select',
      componentProps: {
        options: [],
        disabled: false,
      },
      rules: [{ required: true, message: '请选择模板' }],
      ifShow: ['01', '02'].includes(myContractType),
    },
    {
      // 提车需求 -- request
      label: '提车需求',
      field: 'orderProduct',
      component: 'InputTextArea',
      componentProps: {
        placeholder: ' ',
        autoSize: true,
      },
      dynamicDisabled: true,
    },
    {
      // 申请人 -- request
      label: '申请人',
      field: 'applicant',
      component: 'Input',
      dynamicDisabled: true,
    },
    {
      // 申请日期 -- request
      label: '申请日期',
      field: 'applicationDate',
      component: 'Input',
      dynamicDisabled: true,
    },
    {
      // 印章归属公司 -- request
      label: '印章归属公司',
      field: 'operationOwnership',
      component: 'Input',
      dynamicDisabled: true,
    },
    {
      // 印章类型 -- request
      label: '印章类型',
      field: 'sealType',
      component: 'Input',
      dynamicDisabled: true,
    },
    {
      // 用印份数 -- request
      label: '用印份数',
      field: 'num',
      component: 'Input',
      dynamicDisabled: true,
    },
    {
      // 订单附件 -- request
      label: '订单附件',
      field: 'orderFileList',
      component: 'JUpload',
      rules: [{ required: true, message: '请上传订单附件' }],
      slot: 'supplementary',
      ifShow: ['04'].includes(myContractType)
    },

    {
      // 补充附件 -- request
      label: '补充附件',
      field: 'fileList',
      component: 'JUpload',
      required: false,
      slot: 'supplementary',
      ifShow: ['01','04', '02'].includes(myContractType),
    },
    {
      // 订单补充约定 -- request
      label: '订单补充约定',
      field: 'supplementaryAgreed',
      component: 'InputTextArea',
      componentProps: {
        placeholder: ' ',
        autoSize: { minRows: 4},
      },
      dynamicDisabled: true,
    },
  ];

  return schema;
}
