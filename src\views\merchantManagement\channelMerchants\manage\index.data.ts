import { BasicColumn, FormSchema } from '@/components/Table';
import { render } from '@/utils/common/renderUtils';
import { Badge } from 'ant-design-vue';
import { h } from 'vue';
import { getEnterList } from '/@/api/common/api';
import { initDictOptions } from '@/utils/dict';
import { rules } from '@/utils/helper/validator';
import dayjs from 'dayjs';

const imageUploadRules = (required, model, schema) => {
  return [
    {
      required: required,
      validator: () => {
        for (let i = 0; i < schema.buss.imageUploads?.length; i++) {
          const item = schema.buss.imageUploads[i];
          if (required && !model[item.key]) {
            return Promise.reject(`请上传${item.text}`);
          }
        }
        return Promise.resolve();
      },
    },
  ];
};

const email = (required, schema) => {
  return [
    {
      required: required ? required : false,
      validator: async (_rule, value) => {
        if (required == true && !value) {
          return Promise.reject(`请输入${schema.label}`);
        }
        if (
          value &&
          !new RegExp(/^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/).test(value)
        ) {
          return Promise.reject(`请输入正确${schema.label}格式!`);
        }
        return Promise.resolve();
      },
      trigger: 'change',
    },
  ] as ArrayRule;
};
const phone = (required, schema, model) => {
  return [
    {
      required: required,
      validator: async (_, value) => {
        if (required && !value) {
          return Promise.reject(`请输入${schema.label}`);
        }
        if (!/^1[3456789]\d{9}$/.test(value)) {
          return Promise.reject(`${schema.label}格式有误`);
        }
        return Promise.resolve();
      },
      trigger: 'change',
    },
    {
      validator: rules.duplicateCheckRule('dis_basic_info', 'basic_phone', model, schema, false, { delFlag: 0 })[0].validator,
      trigger: 'blur',
    },
    {
      validator: rules.duplicateCheckRule('ser_basic_info', 'basic_phone', model, schema, false, { delFlag: 0 })[0].validator,
      trigger: 'blur',
    },
    {
      validator: rules.duplicateCheckRule('mdm_key_customers', 'phone', model, schema, false, { delFlag: 0 })[0].validator,
      trigger: 'blur',
    },
  ];
};
const checkRelatedSer = () => {
  return [
    {
      required: true,
      validator: async (_, value) => {
        if (!value) {
          return Promise.reject(`请选择关联关系`);
        }
        return Promise.resolve();
      },
      trigger: 'change',
    },
  ];
};
const idCards = (required, schema) => {
  return [
    {
      required: required,
      validator: async (_, value) => {
        if (required && !value) {
          return Promise.reject(`请输入${schema.label}`);
        }
        if (!new RegExp(/^\d{6}(18|19|20)?\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}(\d|[xX])$/).test(value)) {
          return Promise.reject(`请输入正确的18位${schema.label}`);
        }
        return Promise.resolve();
      },
      trigger: 'change',
    },
  ];
};
const address = required => {
  return [
    {
      required: required ? required : false,
      validator: async (_rule, value) => {
        if (required == true && !value) {
          return Promise.reject(`请选择省市区`);
        }
        return Promise.resolve();
      },
      trigger: 'change',
    },
  ] as ArrayRule;
};

const postcode = (required, schema) => {
  return [
    {
      required: required,
      validator: async (_, value) => {
        if (required && !value) {
          return Promise.reject(`请输入${schema.label}`);
        }
        if (value && value.length >= 1 && (!new RegExp(/^\d+$/).test(value) || value.length < 6)) {
          return Promise.reject(`请输入正确的6位${schema.label}号码`);
        }
        return Promise.resolve();
      },
      trigger: 'change',
    },
  ];
};

export const columns: BasicColumn[] = [
  {
    title: '渠道名称',
    dataIndex: 'basicName',
    width: 280,
  },
  {
    title: '类型',
    dataIndex: 'auditType_dictText',
    width: 50,
  },
  {
    title: '审核人',
    dataIndex: 'reviewer',
    width: 100,
  },
  {
    title: '提交时间',
    dataIndex: 'subTime',
    width: 120,
  },
  {
    title: '审核时间',
    dataIndex: 'auditTime',
    width: 120,
  },
  {
    title: '审核状态',
    dataIndex: 'auditStatus',
    customRender({ text }) {
      const colorArr = ['#00ffff', 'blue', 'cyan', 'red', 'green'];
      // 待提交、待审核(#00ffff或cyan)、未通过、已通过
      return render.renderDictComponents(text, 'dis_audit_status', txt => h(Badge, { color: colorArr[text], text: txt }));
    },
    width: 80,
  },
  {
    title: '营业状态',
    dataIndex: 'basicBusinessStatus',
    customRender({ text }) {
      //正常、停业
      const statusArr = ['', 'success', 'error'];
      return render.renderDictComponents(text, 'dis_business_status', txt => h(Badge, { status: statusArr[text], text: txt }));
    },
    width: 80,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '名称',
    field: 'basicName',
    component: 'Input',
    colProps: { span: 8 },
    componentProps: {
      placeholder: '渠道商名称/实际控制人/业务联系人',
    },
  },
  {
    label: '联系电话',
    field: 'basicPhone',
    component: 'Input',
    colProps: { span: 8 },
  },
];

const commonFormSchema: FormSchema[] = [
  // {
  //   label: 'tabsActive',
  //   field: 'tabsActive',
  //   component: 'Input',
  //   show: false,
  //   defaultValue: 'basic',
  // },
  {
    label: '审核状态',
    field: 'auditStatus',
    component: 'Input',
    show: false,
    defaultValue: false,
  },
  {
    label: '是否是审核编译后',
    field: 'isAuditEdit',
    component: 'Switch',
    show: false,
    defaultValue: false,
  },
  {
    label: '是否是审核通过后编辑',
    field: 'approvedStatus',
    component: 'Input',
    show: false,
    defaultValue: false,
  },
  {
    label: 'ID',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    label: '渠道商信息详情ID',
    field: 'infoId',
    component: 'Input',
    show: false,
  },
  //basicFormSchema
  //financeFormSchema
  //contactFormSchema
  //annexFormSchema
  //auditFormSchema
];
export const accountFormSchema: FormSchema[] = [
  ...commonFormSchema,
  {
    label: '手机号码',
    field: 'basicPhone',
    component: 'Input',
    required: true,
  },
  {
    label: '真实姓名',
    field: 'basicRealName',
    component: 'Input',
    required: true,
  },
];
export const basicFormSchema: FormSchema[] = [
  ...commonFormSchema,
  {
    label: '',
    field: 'title1',
    component: 'Input',
    slot: 'title',
    defaultValue: '选择关联服务商',
    colProps: { span: 24 },
    itemProps: {
      wrapperCol: { sm: 24 },
    },
  },
  {
    // 关联或不关联渠道商
    label: '',
    field: 'relatedSer',
    component: 'Select',
    slot: 'SelectRelate',
    dynamicRules: () => {
      return checkRelatedSer();
    },
    colProps: { span: 24 },
  },
  {
    label: '选择关联服务商',
    field: 'serId',
    component: 'Select',
    required: true,
    colProps: { span: 12, offset: 12, pull: 12 },
    // 关联，展示
    ifShow: ({values}) => {
      return values.relatedSer == 1 ? true: false;
    },
    dynamicDisabled: ({ values }) => values.isAuditEdit || values.approvedStatus,
    componentProps: {
      options: [],
    },
  },
  {
    label: '手机号码',
    field: 'basicPhone',
    component: 'Input',
    dynamicRules: ({ model, schema }) => {
      if (model.relatedSer === 1) {
        return [
          {
            required: true,
            trigger: 'change',
          },
        ];
      }
      return phone(true, schema, model);
    },
    dynamicDisabled: ({ values }) => values.auditStatus == '4' || values.isAuditEdit || values.relatedSer == 1,
  },
  {
    label: '真实姓名',
    field: 'basicRealName',
    component: 'Input',
    required: true,
    dynamicDisabled: ({ values }) => {
      return values.isAuditEdit || values.relatedSer == 1 
    },
  },
  {
    field: 'basicPassword',
    label: '登录密码',
    component: 'StrengthMeter',
    componentProps: {
      placeholder: '请输入登录密码',
      maxlength: 12,
    },
    helpMessage: ['密码强度校验5-12位,', '①至少一个大写字母、', '②至少一个小写字母、', '③至少一个数字'],
    required: true,
    rules: [
      {
        pattern: /^\S*(?=\S{5,})(?=\S*\d)(?=\S*[A-Z])(?=\S*[a-z])\S*$/,
        message: '请按要求填写密码',
        required: true,
      },
    ],
  },
  {
    field: 'confirmPassword',
    label: '确认密码',
    component: 'InputPassword',
    dynamicRules: ({ model }) => {
      return [
        {
          required: true,
          validator: (_, value) => {
            if (!value) {
              return Promise.reject('请输入确认密码');
            }
            if (value != model.basicPassword) {
              return Promise.reject('两次输入的密码不一致');
            }
            return Promise.resolve();
          },
        },
      ];
    },
  },
  {
    label: '经销商名称',
    field: 'basicName',
    component: 'Input',
    // required: true,
    dynamicRules: ({ model, schema }) => {
      return [
        {
          required: true,
          validator: rules.duplicateCheckRule('dis_basic_info', 'basic_name', model, schema, true, { delFlag: 0 })[0].validator,
        },
        {
          required: true,
          validator: rules.duplicateCheckRule('mdm_key_customers', 'business_name', model, schema, true, { delFlag: 0 })[0].validator,
        },
        {
          required: true,
          validator: rules.duplicateCheckRule('mdm_key_customers_sub', 'sub_business_name', model, schema, true, { delFlag: 0 })[0].validator,
        },
      ];
    },
  },
  {
    label: '社会信用代码',
    field: 'basicSocialCreditCode',
    component: 'Input',
    // required: true,
    dynamicRules: ({ model, schema }) => {
      if (model.relatedSer === 1) {
        return [
          {
            required: true,
            trigger: 'change',
          },
        ];
      }
      return [
        {
          required: true,
          validator: rules.duplicateCheckRule('dis_basic_info', 'basic_social_credit_code', model, schema, true, { delFlag: 0 })[0].validator,
        },
        {
          required: true,
          validator: rules.duplicateCheckRule('mdm_key_customers', 'social_credit_code', model, schema, true, { delFlag: 0 })[0].validator,
        },
        {
          required: true,
          validator: rules.duplicateCheckRule('mdm_key_customers_sub', 'social_credit_code', model, schema, true, { delFlag: 0 })[0].validator,
        },
      ];
    },
    dynamicDisabled: ({ values }) => values.relatedSer == 1,
  },
  {
    label: '法人姓名',
    field: 'basicLegalPerson',
    component: 'Input',
    required: true,
  },
  {
    label: '法人身份证号',
    field: 'basicLegalPersonCode',
    component: 'Input',
    dynamicRules: ({ schema }) => idCards(true, schema),
  },
  {
    label: '注册资金',
    field: 'basicRegisteredCapital',
    component: 'Input',
    required: true,
  },
  {
    label: '注册时间',
    field: 'basicRegisteredTime',
    component: 'DatePicker',
    required: true,
    componentProps: {
      //日期格式化，页面上显示的值
      format: 'YYYY-MM-DD',
      //返回值格式化（绑定值的格式）
      valueFormat: 'YYYY-MM-DD',
      //是否显示今天按钮
      showToday: true,
      disabledDate: current => {
        // Can not select days before today and today
        return current && current > dayjs().endOf('day');
      },
    },
  },
  {
    label: '业务邮箱',
    field: 'basicBusinessEmail',
    component: 'Input',
    componentProps: {
      maxlength: 50,
    },
    dynamicRules: ({ schema }) => email(true, schema),
    dynamicDisabled: ({ values }) => values.isAuditEdit,
  },
  {
    label: '联系电话',
    field: 'basicContactNumber',
    component: 'Input',
    required: true,
    componentProps: {
      maxlength: 11,
    },
    dynamicDisabled: ({ values }) => values.isAuditEdit,
  },
  {
    label: '营业时间',
    field: 'basicOpeningTime',
    component: 'RangeTime',
    // required: true,
    componentProps: {
      //日期格式化
      format: 'HH:mm',
      //范围文本描述用集合
      placeholder: '请选择开始时间,请选择结束时间',
    },
    rules: [{ required: true, message: '请选择营业时间' }],
    dynamicDisabled: ({ values }) => values.isAuditEdit,
  },
  {
    label: '业务归属区域',
    field: 'basicBusinessArea',
    component: 'JSearchSelect',
    required: true,
    componentProps: {
      api: getEnterList,
      apiParams: {
        tags: '4',
        pageNo: 1,
        pageSize: 99999,
        _t: new Date().getTime(),
      },
      afterFetch: (rows) => {
        if (Array.isArray(rows.records)) {
          return rows.records.map((item) => {
            return {
              value: item.id,
              text: item.entName,
            };
          });
        } else {
          return [];
        }
      },
    },
  },
  {
    label: '渠道类型',
    field: 'basicChannelType',
    component: 'JDictSelectTag',
    required: true,
    componentProps: {
      dictCode: 'dis_channel_type',
    },
  },
  {
    label: '拟合作业务类型',
    field: 'basicProposedBusinessType',
    component: 'JDictSelectTag',
    required: true,
    componentProps: {
      dictCode: 'dis_pro_business_type',
    },
  },
  {
    label: '拟合作业务名称',
    field: 'basicProposedBusinessName',
    component: 'JSelectMultiple',
    required: true,
    componentProps: {
      dictCode: 'dis_pro_business_name',
    },
    dynamicDisabled: ({ values }) => values.isAuditEdit,
  },
  {
    label: '企业简介',
    field: 'basicCompanyProfile',
    component: 'Input',
  },
  {
    label: '营业状态',
    field: 'basicBusinessStatus',
    component: 'ApiRadioGroup',
    componentProps: {
      api: initDictOptions,
      params: 'dis_business_status',
    },
    colProps: { span: 24 },
    show: false,
    dynamicDisabled: ({ values }) => values.isAuditEdit,
  },
  {
    label: '注册地址',
    field: 'basicRegisteredAddress',
    component: 'JAreaLinkage',
    colProps: { span: 8 },
    dynamicRules: () => address(true),
  },
  {
    label: '',
    field: 'basicFullAddress',
    component: 'Input',
    componentProps: {
      maxlength: 100,
      placeholder: '请输入详情地址',
    },
    rules: [{ required: true, message: '请输入详情地址' }],
    colProps: { span: 10, offset: 1 },
  },
  {
    label: '',
    field: 'basicAddressLngLat',
    component: 'Input',
    slot: 'latlng',
    colProps: { span: 24 },
    itemProps: {
      wrapperCol: { sm: 24 },
    },
  },
];

export const financeFormSchema: FormSchema[] = [
  ...commonFormSchema,
  {
    label: '开票公司全称',
    field: 'financeInvoicingCompany',
    component: 'Input',
    required: true,
    componentProps: {
      maxlength: 50,
    },
  },
  {
    label: '纳税人识别号',
    field: 'financeTaxpayerCode',
    component: 'Input',
    required: true,
    componentProps: {
      maxlength: 30,
    },
  },
  {
    label: '开票电话',
    field: 'financeInvoicingPhone',
    component: 'Input',
    required: true,
    componentProps: {
      maxlength: 11,
    },
  },
  {
    label: '银行账号',
    field: 'financeBankAccount',
    component: 'Input',
    required: true,
    componentProps: {
      maxlength: 19,
    },
  },
  {
    label: '开票地址',
    field: 'financeInvoicingAddress',
    component: 'JAreaLinkage',
    componentProps: {
      placeholder: '请选择省市区',
    },
    // required: true,
    colProps: { span: 7 },
    dynamicRules: () => address(true),
    // rules: [{ required: true, message: '请选择省市区' }],
  },
  {
    label: '',
    field: 'financeInvoicingFullAddress',
    component: 'Input',
    componentProps: {
      maxlength: 50,
      placeholder: '请输入详情地址',
    },
    rules: [{ required: true, message: '请输入详情地址' }],
    colProps: { span: 5 },
    itemProps: {
      wrapperCol: {
        sm: 23,
        push: 1,
      },
    },
  },
  {
    label: '开户行',
    field: 'financeOpenBank',
    component: 'Input',
    required: true,
    componentProps: {
      maxlength: 20,
    },
  },
];

export const contactFormSchema: FormSchema[] = [
  ...commonFormSchema,
  {
    label: '实际控制人姓名',
    field: 'contactActualController',
    component: 'Input',
    required: true,
    componentProps: {
      maxlength: 20,
    },
  },
  {
    label: '联系电话',
    field: 'contactPhone',
    component: 'Input',
    required: true,
    componentProps: {
      maxlength: 11,
    },
    dynamicDisabled: ({ values }) => values.isAuditEdit,
  },
  {
    label: '实际控制人身份证',
    field: 'contactActualIdUrl',
    component: 'Input',
    slot: 'updateImage',
    dynamicRules: ({ model, schema }) => imageUploadRules(true, model, schema),
    colProps: { span: 23 },
    itemProps: {
      wrapperCol: { sm: 24 },
    },
    buss: {
      imageUploads: [
        {
          key: 'contactActualIdUrl1',
          fileMax: 1,
          text: '个人信息页',
          maxSize: 10,
          accept: '.jpg, .jpeg, .png',
        },
        {
          key: 'contactActualIdUrl2',
          fileMax: 1,
          text: '国徽页',
          maxSize: 10,
          accept: '.jpg, .jpeg, .png',
        },
        {
          key: 'contactActualIdUrl3',
          fileMax: 1,
          text: '手持身份证',
          maxSize: 10,
          accept: '.jpg, .jpeg, .png',
        },
      ],
      descText: '核原件，提供复印件（加盖公章），支持JPG、JPEG、PNG格式，大小不超过10M。',
    },
  },
  {
    label: '个人信息页',
    field: 'contactActualIdUrl1',
    component: 'Input',
    // required: true,
    show: false,
  },
  {
    label: '国徽页',
    field: 'contactActualIdUrl2',
    component: 'Input',
    // required: true,
    show: false,
  },
  {
    label: '手持身份证',
    field: 'contactActualIdUrl3',
    component: 'Input',
    // required: true,
    show: false,
  },
  {
    label: '财务联系人姓名',
    field: 'contactFinanceName',
    component: 'Input',
    required: true,
    componentProps: {
      maxlength: 20,
    },
    dynamicDisabled: ({ values }) => values.isAuditEdit,
  },
  {
    label: '联系电话',
    field: 'contactFinancePhone',
    component: 'Input',
    required: true,
    componentProps: {
      maxlength: 11,
    },
    dynamicDisabled: ({ values }) => values.isAuditEdit,
  },
  {
    label: '邮箱地址',
    field: 'contactFinanceEmail',
    component: 'Input',
    required: true,
    dynamicRules: ({ schema }) => email(true, schema),
    dynamicDisabled: ({ values }) => values.isAuditEdit,
  },
  {
    label: '',
    field: 'emptyPlaceholder',
    component: 'Input',
    required: false,
    dynamicRules: ({ schema }) => email(false, schema),
    dynamicDisabled: ({ values }) => values.isAuditEdit,
    slot: 'emptyPlaceholder',
  },
  {
    label: '接车联系人姓名',
    field: 'contactPickName',
    component: 'Input',
    required: true,
    componentProps: {
      maxlength: 20,
    },
    dynamicDisabled: ({ values }) => values.isAuditEdit,
  },
  {
    label: '联系电话',
    field: 'contactPickPhone',
    component: 'Input',
    required: true,
    componentProps: {
      maxlength: 11,
    },
    dynamicDisabled: ({ values }) => values.isAuditEdit,
  },
  {
    label: '业务联系人',
    field: 'contactBusinessName',
    component: 'Input',
    required: true,
    componentProps: {
      maxlength: 20,
    },
    dynamicDisabled: ({ values }) => values.isAuditEdit,
  },
  {
    label: '联系电话',
    field: 'contactBusinessPhone',
    component: 'Input',
    required: true,
    componentProps: {
      maxlength: 11,
    },
    dynamicDisabled: ({ values }) => values.isAuditEdit,
  },
  {
    label: '收件地址',
    field: 'contactDeliveryAddress',
    component: 'JAreaLinkage',
    componentProps: {
      placeholder: '请选择省市区',
    },
    dynamicDisabled: ({ values }) => values.isAuditEdit,
    colProps: { span: 7 },
    dynamicRules: () => address(true),
  },
  {
    label: '',
    field: 'contactDeliveryFullAddress',
    component: 'Input',
    componentProps: {
      maxlength: 50,
      placeholder: '请输入详情地址',
    },
    dynamicDisabled: ({ values }) => values.isAuditEdit,
    rules: [{ required: true, message: '请输入详情地址' }],
    colProps: { span: 5 },
    itemProps: {
      wrapperCol: {
        sm: 23,
        push: 1,
      },
    },
  },
  {
    label: '邮编',
    field: 'contactPostalCode',
    component: 'Input',
    componentProps: {
      maxlength: 6,
      placeholder: '请输入邮编',
    },
    dynamicRules: ({ schema }) => postcode(true, schema),
    dynamicDisabled: ({ values }) => values.isAuditEdit,
  },
  {
    label: '接车地址',
    field: 'contactPickAddress',
    component: 'JAreaLinkage',
    componentProps: {
      placeholder: '请选择省市区',
    },
    dynamicRules: () => address(true),
    dynamicDisabled: ({ values }) => values.isAuditEdit,
    colProps: { span: 7 },
  },
  {
    label: '',
    field: 'contactPickFullAddress',
    component: 'Input',
    componentProps: {
      maxlength: 50,
      placeholder: '请输入详情地址',
    },
    dynamicDisabled: ({ values }) => values.isAuditEdit,
    rules: [{ required: true, message: '请输入详情地址' }],
    colProps: { span: 6 },
    itemProps: {
      wrapperCol: {
        sm: 19,
        push: 1,
      },
    },
  },
  {
    label: '自然人股东姓名',
    field: 'contactShareholder1Name',
    component: 'Input',
    componentProps: {
      maxlength: 20,
    },
    dynamicDisabled: ({ values }) => values.isAuditEdit,
  },
  {
    label: '联系电话',
    field: 'contactShareholder1Phone',
    component: 'Input',
    componentProps: {
      maxlength: 11,
    },
    dynamicDisabled: ({ values }) => values.isAuditEdit,
  },
  {
    label: '自然人股东姓名',
    field: 'contactShareholder2Name',
    component: 'Input',
    componentProps: {
      maxlength: 20,
    },
    dynamicDisabled: ({ values }) => values.isAuditEdit,
  },
  {
    label: '联系电话',
    field: 'contactShareholder2Phone',
    component: 'Input',
    componentProps: {
      maxlength: 11,
    },
    dynamicDisabled: ({ values }) => values.isAuditEdit,
  },
  {
    label: '自然人股东姓名',
    field: 'contactShareholder3Name',
    component: 'Input',
    componentProps: {
      maxlength: 20,
    },
    dynamicDisabled: ({ values }) => values.isAuditEdit,
  },
  {
    label: '联系电话',
    field: 'contactShareholder3Phone',
    component: 'Input',
    componentProps: {
      maxlength: 11,
    },
    dynamicDisabled: ({ values }) => values.isAuditEdit,
  },
];

export const annexFormSchema: FormSchema[] = [
  ...commonFormSchema,
  {
    label: '',
    field: 'title1',
    component: 'Input',
    slot: 'title',
    defaultValue: '以下为必填项',
    colProps: { span: 24 },
    itemProps: {
      wrapperCol: { sm: 24 },
    },
  },
  {
    label: '企业法人营业执照（正本或副本）',
    field: 'annexBusinessLicense',
    component: 'Input',
    slot: 'updateImage',
    dynamicRules: ({ model, schema }) => imageUploadRules(true, model, schema),
    buss: {
      imageUploads: [
        {
          key: 'annexBusinessLicense',
          fileMax: 1,
          text: '营业执照',
        },
      ],
      descText: '核原件，提供复印件（加盖公章），支持JPG、JPEG、PNG、PDF、ZIP格式，大小不超过50M。',
    },
  },
  {
    label: '法人身份证',
    field: 'annexLegalPersonId', //1、2、3
    component: 'Input',
    slot: 'updateImage',
    dynamicRules: ({ model, schema }) => imageUploadRules(true, model, schema),
    buss: {
      imageUploads: [
        {
          key: 'annexLegalPersonId1',
          fileMax: 1,
          text: '个人信息页',
        },
        {
          key: 'annexLegalPersonId2',
          fileMax: 1,
          text: '国徽页',
        },
        {
          key: 'annexLegalPersonId3',
          fileMax: 1,
          text: '手持身份证',
        },
      ],
      descText: '核原件，提供复印件（加盖公章），支持JPG、JPEG、PNG、PDF、ZIP格式，大小不超过50M。',
    },
  },
  {
    label: '个人信息页',
    field: 'annexLegalPersonId1',
    component: 'Input',
    show: false,
  },
  {
    label: '国徽页',
    field: 'annexLegalPersonId2',
    component: 'Input',
    show: false,
  },
  {
    label: '手持身份证',
    field: 'annexLegalPersonId3',
    component: 'Input',
    show: false,
  },
  {
    label: '公司章程',
    field: 'annexCompanyRule',
    component: 'Input',
    slot: 'updateImage',
    dynamicRules: ({ model, schema }) => imageUploadRules(true, model, schema),
    buss: {
      more: true,
      imageUploads: [
        {
          key: 'annexCompanyRule',
          fileMax: 1,
          text: '公司章程',
        },
      ],
      moreItem: {
        key: 'annexCompanyRuleMore',
        fileMax: 5,
        text: '添加更多',
      },
      descText: '公司章程（有股东签字盖章及法人签字），核原件，提供复印件（加盖公章），支持JPG、JPEG、PNG、PDF、ZIP格式，大小不超过50M。',
    },
  },
  {
    label: '添加更多',
    field: 'annexCompanyRuleMore',
    component: 'Input',
    show: false,
  },
  {
    label: '企业一个月内人行信用报告（银行详版）',
    field: 'annexCreditReport',
    component: 'Input',
    slot: 'updateImage',
    dynamicRules: ({ model, schema }) => imageUploadRules(true, model, schema),
    buss: {
      more: true,
      imageUploads: [
        {
          key: 'annexCreditReport',
          fileMax: 1,
          text: '征信报告',
        },
      ],
      moreItem: {
        key: 'annexCreditReportMore',
        fileMax: 5,
        text: '添加更多',
      },
      descText: '企业一个月内人行信用报告（银行详版）并提供复印件（加盖公章），支持JPG、JPEG、PNG、PDF、ZIP格式，大小不超过50M。',
    },
  },
  {
    label: '添加更多',
    field: 'annexCreditReportMore',
    component: 'Input',
    show: false,
  },
  {
    label: '企业经营情况及管理团队介绍',
    field: 'annexManagement', // annexManagementTeam 'annexBusinessCase',
    component: 'Input',
    slot: 'updateImage',
    dynamicRules: ({ model, schema }) => imageUploadRules(true, model, schema),
    buss: {
      more: true,
      imageUploads: [
        {
          key: 'annexBusinessCase',
          fileMax: 1,
          text: '公司经营情况',
        },
        {
          key: 'annexManagementTeam',
          fileMax: 1,
          text: '管理团队介绍',
        },
      ],
      moreItem: {
        key: 'annexBusinessCaseMore',
        fileMax: 5,
        text: '添加更多',
      },
      descText: '提供电子版，支持JPG、JPEG、PNG、PDF、ZIP格式，大小不超过50M。',
    },
  },
  {
    label: '管理团队介绍',
    field: 'annexManagementTeam',
    component: 'Input',
    show: false,
  },
  {
    label: '公司经营情况',
    field: 'annexBusinessCase',
    component: 'Input',
    show: false,
  },
  {
    label: '添加更多',
    field: 'annexBusinessCaseMore',
    component: 'Input',
    show: false,
  },
  {
    label: '公司组织架构及人员情况',
    field: 'annexOrgStructure',
    component: 'Input',
    slot: 'updateImage',
    dynamicRules: ({ model, schema }) => imageUploadRules(true, model, schema),
    buss: {
      more: true,
      imageUploads: [
        {
          key: 'annexOrgStructure',
          fileMax: 1,
          text: '公司组织架构',
        },
      ],
      moreItem: {
        key: 'annexOrgStructureMore',
        fileMax: 5,
        text: '添加更多',
      },
      descText: '提供电子版，支持JPG、JPEG、PNG、PDF、ZIP格式，大小不超过50M。',
    },
  },
  {
    label: '添加更多',
    field: 'annexOrgStructureMore',
    component: 'Input',
    show: false,
  },
  {
    label: '场地租赁协议及场地照片',
    field: 'annexLeaseAgreement',
    component: 'Input',
    slot: 'updateImage',
    dynamicRules: ({ model, schema }) => imageUploadRules(true, model, schema),
    buss: {
      more: true,
      imageUploads: [
        {
          key: 'annexLeaseAgreement',
          fileMax: 1,
          text: '场地租赁协议',
        },
      ],
      moreItem: {
        key: 'annexLeaseAgreementMore',
        fileMax: 5,
        text: '添加更多',
      },
      descText: '租赁协议扫描件（加盖公章），支持JPG、JPEG、PNG、PDF、ZIP格式，大小不超过50M。',
    },
  },
  {
    label: '添加更多',
    field: 'annexLeaseAgreementMore',
    component: 'Input',
    show: false,
  },
  {
    label: '场地照片（门头、前台、室内）',
    field: 'annexSitePhoto', //1、2 、3,
    component: 'Input',
    slot: 'updateImage',
    dynamicRules: ({ model, schema }) => imageUploadRules(true, model, schema),
    buss: {
      more: true,
      imageUploads: [
        {
          key: 'annexSitePhoto1',
          fileMax: 1,
          text: '门头',
        },
        {
          key: 'annexSitePhoto2',
          fileMax: 1,
          text: '前台',
        },
        {
          key: 'annexSitePhoto3',
          fileMax: 1,
          text: '室内',
        },
      ],
      moreItem: {
        key: 'annexSitePhotoMore',
        fileMax: 5,
        text: '添加更多',
      },
      descText: '需现场照片，支持JPG、JPEG、PNG、PDF、ZIP格式，大小不超过50M。',
    },
  },
  {
    label: '门头',
    field: 'annexSitePhoto1',
    component: 'Input',
    show: false,
  },
  {
    label: '前台',
    field: 'annexSitePhoto2',
    component: 'Input',
    show: false,
  },
  {
    label: '室内',
    field: 'annexSitePhoto3',
    component: 'Input',
    show: false,
  },
  {
    label: '添加更多',
    field: 'annexSitePhotoMore',
    component: 'Input',
    show: false,
  },
  {
    label: '',
    field: 'title2',
    component: 'Input',
    slot: 'title',
    defaultValue: '以下为选填项',
    colProps: { span: 24 },
    itemProps: {
      wrapperCol: { sm: 24 },
    },
  },
  {
    label: '道路运输经营许可证（如有）',
    field: 'annexTransportLicense',
    component: 'Input',
    slot: 'updateImage',
    // dynamicRules: ({ model, schema }) => imageUploadRules(true, model, schema),
    buss: {
      imageUploads: [
        {
          key: 'annexTransportLicense',
          fileMax: 1,
          text: '道路运输经营许可证',
        },
      ],
      descText: '核原件，提供复印件（加盖公章），支持JPG、JPEG、PNG、PDF、ZIP格式，大小不超过50M。',
    },
  },
  {
    label: '诉讼情况及融资情况',
    field: 'annexLitFin',
    component: 'Input',
    slot: 'updateImage',
    buss: {
      more: true,
      imageUploads: [
        {
          key: 'annexFinancing',
          fileMax: 1,
          text: '诉讼情况',
        },
        {
          key: 'annexLitigation',
          fileMax: 1,
          text: '融资情况',
        },
      ],
      moreItem: {
        key: 'annexLitFinMore',
        fileMax: 5,
        text: '添加更多',
      },
      descText: '提供电子版，支持JPG、JPEG、PNG、PDF、ZIP格式，大小不超过50M。',
    },
    itemProps: {
      wrapperCol: { sm: 24 },
    },
  },
  {
    label: '诉讼情况',
    field: 'annexFinancing',
    component: 'Input',
    show: false,
  },
  {
    label: '融资情况',
    field: 'annexLitigation',
    component: 'Input',
    show: false,
  },
  {
    label: '添加更多',
    field: 'annexLitFinMore',
    component: 'Input',
    show: false,
  },
  {
    label: '企公司自有车辆出租明细',
    field: 'annexCarRental',
    component: 'Input',
    slot: 'updateImage',
    buss: {
      more: true,
      imageUploads: [
        {
          key: 'annexCarRental',
          fileMax: 1,
          text: '出租明细',
        },
      ],
      moreItem: {
        key: 'annexCarRentalMore',
        fileMax: 5,
        text: '添加更多',
      },
      descText: '核原件，提供扫描件，支持JPG、JPEG、PNG、PDF、ZIP格式，大小不超过50M。',
    },
    itemProps: {
      wrapperCol: { sm: 24 },
    },
  },
  {
    label: '添加更多',
    field: 'annexCarRentalMore',
    component: 'Input',
    show: false,
  },
  {
    label: '实控人一个月内人行信用报告详版',
    field: 'annexControllerCreditReport',
    component: 'Input',
    slot: 'updateImage',
    buss: {
      more: true,
      imageUploads: [
        {
          key: 'annexControllerCreditReport',
          fileMax: 1,
          text: '征信报告',
        },
      ],
      moreItem: {
        key: 'annexControllerCreditReportMore',
        fileMax: 5,
        text: '添加更多',
      },
      descText: '提供复印件（加盖公章），支持JPG、JPEG、PNG、PDF、ZIP格式，大小不超过50M。',
    },
    itemProps: {
      wrapperCol: { sm: 24 },
    },
  },
  {
    label: '添加更多',
    field: 'annexControllerCreditReportMore',
    component: 'Input',
    show: false,
  },
  {
    label: '股权代持协议',
    field: 'annexEquityProxyAgreement',
    component: 'Input',
    slot: 'updateImage',
    buss: {
      more: true,
      imageUploads: [
        {
          key: 'annexEquityProxyAgreement',
          fileMax: 1,
          text: '股权代持协议',
        },
      ],
      moreItem: {
        key: 'annexEquityProxyAgreementMore',
        fileMax: 5,
        text: '添加更多',
      },
      descText: '核原件，提供复印件（加盖公章），支持JPG、JPEG、PNG、PDF、ZIP格式，大小不超过50M。',
    },
    itemProps: {
      wrapperCol: { sm: 24 },
    },
  },
  {
    label: '添加更多',
    field: 'annexEquityProxyAgreementMore',
    component: 'Input',
    show: false,
  },
  {
    label: '公司近半年进销存台账，抽查对应采购合同、销售合同及发票（提供3份）',
    field: 'annexInventorySalesLedger',
    component: 'Input',
    slot: 'updateImage',
    buss: {
      more: true,
      imageUploads: [
        {
          key: 'annexInventoryLedger',
          fileMax: 1,
          text: '进销存台账',
        },
        {
          key: 'annexPurchaseContracts',
          fileMax: 1,
          text: '采购合同',
        },
        {
          key: 'annexSaleContract',
          fileMax: 1,
          text: '销售合同',
        },
        {
          key: 'annexInvoice',
          fileMax: 1,
          text: '发票',
        },
      ],
      moreItem: {
        key: 'annexInventorySalesLedgerMore',
        fileMax: 5,
        text: '添加更多',
      },
      descText: '核原件，提供扫描件，支持JPG、JPEG、PNG、PDF、ZIP格式，大小不超过50M。',
    },
    itemProps: {
      wrapperCol: { sm: 24 },
    },
  },
  {
    label: '进销存台账',
    field: 'annexInventoryLedger',
    component: 'Input',
    show: false,
  },
  {
    label: '采购合同',
    field: 'annexPurchaseContracts',
    component: 'Input',
    show: false,
  },
  {
    label: '销售合同',
    field: 'annexSaleContract',
    component: 'Input',
    show: false,
  },
  {
    label: '发票',
    field: 'annexInvoice',
    component: 'Input',
    show: false,
  },
  {
    label: '添加更多',
    field: 'annexInventorySalesLedgerMore',
    component: 'Input',
    show: false,
  },
  {
    label: '公司资产（房产证、土地证）（承租人、法人担保方）（如有）',
    field: 'annexCorporateAssets',
    component: 'Input',
    slot: 'updateImage',
    buss: {
      more: true,
      imageUploads: [
        {
          key: 'annexTitleDeedCorp',
          fileMax: 1,
          text: '公司房产证',
        },
        {
          key: 'annexLandTitleCorp',
          fileMax: 1,
          text: '公司土地证',
        },
      ],
      moreItem: {
        key: 'annexCorporateAssetsMore',
        fileMax: 5,
        text: '添加更多',
      },
      descText: '核原件，提供扫描件，支持JPG、JPEG、PNG、PDF、ZIP格式，大小不超过50 M。',
    },
    itemProps: {
      wrapperCol: { sm: 24 },
    },
  },
  {
    label: '公司房产证',
    field: 'annexTitleDeedCorp',
    component: 'Input',
    show: false,
  },
  {
    label: '公司土地证',
    field: 'annexLandTitleCorp',
    component: 'Input',
    show: false,
  },
  {
    label: '添加更多',
    field: 'annexCorporateAssetsMore',
    component: 'Input',
    show: false,
  },
  {
    label: '个人资产权证原件（自然人实控人、自然人担保方），土地证、房产证、机动车登记证等',
    field: 'annexPersonalAssets',
    component: 'Input',
    slot: 'updateImage',
    buss: {
      more: true,
      imageUploads: [
        {
          key: 'annexLandTitlePer',
          fileMax: 1,
          text: '个人土地证',
        },
        {
          key: 'annexTitleDeedPer',
          fileMax: 1,
          text: '个人房产证',
        },
        {
          key: 'annexVehicleLicensePer',
          fileMax: 1,
          text: '机动车登记证',
        },
      ],
      moreItem: {
        key: 'annexPersonalAssetsMore',
        fileMax: 5,
        text: '添加更多',
      },
      descText: '核原件，提供复印件（加盖公章），支持JPG、JPEG、PNG、PDF、ZIP格式，大小不超过50M。',
    },
    itemProps: {
      wrapperCol: { sm: 24 },
    },
  },
  {
    label: '个人土地证',
    field: 'annexLandTitlePer',
    component: 'Input',
    show: false,
  },
  {
    label: '个人房产证',
    field: 'annexTitleDeedPer',
    component: 'Input',
    show: false,
  },
  {
    label: '机动车登记证',
    field: 'annexVehicleLicensePer',
    component: 'Input',
    show: false,
  },
  {
    label: '添加更多',
    field: 'annexPersonalAssetsMore',
    component: 'Input',
    show: false,
  },
  {
    label: '身份证、结婚证（自然人实控人、自然人担保方）',
    field: 'annexIdMarriage',
    component: 'Input',
    slot: 'updateImage',
    buss: {
      more: true,
      imageUploads: [
        {
          key: 'annexIdUrl1',
          fileMax: 1,
          text: '实控人信息页',
        },
        {
          key: 'annexIdUrl2',
          fileMax: 1,
          text: '实控人国徽页',
        },
        {
          key: 'annexIdUrl3',
          fileMax: 1,
          text: '实控人手持身份证',
        },
        {
          key: 'annexMarriageUrl',
          fileMax: 1,
          text: '实控人结婚证',
        },
      ],
      moreItem: {
        key: 'annexIdMarriageMore',
        fileMax: 5,
        text: '添加更多',
      },
      descText: '核原件，提供复印件（加盖公章），支持JPG、JPEG、PNG、PDF、ZIP格式，大小不超过50M。',
    },
    itemProps: {
      wrapperCol: { sm: 24 },
    },
  },
  {
    label: '实控人信息页',
    field: 'annexIdUrl1',
    component: 'Input',
    show: false,
  },
  {
    label: '实控人国徽页',
    field: 'annexIdUrl2',
    component: 'Input',
    show: false,
  },
  {
    label: '实控人手持身份证',
    field: 'annexIdUrl3',
    component: 'Input',
    show: false,
  },
  {
    label: '实控人结婚证',
    field: 'annexMarriageUrl',
    component: 'Input',
    show: false,
  },
  {
    label: '添加更多',
    field: 'annexIdMarriageMore',
    component: 'Input',
    show: false,
  },
  {
    label: '其他补充资料',
    field: 'annexAdditionalInformation',
    component: 'Input',
    slot: 'updateImage',
    buss: {
      more: true,
      moreItem: {
        key: 'annexAdditionalInformation',
        fileMax: 5,
        text: '添加更多',
      },
      descText: '支持JPG、JPEG、PNG、PDF、ZIP格式，大小不超过50M。',
    },
    itemProps: {
      wrapperCol: { sm: 24 },
    },
  },
];

export const auditFormSchema: FormSchema[] = [
  ...commonFormSchema,
  {
    label: '审核结果',
    field: 'auditResult',
    component: 'ApiRadioGroup',
    required: true,
    componentProps: {
      api: initDictOptions,
      params: 'dis_audit_result',
    },
  },
  {
    label: '审核意见',
    field: 'reviewComments',
    component: 'InputTextArea',
    componentProps: {
      maxlength: 500,
    },
    required: false,
  },
  {
    label: '审核日志',
    field: 'basicRealName',
    component: 'Input',
    slot: 'table',
    colProps: { span: 20 },
    itemProps: {
      wrapperCol: { sm: 24 },
    },
  },
];

export const auditColumns: BasicColumn[] = [
  {
    title: '操作时间',
    dataIndex: 'operateTime',
  },
  {
    title: '操作人',
    dataIndex: 'operateUser',
    // operateUserWorkNo
    customRender({ record }) {
      return `${record?.operateUser}/${record?.operateUserWorkNo}`;
    },
  },
  {
    title: '操作日志',
    dataIndex: 'operateResult',
  },
  {
    title: '审核意见',
    dataIndex: 'reviewComment',
  },
];