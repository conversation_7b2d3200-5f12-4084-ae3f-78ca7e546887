<template>
  <a-spin :spinning="pageLoading">
    <div class="vehicle-management-gps">
      <BasicTable @register="registerTable">
        <template #tableTitle>
          <a-button v-if="hasPermission('externalGps:export')" type="primary" @click="handleExport">导出列表</a-button>
        </template>
        <template #action="{ record }">
          <TableAction :actions="getTableAction(record)" />
        </template>
      </BasicTable>
    </div>
  </a-spin>
</template>
<script setup lang="ts" name="vehicleManagement-gps">
  import { ActionItem, BasicTable, TableAction } from '@/components/Table';
  import { useListPage } from '@/hooks/system/useListPage';
  import { columns, searchFormSchema } from './index.data';
  import { onMounted, ref, reactive } from 'vue';
  import { getBrandTree } from '@/views/vehicleManagement/vehicleAssets/index.api';
  import { getEnterList } from '@/api/common/api';
  import { useGo } from '@/hooks/web/usePage';
  import { listApi, exportFiles } from './index.api';
  import { queryAllCity } from '@/api/common/api';
  import { usePermission } from '@/hooks/web/usePermission';
  import dayjs from 'dayjs';

  const { hasPermission } = usePermission();
  const go = useGo();

  const state = reactive({
    exportFilesParams: null,
  });

  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    tableProps: {
      api: listApi,
      rowKey: 'id',
      rowSelection: { type: 'checkbox' },
      columns: columns,
      size: 'small',
      showIndexColumn: true,
      formConfig: {
        schemas: searchFormSchema,
        labelWidth: 90,
        actionColOptions: { offset: 12 },
      },
      actionColumn: {
        width: 100,
        fixed: 'right',
      },
      beforeFetch: params => {
        if (params?.installationTime) {
          const handleArr = params.installationTime?.split(',');
          if (handleArr?.length >= 2) {
            params.installationTimeStart = handleArr[0] + ' 00:00:00';
            params.installationTimeEnd = handleArr[1] + ' 23:59:59';
          }
          delete params.installationTime;
        }
        if (params?.positioningTime) {
          const handleArr1 = params.positioningTime?.split(',');
          if (handleArr1?.length >= 2) {
            params.positioningTimeStart = handleArr1[0] + ' 00:00:00';
            params.positioningTimeEnd = handleArr1[1] + ' 23:59:59';
          }
          delete params.positioningTime;
        }
        params.carPlateList =
          params.carPlateList && params.carPlateList.length ? params.carPlateList.replace(/\n|\r\n/g, ',') : void 0;
        params.vinList = params.vinList && params.vinList.length ? params.vinList.replace(/\n|\r\n/g, ',') : void 0;
        if (params.carPlateList1) {
          params.carPlateList = params.carPlateList1;
          delete params.carPlateList1;
        }
        if (params.vinList1) {
          params.vinList = params.vinList1;
          delete params.vinList1;
        }

        const { ...otherParams } = params;
        state.exportFilesParams = { ...otherParams };
      },
    },
  });

  //注册table数据
  const [registerTable, { getForm }] = tableContext;
  const pageLoading = ref<boolean>(false);
  const handleExport = async () => {
    pageLoading.value = true;
    await exportFiles(state.exportFilesParams);
    pageLoading.value = false;
  };
  const handleDetail = record => {
    const url = `/vehicleManagement/thirdPartGPS/detail?id=${record?.id}&supId=${record.supplierId}&carPlate=${
      record.carPlate || ''
    }&vin=${record?.vin || ''}`;
    go(url);
  };
  /**
   * 操作栏
   */
  function getTableAction(record): ActionItem[] {
    //这里应该是最多显示3个操作按钮，如果操作＞3个时，第三个按钮变成更多，点击更多展开其它操作按钮，这样可以保证更多按钮展开时显示至少两个按钮
    //按照现有逻辑没有更多
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
        ifShow: hasPermission('externalGps:info'),
      },
    ];
  }

  const getFormOptions = async () => {
    const { updateSchema, setFieldsValue } = getForm();
    let carBrandOptions = [];
    let handleRes = [],
      handleRes1 = [],
      handleRes2 = [];
    try {
      const treeRes = await getBrandTree();
      // console.log(treeRes, 'treeRes');
      carBrandOptions = treeRes.data || [];
      if (treeRes?.data?.length) {
        handleRes = treeRes.data.map((item: any) => {
          return { value: item?.vehicleBrandId || '', label: item?.vehicleBrand || '' };
        });
      }
    } catch (err) {
      console.log(err, 'treeRes err');
    }
    try {
      const enterpriseRes1 = await getEnterList({ tags: 3 });
      console.log(enterpriseRes1, 'enterpriseRes1');
      handleRes1 =
        enterpriseRes1?.records.map(item => {
          return { value: item?.id || '', label: item?.entName || '' };
        }) || [];
    } catch (err) {
      console.log(err, 'enterpriseRes1 err');
    }
    try {
      const enterpriseRes2 = await getEnterList({ tags: 4 });
      // console.log(enterpriseRes2, 'enterpriseRes2');
      handleRes2 =
        enterpriseRes2?.records.map(item => {
          return { value: item?.id || '', label: item?.entName || '' };
        }) || [];
    } catch (err) {
      console.log(err, 'enterpriseRes2 err');
    }

    updateSchema([
      {
        field: 'brandId',
        componentProps: {
          options: handleRes,
          placeholder: '车辆品牌',
          onChange: e => {
            setFieldsValue({ modelId: undefined });
            if (e) {
              const find = carBrandOptions.find(item => item.vehicleBrandId == e);
              let handleRes = (find?.modelList || []).map(item => {
                return { value: item?.id || '', label: item?.model || '' };
              });
              updateSchema([{ field: 'modelId', componentProps: { options: handleRes } }]);
            } else {
              updateSchema([{ field: 'modelId', componentProps: { options: [] } }]);
            }
          },
        },
      },
      { field: 'assetOwnershipId', componentProps: { options: handleRes1 } },
      { field: 'operationOwnershipId', componentProps: { options: handleRes2 } },
    ]);
  };
  const init = async () => {
    const { updateSchema } = getForm();
    queryAllCity({}).then(res => {
      // const handleRes = handleResToOptions(res)
      updateSchema([
        {
          field: 'operationCityCode',
          componentProps: { apiOptions: res, isAreaSelect: true, showArea: false, placeholder: '运营城市' },
        },
      ]);
    });
    await getFormOptions();
  };

  onMounted(async () => {
    const { setFieldsValue } = getForm();
    await setFieldsValue({
      positioningTime: [dayjs().subtract(89, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
    });
    await init();
  });
</script>
<style scoped lang="less">
  .vehicle-management-gps {
    :deep(.word-multiple-item) {
      width: 110px;
    }
  }
</style>
