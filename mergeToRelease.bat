@echo off
SETLOCAL ENABLEDELAYEDEXPANSION

FOR /F "tokens=*" %%B IN ('git rev-parse --abbrev-ref HEAD') DO SET CURRENT_BRANCH=%%B

echo current banch: %CURRENT_BRANCH%

echo switch to the release branch and pull the latest code...
git checkout release
git pull origin release

echo merge %CURRENT_BRANCH% branch to the release...
git merge %CURRENT_BRANCH%
IF ERRORLEVEL 1 (
    echo merge fail, please check for conflicts
    EXIT /B 1
)

echo push the release branch to the remote end...
git push origin release

echo Return to the %CURRENT_BRANCH% branch...
git checkout %CURRENT_BRANCH%

echo Operation completed. 
ENDLOCAL