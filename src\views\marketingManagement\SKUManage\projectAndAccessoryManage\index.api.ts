import { defHttp } from '/@/utils/http/axios';
import { downloadXlsTools } from '/@/utils/file/download';

import { UploadFileParams } from '/#/axios';
enum Api {
  excelModel = '/sys/common/getImportTemplate',

  list = '/market/category/list',
  // sku管理-新增
  skuAdd = '/market/sku/add', // post
  // sku管理-删除
  skuDelete = '/market/sku/delete', // delete
  // sku管理-禁用
  skuDisable = '/market/sku/disable', // get
  // sku管理-启用
  skuEnable = '/market/sku/enable', // get
  // sku管理-导出
  skuExportExcel = '/market/sku/exportExcel', // post
  // sku管理-获取详情
  skuGetInfo = '/market/sku/getInfo', // get
  // sku管理-导入检测
  skuImportCheck = '/ov/api/market/sku/importCheck', // post
  // sku管理-导入
  skuImportExcel = '/ov/api/market/sku/importExcel', // post
  // sku管理-分页查询
  skuPage = '/market/sku/page', // get
  // sku管理-修改
  skuUpdate = '/market/sku/update', // post

  // project
  // "market:sku1:add",
  // "market:sku1:delete",
  // "market:sku1:disable",
  // "market:sku1:enable",
  // "market:sku1:exportExcel",
  // "market:sku1:getInfo",
  // "market:sku1:importCheck",
  // "market:sku1:import",
  // "market:sku1:page",
  // "market:sku1:publicPage",
  // "market:sku1:update",

  // accessory
  // "market:sku2:add",
  // "market:sku2:delete",
  // "market:sku2:disable",
  // "market:sku2:enable",
  // "market:sku2:exportExcel",
  // "market:sku2:getInfo",
  // "market:sku2:importCheck",
  // "market:sku2:import",
  // "market:sku2:page",
  // "market:sku2:publicPage",
  // "market:sku2:update",
}

// 列表接口
export const skuPageList = (params: Record<string, any>) => {
  console.log("🚀 ~ skuPageList ~ params:", params)
  
  return defHttp.get({ url: Api.skuPage, params })
};
// 类目列表接口
export const list = (params: Record<string, any>) => defHttp.get({ url: Api.list, params });

//更新接口
export const saveOrUpdate = (params: Record<string, any>, isUpdate: Boolean) => {
  const url = isUpdate ? Api.skuUpdate : Api.skuAdd;
  return defHttp.post({ url: url, params });
};

//删除接口
export const deleteItemById = async (params: Record<string, any>, handleSuccess: Fn<any>) => {
  await defHttp.delete({ url: Api.skuDelete, params }, { joinParamsToUrl: true });
  handleSuccess();
};
export const getImportUrl = Api.skuImportExcel;

// 根据id获取详情
export const getDetailbyId = (params: Record<string, any>) => defHttp.get({ url: Api.skuGetInfo, params });

/**
 * 导出接口
 * @param params
 */
export const downloadXls = params => {
  downloadXlsTools(Api.skuExportExcel, params, '项目SKU');
};
//禁用接口
export const disabledDate = async (params: Record<string, any>) => {
  await defHttp.get({ url: Api.skuDisable, params });
};
//恢复接口
export const enableDate = async (params: Record<string, any>) => {
  await defHttp.get({ url: Api.skuEnable, params });
};

import { useMessage } from '/@/hooks/web/useMessage';
const { createMessage, createWarningModal } = useMessage();
import { useGlobSetting } from '/@/hooks/setting';

const glob = useGlobSetting();
// 检查导入表格
export const checkImportExcel = (data, skuType, success) => {
  const isReturn = fileInfo => {
    console.log('🚀 ~ isReturn ~ fileInfo:', fileInfo);
    console.log(fileInfo);

    try {
      if (fileInfo.code === 201) {
        const {
          message,
          result: { msg, fileUrl, fileName },
        } = fileInfo;
        const href = glob.uploadUrl + fileUrl;
        createWarningModal({
          title: message,
          centered: false,
          content: `<div>
                    <span>${msg}</span><br/> 
                    <span>具体详情请<a href = ${href} download = ${fileName}> 点击下载 </a> </span> 
                  </div>`,
        });
        //update-begin---author:wangshuai ---date:20221121  for：[VUEN-2827]导入无权限，提示图标错误------------
      } else if (fileInfo.code === 500 || fileInfo.code === 510) {
        createMessage.error(fileInfo.message || `${data.name} 导入失败`);
        //update-end---author:wangshuai ---date:20221121  for：[VUEN-2827]导入无权限，提示图标错误------------
      } else {
        createMessage.success(fileInfo.message || `${data.name} 文件上传成功`);
      }
    } catch (error) {
      console.log('导入的数据异常', error);
    } finally {
      typeof success === 'function' ? success(fileInfo) : '';
    }
  };
  return defHttp.uploadFile({ url: `${Api.skuImportCheck}?skuType=${skuType}` }, { file: data }, { success: isReturn });
};
// http://**************:9000/ov-portal-pub/marketTemplate/项目sku导入模板.xlsx
// 下载模板
export const getExcelModel = params =>
  defHttp.get({ url: Api.excelModel, params, responseType: 'blob' }, { isTransformResponse: false });

/**
 * @description: Upload interface
 */
export function skuImportExcel(params: UploadFileParams) {
  return defHttp.uploadFile(
    {
      url: Api.excelModel,
    },
    params,
    { isReturnResponse: true }
  );
}