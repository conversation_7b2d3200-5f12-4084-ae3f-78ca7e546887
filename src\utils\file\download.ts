import { openWindow } from '..';
import { dataURLtoBlob, urlToBase64 } from './base64Conver';

/**
 * Download online pictures
 * @param url
 * @param filename
 * @param mime
 * @param bom
 */
export function downloadByOnlineUrl(url: string, filename: string, mime?: string, bom?: BlobPart) {
  urlToBase64(url).then(base64 => {
    downloadByBase64(base64, filename, mime, bom);
  });
}

/**
 * Download pictures based on base64
 * @param buf
 * @param filename
 * @param mime
 * @param bom
 */
export function downloadByBase64(buf: string, filename: string, mime?: string, bom?: BlobPart) {
  const base64Buf = dataURLtoBlob(buf);
  downloadByData(base64Buf, filename, mime, bom);
}

/**
 * Download according to the background interface file stream
 * @param {*} data
 * @param {*} filename
 * @param {*} mime
 * @param {*} bom
 */
export function downloadByData(data: BlobPart, filename: string, mime?: string, bom?: BlobPart) {
  const blobData = typeof bom !== 'undefined' ? [bom, data] : [data];
  const blob = new Blob(blobData, { type: mime || 'application/octet-stream' });
  if (typeof window.navigator.msSaveBlob !== 'undefined') {
    window.navigator.msSaveBlob(blob, filename);
  } else {
    const blobURL = window.URL.createObjectURL(blob);
    const tempLink = document.createElement('a');
    tempLink.style.display = 'none';
    tempLink.href = blobURL;
    tempLink.setAttribute('download', filename);
    if (typeof tempLink.download === 'undefined') {
      tempLink.setAttribute('target', '_blank');
    }
    document.body.appendChild(tempLink);
    tempLink.click();
    document.body.removeChild(tempLink);
    window.URL.revokeObjectURL(blobURL);
  }
}

/**
 * Download file according to file address
 * @param {*} sUrl
 */
export function downloadByUrl({ url, target = '_blank', fileName }: { url: string; target?: TargetContext; fileName?: string }): boolean {
  const isChrome = window.navigator.userAgent.toLowerCase().indexOf('chrome') > -1;
  const isSafari = window.navigator.userAgent.toLowerCase().indexOf('safari') > -1;

  if (/(iP)/g.test(window.navigator.userAgent)) {
    console.error('Your browser does not support download!');
    return false;
  }
  if (isChrome || isSafari) {
    const link = document.createElement('a');
    link.href = url;
    link.target = target;

    if (link.download !== undefined) {
      link.download = fileName || url.substring(url.lastIndexOf('/') + 1, url.length);
    }

    if (document.createEvent) {
      const e = document.createEvent('MouseEvents');
      e.initEvent('click', true, true);
      link.dispatchEvent(e);
      return true;
    }
  }
  if (url.indexOf('?') === -1) {
    url += '?download';
  }

  openWindow(url, { target });
  return true;
}

import { useMessage } from '/@/hooks/web/useMessage';
import { defHttp } from '/@/utils/http/axios';
const { createMessage } = useMessage();

export const downloadXlsTools = async (apiUrl, params, name='导出文件') => {
  const data = await defHttp.post({ url: apiUrl, params, responseType: 'blob' }, { isTransformResponse: false, isReturnNativeResponse: true });

  const contentDisposition = data.headers['content-disposition'];

  if (!data?.data) {
    createMessage.warning('文件下载失败');
    return;
  }
  const blobOptions = { type: 'application/vnd.ms-excel' };
  const url = window.URL.createObjectURL(new Blob([data?.data], blobOptions));

  if (contentDisposition) {
    const matches = contentDisposition.match(/filename="?([^"]+)"?/);
    if (matches != null && matches[1]) {
      const decodeName = decodeURIComponent(matches[1]); // 解码文件名，支持中文
      const bytes = new Uint8Array(decodeName.split('').map(char => char.charCodeAt(0)));
      name = new TextDecoder('utf-8').decode(bytes);
    }
  }

  const link = document.createElement('a');
  link.style.display = 'none';
  link.href = url;
  link.setAttribute('download', name);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link); //下载完成移除元素
  window.URL.revokeObjectURL(url); //释放掉blob对象
};

export function downloadPDFFile(data: BlobPart, fileName: string) {
  downLoadFile(data, fileName, 'application/pdf');
}

export function downLoadFile(data: BlobPart, fileName: string, mimeType?: string) {
  const blob = new Blob([data], { type: mimeType || 'application/pdf' });
  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = fileName; // 设置下载文件名
  document.body.appendChild(a);
  a.click();
  window.URL.revokeObjectURL(url);
  document.body.removeChild(a);
}
export function isBlob(data) {
  if (data.size > 0 && /^(application\/octet-stream)/.test(data.type)) {
    return true;
  } else {
    return false;
  }
}
export function handleJson(text) {
  console.log("🚀 ~ handleJson ~ text:", text)
  try {
    // 尝试解析为 JSON
    const json = JSON.parse(text);
    console.log("🚀 ~ handleJson ~ json:", json)
    if (json.code || json.message) {
      // 处理服务器返回的错误信息
      console.error('Server error:', json.message);
      return json;
    }
  } catch (e) {
    // 不是 JSON，可能是纯文本错误
    return text;
  }
}
