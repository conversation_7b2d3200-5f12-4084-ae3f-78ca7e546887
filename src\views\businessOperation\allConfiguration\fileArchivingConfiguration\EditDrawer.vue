<template>
  <BasicDrawer
    @register="registerDrawer"
    :title="title"
    :width="adaptiveWidth"
    @ok="handleSubmit"
    :showFooter="showFooter"
    destroyOnClose
  >
    <BasicForm @register="registerForm" />
  </BasicDrawer>
</template>
<script lang="ts" setup>
  import { ref, computed, unref } from 'vue';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { formSchema } from './index.data';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { saveOrUpdate, cityListAll } from './index.api';
  import { useDrawerAdaptiveWidth } from '/@/hooks/jeecg/useAdaptiveWidth';
  import { ApprovalTemplateTypeOptions, ContractBusinessType } from '/@/enums/contract.enum';

  // 声明Emits
  const emit = defineEmits(['success']);
  const isUpdate = ref(true);
  //表单配置
  const [registerForm, { setProps, updateSchema, resetFields, setFieldsValue, validate }] = useForm({
    schemas: formSchema,
    labelWidth: 200,
    showActionButtonGroup: false,
  });

  const cityListOptions = ref<any>([]);
  cityListAll().then(res => {
    console.log('🚀 cityListAll ~ res:', res);
    cityListOptions.value = res;
  });
  const showFooter = ref(true);

  //表单赋值
  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async data => {
    console.log('是否带过来数据...', data, data.record);
    await resetFields();

    updateSchema({
      field: 'templateType',
      componentProps: {
        options: ApprovalTemplateTypeOptions,
        onChange: e => {
          setFieldsValue({ businessType: undefined });
          let options = [...ContractBusinessType];
          if (e == 2 || e == 4) {
            options = options.filter(item => item.value === '01');
          }
          updateSchema({
            field: 'businessType',
            componentProps: {
              options,
            },
            show: [1, 2, 3, 4].includes(e),
          });
        },
      },
    });
    console.log('🚀 ~ cityListOptions.value:', cityListOptions.value);

    updateSchema({
      field: 'otherType',
      componentProps: {
        options: cityListOptions.value,
        fieldNames: {
          label: 'cityName',
          value: 'cityName',
        },
      },
    });
    showFooter.value = data?.showFooter ?? true;
    setDrawerProps({ confirmLoading: false, showFooter: showFooter.value });
    isUpdate.value = !!data?.isUpdate;

    // 无论新增还是编辑，都可以设置表单值
    if (typeof data.record === 'object') {
      setFieldsValue({
        ...data.record,
      });
      const templateType = data.record.templateType;
      let options = [...ContractBusinessType];
      if (templateType == 2 || templateType == 4) {
        options = options.filter(item => item.value === '01');
      }
      updateSchema({
        field: 'businessType',
        componentProps: {
          options,
        },
      });
    }
    setProps({ disabled: !showFooter.value });
  });
  //获取标题
  const title = computed(() => {
    if (unref(showFooter) == false) {
      return '详情';
    } else {
      return !unref(isUpdate) ? '新增' : '编辑';
    }
  });

  const { adaptiveWidth } = useDrawerAdaptiveWidth();

  //提交事件
  async function handleSubmit() {
    try {
      let values = await validate();
      setDrawerProps({ confirmLoading: true });
      let isUpdateVal = unref(isUpdate);
      //提交表单
      await saveOrUpdate({ ...values }, isUpdateVal);
      //关闭弹窗
      closeDrawer();
      //刷新列表
      emit('success', { isUpdateVal, values });
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
