<template>
  <a-spin :spinning="pageLoading">
    <div class="vehicle-management-electricFence__log">
      <BasicTable @register="registerTable">
        <template #tableTitle>
          <a-button v-auth="'veh:fence_log:export'" type="primary" @click="handleExport">导出列表</a-button>
        </template>
      </BasicTable>
    </div>
  </a-spin>
</template>
<script setup lang="ts" name="vehicleManagement-electricFence-log">
  import { onMounted, reactive, ref } from 'vue';
  import { BasicTable } from '@/components/Table';
  import { useListPage } from '@/hooks/system/useListPage';
  import { all, logExportFiles, logListApi } from './index.api';
  import { logColumns, logSearchFormSchema } from './index.data';
  import dayjs from 'dayjs';

  const state = reactive({
    exportFilesParams: null,
  });
  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    tableProps: {
      api: logListApi,
      rowKey: 'id',
      rowSelection: { type: 'checkbox' },
      columns: logColumns,
      size: 'small',
      showIndexColumn: true,
      formConfig: {
        schemas: logSearchFormSchema,
        labelWidth: 100,
        actionColOptions: { offset: 12 },
      },
      showActionColumn: false,
      beforeFetch: params => {
        if (params?.reportTime) {
          const handleArr = params.reportTime?.split(',');
          if (handleArr?.length >= 2) {
            params.startTime = handleArr[0] + ' 00:00:00';
            params.endTime = handleArr[1] + ' 23:59:59';
          }
          delete params.reportTime;
        }
        params.column = 'alarmTime';
        params.carPlates =
          params.carPlates && params.carPlates.length ? params.carPlates.replace(/\n|\r\n/g, ',').split(',') : [];
        params.vins = params.vins && params.vins.length ? params.vins.replace(/\n|\r\n/g, ',').split(',') : [];
        const { pageNo, pageSize, ...otherParams } = params;
        state.exportFilesParams = { ...otherParams };
      },
    },
  });

  //注册table数据
  const [registerTable, { getForm }] = tableContext;

  const pageLoading = ref<boolean>(false);
  const handleExport = async () => {
    pageLoading.value = true;
    await logExportFiles(state.exportFilesParams);
    pageLoading.value = false;
  };

  const getFormOptions = async () => {
    const { updateSchema } = getForm();
    let fenceIdOptions = [];
    try {
      const res = await all();
      // console.log(res,'res');
      if (res?.length) {
        fenceIdOptions = res.map((item: any) => {
          return { value: item?.fenceName || '', label: item?.fenceName || '' };
        });
      }
      updateSchema([
        {
          field: 'fenceName',
          componentProps: {
            options: fenceIdOptions,
          },
        },
      ]);
    } catch (err) {
      console.log(err, 'fenceName err');
    }
  };

  const init = async () => {
    await getFormOptions();
  };

  onMounted(async () => {
    const { setFieldsValue } = getForm();
    setFieldsValue({ reportTime: [dayjs().subtract(29, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')] });
    await init();
  });
</script>
<style scoped lang="less">
  .vehicle-management-electricFence__log {
  }
</style>
