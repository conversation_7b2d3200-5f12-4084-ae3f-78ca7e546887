//任务单信息
export interface ITaskInfo {
  id: string;
  // 上游订单编号
  upstreamOrderNo: string;
  // 替换车单编号
  replaceOrderNo: string;
  // 交车状态
  deliveryStatus: number;
  // 审核状态
  approvalStatus: number;
  lesseeId: string;
  lesseeName: string; // 承租方名称
  lessorName: string; // 出租方名称
  customerName: string; // 客户提车人
  requestPickupDate: string; // 要求提车时间
  applicationType: string; // 申请类型
  applicationCause: string; // 申请原因
  // 车型名称
  modelName: string;
  // 车牌号
  oldVehiclePlate: string;
  // 车架号
  oldVin: string;
  // 车牌号
  vehiclePlate: string;
  // 车架号
  vin: string;
  // 交车人
  deliveryPerson: string;
  // 场站
  stationName: string;
  // 创建时间
  createTime: string;
  // 要求提车时间
  pickupTime: string;
  pickupDate: string;
  // 退车/作废原因
  reason: string;
  // 验车信息
  checkImgs: '';
  slaInfo: Array<any>;
  history: Array<any>;
  workOrderLogs?: any[]; // 任务单日志（编辑）
  orderAttributionSalesName: string;
}
