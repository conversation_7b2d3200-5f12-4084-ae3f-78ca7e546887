<template>
  <div class="word-multiple-box">
    <span class="word-multiple-item" :class="{ selected: isSelected(item) }" v-for="item in options" :key="item.key" @click="toggleSelect(item)">
      {{ item.title }}
    </span>
  </div>
</template>

<script lang="ts">
  import { defineComponent, ref, watch } from 'vue';

  export default defineComponent({
    name: 'MultipleTextSelector',
    props: {
      modelValue: {
        type: Array as () => string[],
        default: () => [],
      },
      options: {
        type: Array as () => { key: string; title: string; dataIndex: string }[],
        required: true,
      },
    },
    emits: ['update:modelValue'],
    setup(props, { emit }) {
      const selectedKeys = ref<string[]>(props.modelValue);

      const isSelected = (item: { key: string }) => {
        return selectedKeys.value.includes(item.key);
      };

      const toggleSelect = (item: { key: string; title: string; dataIndex: string }) => {
        if (item.dataIndex === 'all') {
          // 选择“全部”时清空所有选项
          if (selectedKeys.value.includes('1')) {
            selectedKeys.value = [];
          } else {
            selectedKeys.value = [item.key];
          }
        } else {
          // 取消“全部”选择
          selectedKeys.value = selectedKeys.value.filter((key) => key !== '1');
          const index = selectedKeys.value.indexOf(item.key);
          if (index === -1) {
            selectedKeys.value.push(item.key); // 添加新选择
          } else {
            selectedKeys.value.splice(index, 1); // 取消选择
          }
        }

        // 更新父组件的 v-model 数据
        emit('update:modelValue', selectedKeys.value);
      };

      // 同步外部 modelValue 的变化
      watch(
        () => props.modelValue,
        (newValue) => {
          selectedKeys.value = [...newValue];
        }
      );

      return {
        selectedKeys,
        isSelected,
        toggleSelect,
      };
    },
  });
</script>

<style scoped>
  .word-multiple-box {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  .word-multiple-item {
    padding: 6px 12px;
    /* border: 1px solid #d9d9d9; */
    border-radius: 4px;
    cursor: pointer;
    user-select: none;
  }

  .word-multiple-item.selected {
    /* background-color: #409eff; */
    /* color: #fff; */
    color: #409eff;
    /* border-color: #409eff; */
  }
</style>
