<!-- eslint-disable prettier/prettier -->
<template>
  <Form
    v-bind="getBindValue"
    :class="getFormClass"
    ref="formElRef"
    :model="formModel"
    @keypress.enter="handleEnterPress"
  >
    <Row v-bind="getRow" style="display: flex">
      <slot name="formHeader"></slot>
      <div style="flex: 1">
        <Col :span="24">
          <Row v-bind="getRow">
            <template v-for="schema in getSchema" :key="schema.field">
              <FormItem
                :formActionType="formActionType"
                :schema="schema"
                :formProps="getProps"
                :allDefaultValues="defaultValueRef"
                :formModel="formModel"
                :setFormModel="setFormModel"
                :showItemByQuickSearch="openQuickSearchRef || !schema.hideByQuickSearchHide"
              >
                <template #[item]="data" v-for="item in Object.keys($slots)">
                  <slot :name="item" v-bind="data || {}"></slot>
                </template>
              </FormItem>
            </template>
          </Row>
        </Col>
      </div>
      <!-- 新增，按钮不换行 -->
      <FormAction
        v-if="getProps.reference === 'table'"
        style="display: inline-block; width: 230px; white-space: nowrap"
        v-bind="getFormActionBindProps"
        @toggle-advanced="handleToggleAdvanced"
      >
        <template #[item]="data" v-for="item in ['resetBefore', 'submitBefore', 'advanceBefore', 'advanceAfter']">
          <slot :name="item" v-bind="data || {}"></slot>
        </template>
      </FormAction>
      <slot name="formFooter"></slot>
    </Row>
    <div class="quick-container" style="display: flex" v-if="getQuickSchema.length" v-show="openQuickSearchRef">
      <div class="quick-title" :style="{ width: `${getProps.labelWidth}px`, fontSize: '13px', paddingTop: '6px'}" v-if="!getQuickSchema.some(i => i.hideQuickTitle)"> 快捷查询 </div>
      <div style="flex: 1">
        <Row v-bind="getQuickStyle" v-for="(item, $index) in getQuickSchema" :key="$index">
          <FormItem
            v-for="(i, index) in item.componentList"
            :key="index"
            :tableAction="tableAction"
          :formActionType="formActionType"
            :schema="i"
            :formProps="getProps"
            :allDefaultValues="defaultValueRef"
            :formModel="formModel"
            :setFormModel="setFormModel"
          >
            <template #[item]="data" v-for="item in Object.keys($slots)">
              <slot :name="item" v-bind="data || {}"></slot>
            </template>
          </FormItem>
        </Row>
      </div>
      <div :style="{marginRight: `${230 - (Number(getProps.labelWidth) || 0)}px`}"></div>
    </div>
    <div v-if="showQuickSearchHideButton" style="text-align: center; position: relative; top: -10px">
      <UpOutlined v-if="openQuickSearchRef" @click="() => (openQuickSearchRef = !openQuickSearchRef)" />
      <DownOutlined v-if="!openQuickSearchRef" @click="() => (openQuickSearchRef = !openQuickSearchRef)" />
    </div>
  </Form>
</template>
<script lang="ts">
  import type { Ref } from 'vue';
  import type { FormActionType, FormProps, FormSchema } from './types/form';
  import type { AdvanceState } from './types/hooks';

  import { Col, Form, Row } from 'ant-design-vue';
  import { computed, defineComponent, nextTick, onMounted, reactive, ref, unref, watch } from 'vue';
  import FormAction from './components/FormAction.vue';
  import FormItem from './components/FormItem.vue';

  import { dateItemType } from './helper';
  import { dateUtil } from '/@/utils/dateUtil';

  // import { cloneDeep } from 'lodash-es';
  import { deepMerge } from '/@/utils';

  import useAdvanced from './hooks/useAdvanced';
  import { useAutoFocus } from './hooks/useAutoFocus';
  import { createFormContext } from './hooks/useFormContext';
  import { useFormEvents } from './hooks/useFormEvents';
  import { useFormValues } from './hooks/useFormValues';
  import { useModalContext } from '/@/components/Modal';

  import { useDebounceFn } from '@vueuse/core';
  import dayjs from 'dayjs';
  import { basicProps } from './props';
  import { useDesign } from '/@/hooks/web/useDesign';

  import { UpOutlined, DownOutlined } from '@ant-design/icons-vue';

  export default defineComponent({
    name: 'BasicForm',
    components: { FormItem, Form, Row, Col, FormAction, UpOutlined, DownOutlined },
    props: basicProps,
    emits: ['advanced-change', 'reset', 'submit', 'register'],
    setup(props, { emit, attrs }) {
      const formModel = reactive<Recordable>({});
      const modalFn = useModalContext();

      const advanceState = reactive<AdvanceState>({
        // 默认是收起状态
        isAdvanced: false,
        hideAdvanceBtn: true,
        isLoad: false,
        actionSpan: 6,
      });

      const defaultValueRef = ref<Recordable>({});
      const isInitedDefaultRef = ref(false);
      const propsRef = ref<Partial<FormProps>>({});
      const schemaRef = ref<Nullable<FormSchema[]>>(null);
      const formElRef = ref<Nullable<FormActionType>>(null);
      const openQuickSearchRef = ref<boolean>(false);
      const { prefixCls } = useDesign('basic-form');

      // Get the basic configuration of the form
      const getProps = computed((): FormProps => {
        let mergeProps = { ...props, ...unref(propsRef) } as FormProps;
        //update-begin-author:sunjianlei date:20220923 for: 如果用户设置了labelWidth，则使labelCol失效，解决labelWidth设置无效的问题
        if (mergeProps.labelWidth) {
          mergeProps.labelCol = undefined;
        }
        //update-end-author:sunjianlei date:20220923 for: 如果用户设置了labelWidth，则使labelCol失效，解决labelWidth设置无效的问题
        return mergeProps;
      });

      const getFormClass = computed(() => {
        return [
          prefixCls,
          {
            [`${prefixCls}--compact`]: unref(getProps).compact,
          },
        ];
      });

      // Get uniform row style and Row configuration for the entire form
      const getRow = computed((): Recordable => {
        const { baseRowStyle = {}, rowProps } = unref(getProps);
        return {
          style: baseRowStyle,
          ...rowProps,
        };
      });

      const getQuickStyle = computed(() => {
        const { baseQuickRowStyle = {}, baseRowStyle = {}, rowProps } = unref(getProps);
        return {
          style: { ...baseQuickRowStyle, ...baseRowStyle },
          ...rowProps,
        };
      });
      
      // 是否展示快捷查询隐藏按钮
      const showQuickSearchHideButton = computed(() => {
        const showFlag = getMySchema.value.filter(i=> !!i.hideByQuickSearchHide).length > 0;
        return getQuickSchema.value.length > 0 || showFlag;
      });

      const getBindValue = computed(() => ({ ...attrs, ...props, ...unref(getProps) }) as Recordable);

      const getMySchema = computed((): FormSchema[] => {
        const schemas: FormSchema[] = unref(schemaRef) || (unref(getProps).schemas as any);
        for (const schema of schemas) {
          const { defaultValue, component, componentProps } = schema;
          // console.log(schema, '我是schema');
          // handle date type
          if (defaultValue && dateItemType.includes(component)) {
            //update-begin---author:wangshuai ---date:20230410  for：【issues/435】代码生成的日期控件赋默认值报错------------
            let valueFormat: string = '';
            if (componentProps) {
              valueFormat = componentProps?.valueFormat;
            }
            if (!valueFormat) {
              console.warn('未配置valueFormat,可能导致格式化错误！');
            }
            //update-end---author:wangshuai ---date:20230410  for：【issues/435】代码生成的日期控件赋默认值报错------------
            if (!Array.isArray(defaultValue)) {
              //update-begin---author:wangshuai ---date:20221124  for：[issues/215]列表页查询框（日期选择框）设置初始时间，一进入页面时，后台报日期转换类型错误的------------
              if (valueFormat) {
                schema.defaultValue = dateUtil(defaultValue).format(valueFormat);
              } else {
                schema.defaultValue = dateUtil(defaultValue);
              }
              //update-end---author:wangshuai ---date:20221124  for：[issues/215]列表页查询框（日期选择框）设置初始时间，一进入页面时，后台报日期转换类型错误的------------
            } else {
              const def: dayjs.Dayjs[] = [];
              defaultValue.forEach(item => {
                //update-begin---author:wangshuai ---date:20221124  for：[issues/215]列表页查询框（日期选择框）设置初始时间，一进入页面时，后台报日期转换类型错误的------------
                if (valueFormat) {
                  def.push(dateUtil(item).format(valueFormat));
                } else {
                  def.push(dateUtil(item));
                }
                //update-end---author:wangshuai ---date:20221124  for：[issues/215]列表页查询框（日期选择框）设置初始时间，一进入页面时，后台报日期转换类型错误的------------
              });
              schema.defaultValue = def;
            }
          }
        }
        if (unref(getProps).showAdvancedButton) {
          return schemas.filter(schema => schema.component !== 'Divider') as FormSchema[];
        } else {
          return schemas as FormSchema[];
        }
      });

      const getSchema = computed(() => {
        return getMySchema.value.filter(i => !i.isQuickSearch);
      });

      const getQuickSchema = computed(() => {
        return getMySchema.value.filter(i => !!i.isQuickSearch);
      });

      const { handleToggleAdvanced } = useAdvanced({
        advanceState,
        emit,
        getProps,
        getSchema,
        formModel,
        defaultValueRef,
      });

      const { handleFormValues, initDefault } = useFormValues({
        getProps,
        defaultValueRef,
        getSchema,
        formModel,
      });

      useAutoFocus({
        getSchema,
        getProps,
        isInitedDefault: isInitedDefaultRef,
        formElRef: formElRef as Ref<FormActionType>,
      });

      const {
        handleSubmit,
        setFieldsValue,
        clearValidate,
        validate,
        validateFields,
        getFieldsValue,
        updateSchema,
        resetSchema,
        appendSchemaByField,
        removeSchemaByFiled,
        resetFields,
        scrollToField,
      } = useFormEvents({
        emit,
        getProps,
        formModel,
        getSchema: getMySchema,
        defaultValueRef,
        formElRef: formElRef as Ref<FormActionType>,
        schemaRef: schemaRef as Ref<FormSchema[]>,
        handleFormValues,
      });

      createFormContext({
        resetAction: resetFields,
        submitAction: handleSubmit,
      });

      watch(
        () => unref(getProps).model,
        () => {
          const { model } = unref(getProps);
          if (!model) return;
          setFieldsValue(model);
        },
        {
          immediate: true,
        }
      );

      watch(
        () => unref(getProps).schemas,
        schemas => {
          resetSchema(schemas ?? []);
        }
      );

      watch(
        () => getSchema.value,
        schema => {
          nextTick(() => {
            //  Solve the problem of modal adaptive height calculation when the form is placed in the modal
            modalFn?.redoModalHeight?.();
          });
          if (unref(isInitedDefaultRef)) {
            return;
          }
          if (schema?.length) {
            initDefault();
            isInitedDefaultRef.value = true;
          }
        }
      );

      async function setProps(formProps: Partial<FormProps>): Promise<void> {
        propsRef.value = deepMerge(unref(propsRef) || {}, formProps);
      }

      //update-begin-author:taoyan date:2022-11-28 for: QQYUN-3121 【优化】表单视图问题#scott测试 8、此功能未实现
      const onFormSubmitWhenChange = useDebounceFn(handleSubmit, 300);

      function setFormModel(key: string, value: any) {
        formModel[key] = value;
        const { validateTrigger } = unref(getBindValue);
        if (!validateTrigger || validateTrigger === 'change') {
          validateFields([key]).catch(_ => {});
        }
        if (props.autoSearch === true) {
          onFormSubmitWhenChange();
        }
      }

      //update-end-author:taoyan date:2022-11-28 for: QQYUN-3121 【优化】表单视图问题#scott测试 8、此功能未实现

      function handleEnterPress(e: KeyboardEvent) {
        const { autoSubmitOnEnter } = unref(getProps);
        if (!autoSubmitOnEnter) return;
        if (e.key === 'Enter' && e.target && e.target instanceof HTMLElement) {
          const target: HTMLElement = e.target as HTMLElement;
          if (target && target.tagName && target.tagName.toUpperCase() == 'INPUT') {
            handleSubmit();
          }
        }
      }

      const formActionType: Partial<FormActionType> = {
        getFieldsValue,
        setFieldsValue,
        resetFields,
        updateSchema,
        resetSchema,
        setProps,
        getProps,
        removeSchemaByFiled,
        appendSchemaByField,
        clearValidate,
        validateFields,
        validate,
        submit: handleSubmit,
        scrollToField: scrollToField,
      };

      onMounted(() => {
        initDefault();
        emit('register', formActionType);
      });

      return {
        openQuickSearchRef,
        getBindValue,
        handleToggleAdvanced,
        handleEnterPress,
        formModel,
        defaultValueRef,
        advanceState,
        getRow,
        getQuickStyle,
        showQuickSearchHideButton,
        getProps,
        formElRef,
        getSchema,
        getQuickSchema,
        getMySchema,
        formActionType: formActionType as any,
        setFormModel,
        getFormClass,
        getFormActionBindProps: computed((): Recordable => ({ ...getProps.value, ...advanceState })),
        ...formActionType,
      };
    },
  });
  // 戒指 15 戒指 35 项链 手套 鞋用什么？ 护腕没有 肩膀？
</script>
<style lang="less">
  @prefix-cls: ~'@{namespace}-basic-form';

  .@{prefix-cls} {
    .ant-form-item {
      &-label label::after {
        margin: 0 6px 0 2px;
      }

      &-with-help {
        margin-bottom: 0;
      }

      &:not(.ant-form-item-with-help) {
        margin-bottom: 24px;
      }

      &.suffix-item {
        .ant-form-item-children {
          display: flex;
        }

        .ant-form-item-control {
          margin-top: 4px;
        }

        .suffix {
          display: inline-flex;
          padding-left: 6px;
          margin-top: 1px;
          line-height: 1;
          align-items: center;
        }
      }
    }

    /* 【美化表单】form的字体改小一号 */

    .ant-form-item-label > label {
      font-size: 13px;
    }

    .ant-form-item .ant-select {
      font-size: 13px;
    }

    .ant-select-item-option-selected {
      font-size: 13px;
    }

    .ant-select-item-option-content {
      font-size: 13px;
    }

    .ant-input {
      font-size: 13px;
    }

    /* 【美化表单】form的字体改小一号 */

    .ant-form-explain {
      font-size: 14px;
    }
  }

  .quick-title {
    &::after {
      content: ':';
      position: relative;
      top: -0.5px;
      margin: 0 6px 0 2px;
    }
  }
</style>
