<template>
  <div class="vehicle-management-gps__detail">
    <BasicForm class="vehicle-management-gps__detail__form" @register="registerForm">
      <template #formFooter>
        <a-button type="primary" @click="handleSearch">查询</a-button>
        <a-button @click="handleReset">重置</a-button>
      </template>
    </BasicForm>
    <div class="map-slot">
      <div class="container" ref="mapEl"></div>
      <div class="result">
        <Description @register="registerDesc" class="mt-4" />
      </div>
    </div>
  </div>
</template>
<script setup lang="ts">
  import { nextTick, onMounted, onUnmounted, reactive, ref, unref } from 'vue';
  import { useRoute } from 'vue-router';
  import { BasicForm, useForm } from '/@/components/Form';
  import { detailFormSchema, detailDescirptSchema } from './index.data';
  import { queryByIdApi } from './index.api';
  import { getSupplierList } from '/@/api/common/api';
  import { Description, useDescription } from '@/components/Description';
  import { useMaps } from '@/views/channelMerchants/manage/components/useMap';
  import car from '@/assets/images/car.png';
  import { useMessage } from '@/hooks/web/useMessage';

  const route = useRoute();
  const { creatMapTianDi } = useMaps();
  const map = ref(null);
  const mapEl = ref(null);
  const timer = ref<ReturnType<typeof setTimeout> | null>(null);
  const supplierList = ref<any[]>([]);
  const state = reactive({
    detailId: '',
    timeout: 30 * 1000,
    vin: '',
  });
  const { createMessage } = useMessage();
  const [registerForm, { resetFields, validate, setFieldsValue, updateSchema }] = useForm({
    labelAlign: 'left',
    labelWidth: 70,
    schemas: detailFormSchema,
    autoSubmitOnEnter: true,
  });
  const [registerDesc, { setDescProps }] = useDescription({
    title: '',
    column: 1,
    bordered: false,
    data: {},
    schema: detailDescirptSchema,
  });
  const handleSearch = async () => {
    clearInterval(timer.value!);
    timer.value = null;
    const values = await validate();
    if (!values?.carPlate && !values?.vin) {
      createMessage.warn('请输入搜索条件');
      return null;
    }
    await getDetail(true);
    timer.value = setInterval(async () => {
      await getDetail(true);
    }, state.timeout);
  };
  const handleReset = async () => {
    await resetFields();
    await setFieldsValue({
      supplierId: route?.query?.supId,
    });
    clearInterval(timer.value!);
    timer.value = null;
    // await getDetail();
    // timer.value = setInterval(async () => {
    //   await getDetail();
    // }, state.timeout);
  };
  const getSupList = async () => {
    await getSupplierList().then((res: any) => {
      supplierList.value =
        res.map(item => {
          return {
            label: item.name,
            value: item.id,
          };
        }) || [];
    });
  };

  const getDetail = async (search = false) => {
    if (!state.detailId && !search) {
      return null;
    }
    try {
      const values = await validate();
      let params: any = {
        id: state.detailId,
      };
      if (search) {
        params = values;
      }

      const res = await queryByIdApi(params);
      if (!res) {
        createMessage.warn('暂无数据');
        return null;
      }
      unref(map).clearOverLays();
      //创建对象
      let geocode = new T.Geocoder();
      const lnglat = new T.LngLat(res?.lon, res?.lat);
      geocode.getLocation(lnglat, () => {
        setDescProps({
          title: res?.carPlate,
          data: {
            ...res,
            latitude: res.lat,
            longitude: res.lon,
            // address: localRes.formatted_address,
          },
        });
      });
      //创建图片对象
      let icon = new T.Icon({
        iconUrl: car,
        iconSize: new T.Point(44, 100),
        iconAnchor: new T.Point(22, 50),
      });
      //创建标注对象
      const marker = new T.Marker(lnglat, { icon: icon });
      unref(map).panTo(lnglat, 14);
      //向地图上添加标注
      unref(map).addOverLay(marker);
    } catch (err) {
      console.log(err, 'err');
    }
  };
  onMounted(async () => {
    state.detailId = route?.query?.id as string;
    await resetFields();
    await getSupList();
    await nextTick(async () => {
      setFieldsValue({
        ...route.query,
        supplierId: String(route?.query?.supId),
      });
      updateSchema({
        field: 'supplierId',
        component: 'Select',
        componentProps: {
          options: supplierList.value,
          placeholder: '请选择定位数据源',
          allowClear: false,
        },
      });
    });
    map.value = await creatMapTianDi({ wrapRef: unref(mapEl) });
    await getDetail();
    timer.value = setInterval(async () => {
      await getDetail();
    }, state.timeout);
  });
  onUnmounted(() => {
    clearInterval(timer.value!);
    timer.value = null;
  });
</script>
<style lang="less">
  .mt-4 {
    .jeecg-basic-title {
      font-size: 20px;
    }
  }
  .result {
    .ant-descriptions-row > td {
      padding-bottom: 0px;
    }
  }
</style>
<style scoped lang="less">
  .vehicle-management-gps__detail {
    padding: 10px;
    &__form {
      padding: 12px 10px 6px;
      margin-bottom: 8px;
      border-radius: 2px;
      //padding-top;
      background: #fff;
      :deep(.ant-form-item:not(.ant-form-item-with-help)) {
        margin-bottom: 8px;
      }
    }
    .map-slot {
      width: 100%;
      height: calc(100vh - @header-height - @multiple-card-height - 66px - 20px);
      //background: #1a1a1a;
      position: relative;
      .container {
        width: 100%;
        height: 100%;
        max-width: none;
        //img {
        //  max-width: inherit;
        //}
      }
      .result {
        width: 300px;
        max-height: 80%;
        position: absolute;
        right: 10px;
        top: 0;
        z-index: 400;
        background: #fff;
        opacity: 0.8;
      }
    }
  }
</style>
