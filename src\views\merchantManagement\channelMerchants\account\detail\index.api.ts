import { defHttp } from '/@/utils/http/axios';

enum Api {
  list = '/dis/account/pageUserAccount',
  restoreDis = '/dis/account/restoreDisAccount',
  disableDis = '/dis/account/disableDisAccount',
}
/**
 * 列表
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

export const restoreDis = (params) => defHttp.get({ url: Api.restoreDis, params });

export const disableDis = (params) => defHttp.get({ url: Api.disableDis, params });


