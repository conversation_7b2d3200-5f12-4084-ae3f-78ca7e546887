import { useAppProviderContext } from '/@/components/Application';
import { computed, unref } from 'vue';
import { useAppStore } from '@/store/modules/app';

export function useAppInject() {
  const values = useAppProviderContext();
  const appStore = useAppStore();
  return {
    getIsMobile: computed(() => {
      if (appStore.isMobile !== undefined && appStore.isMobile !== null) {
        return appStore.isMobile;
      }
      return unref(values.isMobile);
    }),
  };
}
