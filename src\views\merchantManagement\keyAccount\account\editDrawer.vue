<template>
  <BasicDrawer
    v-bind="$attrs"
    @register="registerDrawer"
    title="编辑"
    :width="adaptiveWidth"
    @ok="handleSubmit"
    :showFooter="showFooter"
    destroyOnClose
  >
    <BasicForm @register="registerForm">
      <template #nameText="{ model, field }">
        {{ model[field] }}
      </template>
      <template #realNameText="{ model, field }">
        {{ model[field] }}
      </template>
      <template #phoneText="{ model, field }">
        {{ model[field] }}
      </template>
    </BasicForm>
  </BasicDrawer>
</template>
<script lang="ts" setup>
  import { ref, watch, nextTick } from 'vue';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { useDrawerAdaptiveWidth } from '/@/hooks/jeecg/useAdaptiveWidth';
  import { formSchema } from './index.data';
  import { editAccount } from './index.api';

  // 声明Emits
  const emit = defineEmits(['success', 'register']);

  //表单配置
  const [registerForm, { resetFields, setFieldsValue, validate, clearValidate }] = useForm({
    schemas: formSchema,
    // layout: 'vertical',
    labelWidth: 200,
    showActionButtonGroup: false,
  });

  const showFooter = ref(true);

  //表单赋值
  const [registerDrawer, { setDrawerProps, closeDrawer, getVisible }] = useDrawerInner(async data => {
    console.log('是否带过来数据...', data, data.record);
    await resetFields();
    // 无论新增还是编辑，都可以设置表单值
    setFieldsValue({
      ...data.record,
      basicPhone: data.record.phone,
    });
  });

  const { adaptiveWidth } = useDrawerAdaptiveWidth();

  watch(getVisible, () => {
    nextTick(() => {
      clearValidate();
    });
  });

  //提交事件
  async function handleSubmit() {
    try {
      console.log(validate);
      let values = await validate();
      const params = {
        id: values.id,
        basicPhone: values.basicPhone,
        basicPassword: values.basicPassword,
      };

      setDrawerProps({ confirmLoading: true });

      //提交表单
      await editAccount({ ...params });
      //关闭弹窗
      closeDrawer();
      //刷新列表
      emit('success', { values });
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
