import { defHttp } from '/@/utils/http/axios';

enum Api {
  list = '/vehWarningConfiguration/list', // 配置列表
  save = '/vehWarningConfiguration/add', // 新增配置
  edit = '/vehWarningConfiguration/edit', // 编辑配置
  delete = '/vehWarningConfiguration/del', // 删除配置
  enable = '/vehWarningConfiguration/enable', // 禁用/启用
  treeList = '/sys/sysDepart/getSysDepartAndUserTree',
  user = '/sys/sysDepart/getUserList',
}

/**
 * 列表接口
 * @param params
 */
export const list = params => defHttp.get({ url: Api.list, params });

export const saveOrUpdate = (params, isUpdate) => {
  const url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params });
};

export const handleDelete = (params, handleSuccess) => {
  return defHttp.get({ url: Api.delete, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};

export const handleChangeStatus = (params, handleSuccess) => {
  return defHttp.get({ url: Api.enable, params }).then(() => {
    handleSuccess();
  });
};

export const getTreeList = (params = {}) => defHttp.get({ url: Api.treeList, params });

export const getUserList = (params = {}) => defHttp.post({ url: Api.user, params });
