<template>
  <div class="channelMerchants__manage-list">
    <a-tabs v-if="state.tabsShow" class="manage-list__tabs" v-model:activeKey="state.activeKey" @change="handleChange">
      <a-tab-pane v-for="item in state.tabs" :key="item.key">
        <template #tab>
          <div class="tab-item__title-text">{{ item.title }}</div>
          <div class="tab-item__title-num">{{ item.num }}</div>
        </template>
      </a-tab-pane>
    </a-tabs>
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <template #tableTitle>
        <a-button v-auth="'dis:acc:add'" type="primary" preIcon="ant-design:plus-outlined" @click="handleCreate">
          新增大客户
        </a-button>
        <!-- <a-dropdown v-if="selectedRowKeys.length > 0 && hasPermission('dis:audit:export')">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="handleExport">
                <Icon icon="ant-design:export-outlined" />
                导出
              </a-menu-item>
            </a-menu>
          </template>
          <a-button
            >批量操作
            <Icon icon="mdi:chevron-down" />
          </a-button>
        </a-dropdown> -->
      </template>
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
    <EditDrawer @register="registerDrawer" @success="handleSuccess" @cancel="handleSuccess" />
  </div>
</template>
<script setup lang="ts" name="channelMerchants-manage">
  import { onMounted, reactive } from 'vue';
  import { useListPage } from '@/hooks/system/useListPage';
  import { ActionItem, TableAction, BasicTable } from '@/components/Table';
  import { useDrawer } from '@/components/Drawer';
  import { columns, searchFormSchema } from './index.data';
  import { deleteOne, list, queryStat } from './index.api';
  import EditDrawer from './components/EditDrawer.vue';
  import { usePermission } from '@/hooks/web/usePermission';
  import { useRouter } from 'vue-router';

  const { hasPermission } = usePermission();
  const router = useRouter();

  const state = reactive({
    tabsShow: true, //暂时隐藏
    tabs: [
      {
        id: 4,
        title: '全部',
        key: 'total',
        num: 384,
      },
      {
        id: 0,
        title: '草稿',
        key: 'draft',
        num: 384,
      },
      {
        id: 1,
        title: '待审核',
        key: 'toAudit',
        num: 384,
      },
      {
        id: 2,
        title: '审核通过',
        key: 'auditPass',
        num: 384,
      },
      {
        id: 3,
        title: '审核不通过',
        key: 'auditNotPass',
        num: 214,
      },
    ],
    activeKey: 'total',
    exportFilesParams: {
      ids: '',
    },
    reloadFirstPage: false,
  });
  const [registerDrawer, { openDrawer }] = useDrawer();
  // 列表页面公共参数、方法
  const { tableContext, onExportZip } = useListPage({
    tableProps: {
      api: list,
      rowKey: 'id',
      rowSelection: { type: 'checkbox' },
      columns: columns,
      size: 'small',
      formConfig: {
        schemas: searchFormSchema,
        labelWidth: 96,
      },
      actionColumn: {
        width: 280,
        fixed: 'right',
      },
      beforeFetch: params => {
        let currentTab = (state.tabs || []).filter(item => item.key === state.activeKey)[0];
        params.reviewStatus = currentTab?.id == 4 ? undefined : currentTab?.id;
      },
    },
  });

  //注册table数据
  const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext;

  onMounted(() => {
    getTabList();
  });
  const getTabList = async () => {
    try {
      const res = await queryStat();
      (state.tabs || []).map(item => {
        item.num = res[item.key] || 0;
      });
    } catch (err) {
      console.log(err, 'err');
    }
  };

  const handleChange = () => {
    reload({ page: 1 });
  };
  const handleExport = () => {
    state.exportFilesParams.ids = selectedRowKeys.value.toString();
    onExportZip();
  };
  const handleCreate = () => {
    state.reloadFirstPage = true;
    openDrawer(true, {
      isUpdate: false,
      showFooter: true,
      labelKey: 'create',
    });
  };

  const handleEdit = record => {
    openDrawer(true, {
      record,
      isUpdate: true,
      showFooter: true,
      labelKey: 'edit',
    });
  };
  const handleDetail = record => {
    router.push({
      path: '/merchantManagement/keyAccount/manage/detail',
      query: {
        id: record.id,
      },
    });
  };
  const handleAudit = record => {
    openDrawer(true, {
      record,
      activeKey: 'audit',
      isUpdate: true,
      showFooter: true,
      labelKey: 'audit',
    });
  };

  const handleDelete = async (record: Recordable) => {
    state.reloadFirstPage = true;
    await deleteOne({ id: record.id }, handleSuccess);
  };
  /**
   * 成功回调
   */
  function handleSuccess() {
    getTabList();
    //新增删除返回首页
    if (state.reloadFirstPage) {
      reload({ page: 1 });
      state.reloadFirstPage = false;
    } else {
      reload();
    }
  }

  /**
   * 操作栏
   */
  function getTableAction(record): ActionItem[] {
    //这里应该是最多显示3个操作按钮，如果操作＞3个时，第三个按钮变成更多，点击更多展开其它操作按钮，这样可以保证更多按钮展开时显示至少两个按钮
    //按照现有逻辑没有更多
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
        auth: 'dis:audit:info',
      },
      {
        label: '审核',
        onClick: handleAudit.bind(null, record),
        ifShow: record.reviewStatus == 1,
        auth: 'dis:acc:audit',
      },
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
        ifShow: record.reviewStatus != 1,
        auth: 'dis:acc:edit',
      },
      {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
        },
        auth: 'mdmKeyCustomers:del',
        ifShow: record.reviewStatus == 0,
      },
    ];
  }
</script>
<style scoped lang="less">
  .channelMerchants__manage-list {
    .manage-list {
      &__tabs {
        margin: 10px 10px 0;
        text-align: center;
        background: #fff;
        :deep(.ant-tabs-nav) {
          margin: 0;
          &::before {
            display: none;
          }
          .ant-tabs-ink-bar {
            display: none;
          }
          .ant-tabs-tab {
            padding-right: 32px;
            &:first-child {
              padding: 0 32px;
            }
          }
        }
        .tab-item {
          &__title {
            &-text {
            }
            &-num {
              margin-top: 18px;
              font-size: 18px;
              font-weight: bold;
            }
          }
        }
      }
    }
  }
</style>
