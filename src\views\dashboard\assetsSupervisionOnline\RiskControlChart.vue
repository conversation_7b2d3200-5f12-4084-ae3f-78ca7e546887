<template>
  <div ref="chartRef" :style="{ height: barHeight, width }" v-loading="loading"></div>
</template>

<script lang="ts" setup>
  import { ref, Ref, reactive, watchEffect, computed, nextTick } from 'vue';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { ChannelInfo } from './index.data';

  const props = defineProps({
    data: {
      type: Array as PropType<ChannelInfo[]>,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
    width: {
      type: String,
      default: '100%',
    },
    height: {
      type: String,
      default: '400px',
    },
  });
  const status = ref<any[]>([
    {
      name: '正常',
      key: 'normalOperation',
      color: '#5b8ff9', // 绿色
    },
    {
      name: '低效',
      key: 'inefficient',
      color: '#5ad8a6', // 橙色
    },
    {
      name: '久停',
      key: 'longPause',
      color: '#ff9845', // 红色
    },
    {
      name: '离线',
      key: 'offlineAll',
      color: '#f4664a', // 灰色
    },
  ]);
  const chartRef = ref<HTMLDivElement | null>(null);
  const { setOptions, resize } = useECharts(chartRef as Ref<HTMLDivElement>);
  const allModels = computed(() => {
    const models = new Set<string>();
    props.data.forEach(channel => {
      channel.modelList.forEach(model => {
        models.add(model.model);
      });
    });
    return Array.from(models).sort((a, b) => {
      return a.localeCompare(b);
    });
  });

  const barHeight = computed(() => {
    return props.data.length * 68 + 'px';
  });

  // 图表配置
  const option = reactive({
    // 设置调色板，按顺序应用到系列
    // color: ['#52c41a', '#faad14', '#ff7875', '#d9d9d9'],
    tooltip: {
      trigger: 'axis' as const,
      axisPointer: {
        type: 'shadow' as const,
      },
      backgroundColor: 'rgba(19, 50, 97, 1)',
      borderColor: '#1a3a6a',
      borderWidth: 1,
      padding: 16,
      textStyle: {
        color: '#00fff9',
      },
      formatter: function (params: any) {
        if (!params || params.length === 0) return '';
        const channelName = params[0].name;
        // 按车型和状态分组数据
        const modelData: Record<string, any[]> = {};
        params.forEach((param: any) => {
          let model = '';
          if (param.data && param.data.model) {
            model = param.data.model;
          }
          // 只有当数量大于0时才添加到tooltip中
          if (model && param.value > 0) {
            if (!modelData[model]) {
              modelData[model] = [];
            }
            modelData[model].push({
              statusName: param.seriesName,
              marker: param.marker,
              value: param.value,
            });
          }
        });
        const models = Object.keys(modelData);

        if (models.length === 0) return '';

        // 构建表格样式的HTML
        let html = `<div style="font-size: 14px; font-weight: bold; margin-bottom: 10px;">${channelName}</div>`;

        // 创建表格（无表头）
        html += '<table style="border-collapse: collapse; width: 100%; font-size: 12px;">';

        // 找到最大的状态数量，用于确定行数
        const maxStatusCount = Math.max(...models.map(model => (modelData[model] ? modelData[model].length : 0)));

        // 为每个状态创建一行
        for (let statusIndex = 0; statusIndex < maxStatusCount; statusIndex++) {
          html += '<tr>';
          models.forEach(model => {
            const statusData = modelData[model] && modelData[model][statusIndex];
            if (statusData) {
              html += `<td style="padding: 2px 8px; text-align: left; min-width: 150px;">${statusData.marker}${model} - ${statusData.statusName}：${statusData.value}辆</td>`;
            } else {
              html += '<td style="padding: 2px 8px; text-align: left; min-width: 120px;"></td>';
            }
          });
          html += '</tr>';
        }

        html += '</table>';
        return html;
      },
    },
    legend: {
      data: status.value.map(item => item.name),
      top: 0,
      left: '0%',
      textStyle: {
        color: '#00fff9',
      },
    },
    grid: {
      left: '0%',
      right: '5%',
      bottom: '3%',
      top: '10%',
      containLabel: true,
    },
    xAxis: {
      type: 'value' as const,
      name: '车辆数量',
      axisLabel: {
        interval: 0,
        // rotate: 45,
        fontSize: 12,
        width: 200, // 标签宽度
        overflow: 'truncate', // 超出截断
        color: '#00fff9',
      },
      splitLine: {
        lineStyle: {
          color: '#4e8bcb7a', // X 轴刻度参考线颜色
          type: 'solid', // 可选：'solid', 'dashed', 'dotted'
        },
      },
      nameTextStyle: {
        fontSize: 12,
        color: '#00fff9',
      },
      position: 'top',
    },
    yAxis: {
      type: 'category' as const,
      data: [] as string[],
      axisLabel: {
        interval: 0,
        // rotate: 45,
        fontSize: 12,
        width: 200, // 标签宽度
        overflow: 'truncate', // 超出截断
        color: '#00fff9',
      },
      axisTick: {
        show: true,
        lineStyle: {
          color: '#4e8bcb7a', // Y 轴刻度线颜色
          width: 1,
        },
      },
    },
    series: [] as any[],
  });

  watchEffect(() => {
    if (props.data && props.data.length > 0) {
      initCharts();
    }
  });

  function initCharts() {
    const channels = props.data.map(item => item.name);
    const maxCount = props.data[props.data.length - 1].modelList[0].total;
    // 设置x轴数据
    option.yAxis.data = channels;
    const series: any = [];
    status.value.map((item: { name: string; key: string; color: string }) => {
      allModels.value.forEach(model => {
        const data = channels.map(name => {
          const channel = props.data.find(c => c.name === name);
          if (!channel) return null;

          const modelData = channel.modelList.find(m => m.model === model);
          if (!modelData) return null;

          const value = modelData[item.key as keyof typeof modelData];
          return typeof value === 'number'
            ? {
                value,
                model,
              }
            : null;
        });
        series.push({
          name: item.name,
          type: 'bar' as const,
          stack: model,
          color: item.color, // 设置系列颜色
          label: {
            show: true,
            formatter: ({ value }) => {
              if (maxCount > 0 && maxCount < 30) {
                if (value < 1) return '';
                else return value + '辆';
              }
              if (maxCount >= 30 && maxCount < 150) {
                if (value < 3) return '';
                else return value + '辆';
              }
              if (maxCount >= 150 && maxCount < 500) {
                if (value < 5) return '';
                else return value + '辆';
              }
              if (maxCount > 500) {
                if (value < 8) return '';
                else return value + '辆';
              }
            },
            fontSize: 10,
            color: '#fff',
            // color: '#00fff9',
          },
          emphasis: { focus: 'series' },
          data,
        });
      });
    });
    option.series = series;
    option.grid.top = props.data.length > 9 ? '3%' : '10%';
    setOptions(option);
    nextTick(() => resize());
  }
</script>

<style lang="less" scoped>
  // 图表容器样式
</style>
