<template>
  <BasicDrawer
    @register="registerDrawer"
    title="提交审核"
    :width="adaptiveWidth"
    @ok="handleSubmit"
    @close="handleClose"
    :showFooter="true"
    destroyOnClose
  >
    <BasicForm class="approval-form" @register="registerForm">
      <template #station="{ model, field }">
        <a-select v-model:value="model[field]" placeholder="请选择退场场站" allowClear>
          <a-select-option v-for="item in stationList" :key="item.id" :value="item.id">
            {{ item.label }}
          </a-select-option>
        </a-select>
      </template>
      <template #vehiclePlate="{ model, field }">
        <a-select
          v-model:value="model[field]"
          showSearch
          allowClear
          placeholder="请选择车牌号"
          @change="
                  (val: string) => {
                    handleCarChange(val, 'id');
                  }
                "
          :filterOption="handleFilterOption"
        >
          <a-select-option v-for="item in carPlateList" :value="item.id" :key="item.id">
            {{ item.carPlate }}
          </a-select-option>
        </a-select>
      </template>
      <template #supplementary="{ model, field }">
        <div class="flex column">
          <JUpload
            v-model:value="model[field]"
            bucketName="ov-portal-pub"
            :returnUrl="true"
            :mover="false"
            accept="image/*,application/pdf"
            :maxCount="10"
            :beforeUpload="handleBeforeUpload"
          />
          <div class="upload_tips">
            <span>支持扩展名: .jpg/.png/.pdf, 最多支持上传10个文件, 每个文件不超过50M</span>
          </div>
        </div>
      </template>
      <template #contractFile="{ model, field }">
        <div class="flex column">
          <JUpload
            v-model:value="model[field]"
            bucketName="ov-portal-pub"
            :returnUrl="true"
            :mover="false"
            accept="application/pdf"
            :maxCount="10"
            :beforeUpload="handleBeforeUploadFile"
          />
          <div class="upload_tips">
            <span>支持扩展名: .pdf, 最多支持上传10个文件, 每个文件不超过50M</span>
          </div>
        </div>
      </template>
    </BasicForm>
  </BasicDrawer>
</template>
<script lang="ts" setup>
  import dayjs from 'dayjs';
  import { ref } from 'vue';
  import { getCityAndStation, getTempVins, getCarList } from './index.api';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { useDrawerAdaptiveWidth } from '/@/hooks/jeecg/useAdaptiveWidth';
  import { JUpload } from '/@/components/Form/src/jeecg/components/JUpload';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { useMessage } from '/@/hooks/web/useMessage';

  const { createMessage } = useMessage();
  const props = defineProps({
    schemas: {
      type: Array<any>,
      default: [],
    },

    submitApproval: {
      type: Function,
      default: () => {},
    },
  });
  const stationList = ref<any[]>([]);
  const carPlateList = ref<any[]>([]);
  // 数据过滤
  const handleFilterOption = (input, option) => {
    const label = carPlateList.value.find(item => item.id === option.value)?.carPlate || '';
    return label.toLowerCase().includes(input.toLowerCase());
  };
  /**
   * 下拉选择改变，根据选择的配置的车牌号获取对应的车架号，车身颜色
   * @param val 选中的车架号/车牌号
   * @param attr 根据此属性来获取当前选中的数据信息
   */
  const handleCarChange = (val: string, attr: string) => {
    if (!val) {
      setFieldsValue({
        model: void 0,
        vehiclePlate: void 0,
        vin: void 0,
        assetId: void 0,
      });
    } else {
      const chooseCar: any = carPlateList.value.find(item => item[attr] === val) || {};
      setFieldsValue({
        model: chooseCar?.model,
        vehiclePlate: chooseCar?.carPlate,
        vin: chooseCar?.vin,
        assetId: chooseCar?.id,
      });
    }
  };

  // 声明Emits
  const emit = defineEmits(['success', 'register', 'handleValues']);
  //表单赋值
  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async (data: any) => {
    setDrawerProps({ loading: true });
    await resetFields();
    if (props.schemas.filter(ele => ele.field === 'stationId').length) {
      await getCityAndStation({}).then((res: any) => {
        const result: any[] = [];
        res.map((item: any) => {
          item.stationVOList &&
            item.stationVOList.forEach((station: any) => {
              result.push({
                id: station.id,
                value: station.id,
                label: `${item.operationCity}${station.stationName}`,
              });
            });
        });
        stationList.value = result;
      });
    }
    if (props.schemas.map(ele => ele.field).includes('vehiclePlate')) {
      getTempVins({ id: data.record.id }).then(async vinStr => {
        await getCarList({
          brandModel: '',
          crates: '',
          stationId: data.record.stationId,
          vinStr,
        }).then(res => {
          carPlateList.value = res;
        });
      });
    }
    data.openCallBack && data.openCallBack(data.record);

    setDrawerProps({ loading: false });
  });

  //表单配置
  const [registerForm, { resetFields, validate, setFieldsValue, updateSchema }] = useForm({
    schemas: props.schemas,
    labelWidth: 140,
    showActionButtonGroup: false,
  });

  // #region 文件上传
  async function handleBeforeUpload(file) {
    const validTypes = ['image/jpeg', 'image/png', 'application/pdf'];
    if (!validTypes.includes(file.type)) {
      createMessage.error('只能上传.jpg,.png和.pdf文件');
      return false;
    }
    // 校验文件大小（10MB）
    const isLtSize = file.size / 1024 / 1024 < 50;
    if (!isLtSize) {
      createMessage.error('文件大小不能超过 50MB');
      return false;
    }
    return true;
  }
  async function handleBeforeUploadFile(file) {
    const validTypes = ['application/pdf'];
    if (!validTypes.includes(file.type)) {
      createMessage.error('只能上传.pdf文件');
      return false;
    }
    // 校验文件大小（10MB）
    const isLtSize = file.size / 1024 / 1024 < 50;
    if (!isLtSize) {
      createMessage.error('文件大小不能超过 10MB');
      return false;
    }
    return true;
  }
  // function handleUploadChange(files) {
  //   fileList.value = files.map(item => item.filePath);
  // }
  // #endregion
  //提交事件
  async function handleSubmit() {
    try {
      const values = await validate();
      setDrawerProps({ loading: true, confirmLoading: true });
      const params = {
        ...values,
        applicationDate: dayjs(values.applicationDate).format('YYYY-MM-DD'),
      };
      emit('handleValues', params);
      await props.submitApproval({ ...params });
      setDrawerProps({ loading: true });
      closeDrawer();
      setDrawerProps({ loading: false, confirmLoading: false });
      emit('success');
    } catch (err) {
      setDrawerProps({ loading: false, confirmLoading: false });
    }
  }
  const handleClose = () => {
    // fileList.value = [];
    // contractData.value = {};
  };
  const { adaptiveWidth } = useDrawerAdaptiveWidth();

  export type DTApprovalDrawerRefType = RefType<{
    updateSchema: typeof updateSchema;
    setFieldsValue: typeof setFieldsValue;
  }>;

  defineExpose({
    updateSchema,
    setFieldsValue,
  });
</script>
<style lang="less" scoped>
  .column {
    flex-direction: column;
  }
  .upload_tips {
    margin-top: 12px;
    color: #8d8d8d;
    font-size: 14px;
  }
</style>

<style lang="less">
  .approval-form {
    .ant-input[disabled] {
      color: rgba(0, 0, 0, 0.65);
    }
  }
</style>
