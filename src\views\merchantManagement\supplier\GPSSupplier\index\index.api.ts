import { defHttp } from '/@/utils/http/axios';

enum Api {
  list = '/ov/api/gpsSupplier/list',
  add = '/ov/api/gpsSupplier/add',
  edit = '/ov/api/gpsSupplier/edit',
  enable = '/ov/api/gpsSupplier/enable',
  delete = '/ov/api/gpsSupplier/del',
}
/**
 * 列表
 * @param params
 */
export const list = params => defHttp.get({ url: Api.list, params });

export const handleDelSupplier = params => defHttp.get({ url: Api.delete, params });

export const saveOrUpdate = (params, isUpdate) => defHttp.post({ url: isUpdate ? Api.edit : Api.add, params });

export const changeStatus = params => defHttp.get({ url: Api.enable, params });
