<template>
  <MenuItem :key="item.path" :title="item.title">
    <MenuItemContent v-bind="$props" :item="item" />
  </MenuItem>
</template>
<script lang="ts">
  import { defineComponent } from 'vue';
  import { Menu } from 'ant-design-vue';
  import { itemProps } from '../props';

  import MenuItemContent from './MenuItemContent.vue';
  export default defineComponent({
    name: 'BasicMenuItem',
    components: { MenuItem: Menu.Item, MenuItemContent },
    props: itemProps,
    setup() {
      return {};
    },
  });
</script>
<style lang="less">
  .ant-menu-item.ant-menu-item-selected {
    color: #3a78f9 !important; /* 鼠标移入时的文本颜色*/
    &:hover {
      color: #3a78f9 !important; /* 鼠标移入时的文本颜色*/
    }
  }
  .ant-menu-item {
    //color: black !important;
    color: #666 !important;
    // color: #ffffffa6 !important;
    // background-color: #30374d !important;
    background-color: #fff !important;
    /* background-color: rgb(228, 228, 228) !important; */

    &.top-menu__first-menus {
      color: #ffffffa6 !important;
      background-color: #30374d !important;

      &.ant-menu-item-selected {
        color: #3a78f9 !important;
      }
    }
  }
  .ant-menu-item:hover {
    // background-color: transparent !important;
    color: #3a78f9 !important;
    // color: #fff !important;
  }
</style>
