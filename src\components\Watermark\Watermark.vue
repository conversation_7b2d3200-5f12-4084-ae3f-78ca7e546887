<template>
  <div class="watermark-container">
    <div class="watermark" v-for="(item, index) in watermarks" :key="index" :style="getStyle(item.row, item.col)">
      <!-- 使用 v-html 渲染带有 <br /> 的文本 -->
      <span :style="spanStyle" v-html="replaceNewLine(item.text)"></span>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { defineProps, onMounted, ref, onBeforeUnmount, watch } from 'vue';
  import { CSSProperties } from 'vue';

  const props = defineProps({
    content: {
      type: String,
      default: 'Watermark',
    },
    fontSize: {
      type: Number,
      default: 36,
    },
    color: {
      type: String,
      default: 'rgba(0, 0, 0, 0.1)',
    },
    rotate: {
      type: Number,
      default: -45,
    },
    spacing: {
      type: Number,
      default: 350, // 增加默认间距以降低水印密度
    },
    spanStyle: {
      type: Object,
      default: () => {},
    },
  });

  const watermarks = ref<{ text: string; row: number; col: number }[]>([]);

  // 动态计算水印数量和间距
  const calculateWatermarks = () => {
    const containerWidth = window.innerWidth;
    const containerHeight = window.innerHeight;

    const cols = Math.ceil(containerWidth / props.spacing) + 1;
    const rows = Math.ceil(containerHeight / props.spacing) + 1;

    watermarks.value = [];
    for (let row = 0; row < rows; row++) {
      for (let col = 0; col < cols; col++) {
        watermarks.value.push({ text: props.content, row, col });
      }
    }
  };

  // 获取水印的样式
  const getStyle = (row: number, col: number): CSSProperties => {
    const top = row * props.spacing;
    const left = col * props.spacing;

    return {
      position: 'absolute',
      top: `${top}px`,
      left: `${left}px`,
      fontSize: `${props.fontSize}px`,
      color: props.color,
      transform: `rotate(${props.rotate}deg)`,
      pointerEvents: 'none',
      userSelect: 'none',
      width: `${props.spacing}px`,
      height: `${props.spacing}px`,
    };
  };

  // 替换换行符为 <br />
  const replaceNewLine = (text: string) => {
    return text.replace(/\n/g, '<br />');
  };

  // 在组件挂载后计算水印
  onMounted(() => {
    calculateWatermarks();
    window.addEventListener('resize', calculateWatermarks);
    watch(
      () => props?.content,
      () => {
        calculateWatermarks();
      }
    );
  });

  onBeforeUnmount(() => {
    window.removeEventListener('resize', calculateWatermarks);
  });
</script>

<style scoped>
  .watermark-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    pointer-events: none;
    z-index: 9999;
    overflow: visible;
  }

  .watermark {
    position: absolute;
    text-align: center;
    line-height: 1;
  }
</style>
