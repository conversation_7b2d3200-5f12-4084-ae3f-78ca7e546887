<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable">
      
      <template #tableTitle>
        <a-button type="primary" v-auth="'veh:violateSync:exportExcel'" @click="handleExportXLS">导出列表</a-button>
      </template>
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
      <template #synchronizationStatus="{ record }">
        <StateBox :text="statusObj[record.status].text" :stateBC="statusObj[record.status].stateBC"></StateBox>
      </template>
    </BasicTable>
  </div>
</template>

<script lang="ts" name="vehicleServiceManagement-violationSynchronization" setup>
  import { BasicTable, TableAction, ActionItem } from '/@/components/Table';
  import { list, vehViolateSyncRetry, downloadXls } from './index.api';
  import { columns, searchFormSchema } from './index.data';
  import { useListPage } from '/@/hooks/system/useListPage';
  import StateBox from '@/components/common/StateBox.vue';
  // #region 权限
  import { usePermission } from '/@/hooks/web/usePermission';
  const { hasPermission } = usePermission();
  // #endregion

  // 同步状态（0：同步中；1：同步完成；2：同步失败）
  const statusObj = {
    '0': {
      text: '同步中',
      stateBC: '#3979f9',
    },
    '1': {
      text: '同步完成',
      stateBC: '#26f338',
    },
    '2': {
      text: '同步失败',
      stateBC: '#fd4545',
    },
  };
  //注册table数据
  const { tableContext } = useListPage({
    tableProps: {
      api: list,
      immediate: true,
      columns,
      canResize: false,
      useSearchForm: true,
      formConfig: {
        schemas: searchFormSchema,
        labelWidth: 90,
      },
      beforeFetch: params => {
        if (params.syncTime) {
          const time = params.syncTime.split(',');
          params.taskStartDate = time[0]  + ' 00:00:00';
          params.taskEndDate = time[1] + ' 23:59:59';
          delete params.syncTime;
        }
        return params;
      },
      actionColumn: {
        width: 100,
        fixed: 'right',
      },
    },
  });
  //注册table数据
  const [registerTable, { reload, getForm }] = tableContext;

  /**
   * 操作栏
   */
  function getTableAction(record): ActionItem[] {
    const arr: ActionItem[] = []
    if(record.status == '2'){
      arr.push({
        label: '重试',
        popConfirm: {
          title: '是否确认重试',
          confirm: retry.bind(null, record),
        },
        ifShow: () => hasPermission('veh:violateSync:retry'),
      })
    }
    return arr;
  }
  async function retry(record) {
    await vehViolateSyncRetry({ id: record.id });
    await reload();
  }
  
  async function handleExportXLS() {
    const { validate } = getForm();
    const params = await validate();
    downloadXls(params);
  }
</script>
