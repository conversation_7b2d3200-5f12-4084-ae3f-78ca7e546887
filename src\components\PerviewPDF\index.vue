<!-- 基本用法 -->
<template>
  <div v-if="iframeSrc">
    <iframe id="previewIframe" :height="height" :src="iframeSrc" width="100%" frameborder="0"></iframe>
    <Tooltip title="下载">
      <download-outlined style="position: fixed; cursor: pointer; top: 40%; right: 48px; z-index: 999; font-size: 36px" @click="downLoad" />
    </Tooltip>
  </div>
</template>
<script lang="ts" setup>
  import { ref, defineProps, watch } from 'vue';
  import { useGlobSetting } from '/@/hooks/setting';
  import { Base64 } from 'js-base64';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { Tooltip } from 'ant-design-vue';
  import { DownloadOutlined } from '@ant-design/icons-vue';
  // 文件预览
  const globSetting = useGlobSetting();
  const filePreviewUrl = globSetting.filePreview;
  const { createMessage } = useMessage();
  console.log('🚀 ~ filePreviewUrl:', filePreviewUrl);
  const iframeSrc = ref('');
  const height = ref(500);
  const props = defineProps({
    // 需要预览的url
    url: { type: String, default: '' },
    // 如果下载需要回传参数，可以使用这个params输入参数
    params: { type: Object, default: {} },
    isDownLoad: { type: Boolean, default: false },
  });
  // 声明Emits
  const emit = defineEmits(['download']);

  // 监听 props.url 变化
  watch(
    () => props.url,
    newUrl => {
      if (newUrl) {
        setSrc(newUrl);
      }
    },
    { immediate: true } // 立即执行一次
  );
  function setSrc(url) {
    var baseUrl = filePreviewUrl?.endsWith('/') ? filePreviewUrl : filePreviewUrl + '/';
    if (!url.startsWith(baseUrl)) {
      url = baseUrl + 'getCorsFile?urlPath=' + encodeURIComponent(Base64.encode(url));
    }
    console.log('🚀 ~ newUrl:', url);
    // 拼接完整的 iframe src（保留原逻辑）
    iframeSrc.value =
      filePreviewUrl + '/pdfjs/web/viewer.html?file=' + encodeURIComponent(url) + '&disablepresentationmode=true&disableopenfile=true&disableprint=true&disabledownload=true&disablebookmark=true';
    setIframeHeight();
  }
  // 回调组件下载方法
  function downLoad() {
    if (props.isDownLoad) {
      createMessage.warning('下载中，请勿重复点击!');
      return;
    }
    emit('download', props.params);
  }
  function setIframeHeight() {
    height.value = document.documentElement.clientHeight - 100;
  }
  // 监听窗口变化
  window.onresize = function () {
    setIframeHeight();
  };
</script>

<style scoped lang="less"></style>
