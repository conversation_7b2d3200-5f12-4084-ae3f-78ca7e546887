import { BasicColumn, FormSchema } from '@/components/Table';

export const columns: BasicColumn[] = [
  {
    title: '供应商',
    dataIndex: 'name',
    width: 280,
  },
  {
    title: '联系人',
    dataIndex: 'contact',
    width: 100,
  },
  {
    title: '联系电话',
    dataIndex: 'phone',
    width: 100,
  },
  {
    title: '状态',
    dataIndex: 'state',
    width: 120,
    slots: { customRender: 'state' },
  },
  {
    title: '创建人',
    dataIndex: 'createBy',
    width: 100,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 100,
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '供应商名称',
    field: 'name',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '状态',
    field: 'state',
    component: 'Select',
    colProps: { span: 6 },
    componentProps: {
      options: [
        {
          label: '禁用',
          value: 0,
        },
        {
          label: '正常',
          value: 1,
        },
      ],
    },
  },
];

export const formSchema: FormSchema[] = [
  {
    label: '',
    field: 'id',
    component: 'Input',
    ifShow: false,
  },
  {
    label: '供应商名称',
    field: 'name',
    component: 'Input',
    componentProps: {
      maxlength: 30,
    },
    required: true,
  },
  {
    label: '状态',
    field: 'state',
    component: 'RadioGroup',
    componentProps: {
      options: [
        {
          label: '启用',
          value: 1,
        },
        {
          label: '禁用',
          value: 0,
        },
      ],
    },
    required: true,
  },
  {
    label: '联系人',
    field: 'contact',
    component: 'Input',
    componentProps: {
      maxlength: 30,
    },
    required: true,
  },
  {
    label: '联系电话',
    field: 'phone',
    component: 'Input',
    componentProps: {
      maxlength: 30,
    },
    required: true,
    rules: [
      {
        required: true,
        pattern: /^(1[3-9]\d{9}|\d{3,4}-\d{7,8})$/,
        message: '请输入正确的联系电话（手机号或座机）',
      },
    ],
  },
  {
    label: '公司地址',
    field: 'address',
    component: 'InputTextArea',
    componentProps: {
      maxlength: 100,
      autoSize: { minRows: 3, maxRows: 6 },
    },
  },
  {
    label: '备注',
    field: 'remark',
    component: 'InputTextArea',
    componentProps: {
      maxlength: 100,
      autoSize: { minRows: 3, maxRows: 6 },
    },
  },
];
