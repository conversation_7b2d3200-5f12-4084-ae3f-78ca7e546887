import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '@/hooks/web/useMessage';

const { createConfirm } = useMessage();

enum Api {
  queryStat = '/dis/audit/queryStat',
  list = '/dis/audit/list',
  submit = '/dis/audit/submit',
  confirm = '/dis/audit/confirm',
  deleteOne = '/dis/audit/delete',
  exportFiles = '/dis/audit/exportFiles',
  logList = '/dis/audit/logList',
  basicQueryById = '/dis/acc/getInfo',
  finQueryById = '/dis/acc/getFin',
  conQueryById = '/dis/acc/getCon',
  annexQueryById = '/dis/acc/getAnnex',
  auditQueryById = '/dis/audit/queryAudit',
  basicSave = '/dis/acc/info/save',
  finSave = '/dis/acc/fin/save',
  conSave = '/dis/acc/con/save',
  annexSave = '/dis/acc/annex/save',
  getAnnexExample = '/dis/acc/getAnnexExample',
  getIDInfo = '/dis/acc/getIDInfo',
  listSer = "/dis/acc/listSer"
}
//渠道商信息详情-批量导出附件
export const exportFiles = Api.exportFiles;
/**
 * 渠道商基本信息-首页审核统计
 * @param params
 */
export const queryStat = (params) => defHttp.get({ url: Api.queryStat, params });

/**
 * 列表
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

/**
 * 查询所有未关联的服务商
 * @param params
 */
export const listSer = (params) => defHttp.get({ url: Api.listSer, params });

/**
 * 审核日志列表
 * @param params
 */
export const logList = (params) => defHttp.get({ url: Api.logList, params });
/**
 *  上传实例链接
 * @param params
 */
export const getAnnexExampleList = (params) => defHttp.get({ url: Api.getAnnexExample, params });
/**
 * 详情
 * @param params
 * tabActive 类别
 */
export const queryByIdApi = (params, tabActive) => {
  // '基本信息', '财务信息', 联系信息', '材料附件', '审核意见',
  // 'basic','finance', 'contact', 'annex', 'audit',
  let url = Api.basicQueryById;
  switch (tabActive) {
    case 'finance':
      url = Api.finQueryById;
      break;
    case 'contact':
      url = Api.conQueryById;
      break;
    case 'annex':
      url = Api.annexQueryById;
      break;
    case 'audit':
      url = Api.auditQueryById;
      break;
    default:
      url = Api.basicQueryById;
      break;
  }
  return defHttp.get({ url: url, params });
};

/**
 * 保存或者更新租户
 * @param params
 */
export const saveOrUpdate = (params, tabActive) => {
  // '基本信息', '财务信息', 联系信息', '材料附件', '审核意见',
  // 'basic','finance', 'contact', 'annex', 'audit',
  let url = Api.basicSave;
  switch (tabActive) {
    case 'finance':
      url = Api.finSave;
      break;
    case 'contact':
      url = Api.conSave;
      break;
    case 'annex':
      url = Api.annexSave;
      break;
    default:
      url = Api.basicSave;
      break;
  }
  return defHttp.post({ url: url, params });
};
/**
 * 删除
 */
export const deleteOne = (params, handleSuccess) => {
  return defHttp.delete({ url: Api.deleteOne, params }, { joinParamsToUrl: true }).then(() => {
    handleSuccess();
  });
};
/**
 * 提交审核
 * @param params
 */
export const submitAudit = (params) => {
  return defHttp.post({ url: Api.submit, params }, { joinParamsToUrl: true });
};
/**
 *
 * 确认审核
 * @param params
 */
export const confirmAudit = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认提交审核结果吗？',
    content: '提交后，则不可以修改',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      // handleSuccess();
      return defHttp.post({ url: Api.confirm, params }).then(() => {
        handleSuccess();
      });
    },
  });
};
/**
 * 识别身份证
 * @param params
 */
export const getIDInfo = (params) => defHttp.get({ url: Api.getIDInfo, params });
