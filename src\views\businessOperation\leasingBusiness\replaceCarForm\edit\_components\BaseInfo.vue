<template>
  <!-- 基本信息 -->
  <div class="p-15px">
    <div class="info-row-vertical">
      <div class="flex items-center common-info-row">
        <div class="common-info-label">上游订单编号:</div>
        <div class="common-info-text">
          <a-button
            class="common-info-btn"
            type="link"
            size="small"
            @click="handleGoPage(`/businessOperation/${routePathMap[myContractType]}/postDeliveryManagement`)"
          >
            {{ formData.upstreamOrderNo }}
          </a-button>
        </div>
      </div>
      <div class="flex items-center common-info-row">
        <div class="common-info-label">替换车单编号:</div>
        <div class="common-info-text">
          <a-button class="common-info-btn" type="link" size="small" @click="router.go(-1)">
            {{ formData.replaceOrderNo }}
          </a-button>
        </div>
      </div>
      <div class="flex items-center common-info-row">
        <div class="common-info-label">原车维修工单:</div>
        <div class="common-info-text">
          <a-button class="common-info-btn" type="link" size="small" @click="handleGoAfterSales">点击查看</a-button>
        </div>
      </div>
    </div>

    <div class="flex flex-wrap info-row-horizontal-four">
      <div class="flex common-info-row w-25">
        <div class="common-info-label">出租方名称:</div>
        <div class="common-info-text">{{ formData.lessorName }}</div>
      </div>
      <div class="flex common-info-row w-25">
        <div class="common-info-label">原车车牌号:</div>
        <div class="common-info-text">{{ formData.oldVehiclePlate || '-' }}</div>
      </div>
      <div class="flex common-info-row w-25">
        <div class="common-info-label">替换车交车状态:</div>
        <!-- <div class="common-info-text">{{ formData.deliveryStatus || '-' }}</div> -->
        <div class="common-state-box">
          <div class="common-state-box__wrap">
            <span :class="['state-dot', `status-${formData.deliveryStatus}`]"></span>
            <span>{{ getNameByValue(formData.deliveryStatus) }}</span>
          </div>
        </div>
      </div>
      <div class="flex common-info-row w-25">
        <div class="common-info-label">客户提车人:</div>
        <div class="common-info-text">{{ formData.customerName || '-' }}</div>
      </div>
      <div class="flex common-info-row w-25">
        <div class="common-info-label">承租方名称:</div>
        <div class="common-info-text">{{ formData.lesseeName || '-' }}</div>
      </div>
      <div class="flex common-info-row w-25">
        <div class="common-info-label">原车车架号:</div>
        <div class="common-info-text">{{ formData.oldVin || '-' }}</div>
      </div>
      <div class="flex common-info-row w-25">
        <div class="common-info-label">替换车车牌号:</div>
        <div class="common-info-text">{{ formData.vehiclePlate || '-' }}</div>
      </div>
      <div class="flex common-info-row w-25">
        <div class="common-info-label">要求提车时间:</div>
        <div class="common-info-text">{{ formData.requestPickupDate || '-' }}</div>
      </div>
      <!-- 已作废，已退车，显示作废原因/退车原因 -->
      <div class="flex common-info-row w-25">
        <div class="common-info-label">申请类型:</div>
        <div class="common-info-text">{{ formData.applicationType || '-' }}</div>
      </div>
      <div class="flex common-info-row w-25">
        <div class="common-info-label">申请原因:</div>
        <div class="common-info-text">{{ formData.applicationCause || '-' }}</div>
      </div>
      <div class="flex common-info-row w-25">
        <div class="common-info-label">替换车车架号:</div>
        <div class="common-info-text">{{ formData.vin || '-' }}</div>
      </div>
      <div class="flex common-info-row w-25">
        <div class="common-info-label">创建时间:</div>
        <div class="common-info-text">{{ formData.createTime || '-' }}</div>
      </div>
    </div>
  </div>
</template>

<!-- 交车状态: 1、待交车 2、交车中 3、已交车  4、已作废 5、已退车 -->
<script lang="ts" name="base-info" setup>
  import { ref } from 'vue';
  import { useRouter } from 'vue-router';
  import { ITaskInfo } from '../../detail/index.data';
  import { initDictOptions } from '/@/utils/dict';
  import {
    EContractType,
    getContractType,
  } from '/@/views/businessOperation/leasingBusiness/leaseContract/index/index.api';

  defineProps<{
    formData: ITaskInfo;
    myContractType: EContractType; // 租赁合同/以租代售合同/展示车合同
  }>();
  // 路由前缀映射
  const routePathMap = {
    [`${EContractType.LEASE_CONTRACT}`]: 'leasingBusiness',
    [`${EContractType.SHOW_CAR_CONTRACT}`]: 'showCarBusiness',
    [`${EContractType.LEASE_PURCHASE_CONTRACT}`]: 'leasePurchase',
  };

  // 初始化字典-交车状态
  const carStatusList = ref<any>([]);
  initDictOptions('bo_pickup_status').then(res => {
    carStatusList.value = res;
  });
  // 获取字典中文
  function getNameByValue(value: number) {
    return carStatusList.value.find(item => +item.value === +value)?.text;
  }

  // 页面跳转
  const router = useRouter();
  const handleGoPage = (url: string) => {
    router.push(url);
  };
  const handleGoAfterSales = () => {
    const aftersalesUrl = `${location.origin}/aftersales`;
    window.open(aftersalesUrl);
  };
</script>
<style lang="less" scoped>
  @import url('../../common/styles.less');
</style>
