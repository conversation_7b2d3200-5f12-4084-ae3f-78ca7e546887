import { BasicColumn, FormSchema } from '/@/components/Table';
import { render } from '@/utils/common/renderUtils';
import { Badge } from 'ant-design-vue';
import { h } from 'vue';

export const columns: BasicColumn[] = [
  {
    title: '一级类目',
    dataIndex: 'field1',
    key: 'field1',
    fixed: 'left',
  },
  {
    title: '二级类目',
    dataIndex: 'field2',
    key: 'field2',
  },
  {
    title: '类目SPU',
    dataIndex: 'spuNO',
    key: 'spuNO',
  },
  {
    title: '配件名称',
    dataIndex: 'projectName',
    key: 'projectName',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    slots: { customRender: 'recordStatus' },
  },
  {
    title: '工时定额',
    dataIndex: 'gongshidinge',
    key: 'gongshidinge',
  },
  {
    title: 'SKU编码',
    dataIndex: 'SKUbianma',
    key: 'SKUbianma',
    slots: { customRender: 'SKUbianma' },
  },
  {
    title: '原厂编码',
    dataIndex: 'gongshidinge',
    key: 'gongshidinge',
  },
  {
    title: '配件属性',
    dataIndex: 'accessoryType',
    key: 'accessoryType',
    slots: { customRender: 'accessoryType' },
  },
  {
    title: '规格',
    dataIndex: 'guige',
    key: 'guige',
  },
  {
    title: '单位',
    dataIndex: 'danwei',
    key: 'danwei',
    customRender({ text }) {
      // 待提交、待审核(#00ffff或cyan)、未通过、已通过
      return render.renderDictComponents(text, 'accessory_sku_unit', txt => h(Badge, { color: '#ccc', text: txt }));
    },
  },
  {
    title: '适用车型',
    dataIndex: 'shiyongchexing',
    key: 'shiyongchexing',
  },
  {
    title: '结算价',
    dataIndex: 'jiesuanjia',
    key: 'jiesuanjia',
  },
  {
    title: '更新人',
    dataIndex: 'updateBy',
    key: 'updateBy',
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
  },
];

export enum TypeEnum {
  ACCESSORY = '2',
  PROJECT = '2',
}

export const searchFormSchema: FormSchema[] = [
  {
    label: '服务商名称',
    field: 'spuNO',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '类目SPU',
    field: 'spuNO',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '类型',
    field: 'typexxxxxxx',
    component: 'Select',
    componentProps: {
      options: [
        { label: '配件', value: TypeEnum.ACCESSORY },
        { label: '项目', value: TypeEnum.PROJECT },
      ],
    },
    colProps: { span: 6 },
  },
  {
    label: '类目SPU',
    field: 'spuNO',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: 'SKU名称',
    field: 'projectName',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: 'SKU编码',
    field: 'skuCode',
    component: 'Input',
    colProps: { span: 6 },
  },
];

export enum OpenDrawerType {
  add = 'add',
  edit = 'edit',
  detail = 'detail',
}
export enum Consumables {
  Consumables = '1',
  IsConsumables = '2',
}
export const formSchema: FormSchema[] = [
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    label: '',
    field: 'openType',
    component: 'Input',
    show: false,
  },

  {
    field: 'fuwushang',
    label: '服务商',
    component: 'Input',
    labelWidth: 80,
    render: ({ field, model }) => {
      return model[field] || '';
    },
  },
  {
    field: 'fuwushang2',
    label: '服务商编码',
  },
  {
    label: '类目SPU',
    field: 'leimuSPU',
    component: 'Input',
    labelWidth: 80,
    render: ({ field, model }) => {
      return model[field] || '';
    },
  },
  {
    label: 'SKU名称',
    field: 'leimuName',
    component: 'Input',
    labelWidth: 80,
    render: ({ field, model }) => {
      return model[field] || '';
    },
  },
  {
    field: 'accessoryType',
    label: '类型',
    component: 'Input',
    labelWidth: 80,
    render: ({ field, model }) => {
      return model[field] || '';
    },
  },
  {
    field: 'gongshidinge',
    label: '工时定额',
    component: 'Input',
    labelWidth: 80,
    render: ({ field, model }) => {
      return model[field] || '';
    },
  },
  {
    field: 'peijianshuxing',
    label: '配件属性',
    component: 'Input',
    labelWidth: 80,
    render: ({ field, model }) => {
      return model[field] || '';
    },
  },
  {
    field: 'danwei',
    label: '单位',
    component: 'Input',
    labelWidth: 80,
    render: ({ field, model }) => {
      return model[field] || '';
    },
  },
  {
    field: 'yuanchangbianma',
    label: '原厂编码',
    component: 'Input',
    labelWidth: 80,
    render: ({ field, model }) => {
      return model[field] || '';
    },
  },
  {
    field: 'guige',
    label: '规格',
    component: 'Input',
    labelWidth: 80,
    render: ({ field, model }) => {
      return model[field] || '';
    },
  },
  {
    field: 'shiyongchexing',
    label: '适用车型',
    component: 'Input',
    labelWidth: 80,
    render: ({ field, model }) => {
      return model[field] || '';
    },
  },
  {
    label: '结算价',
    field: 'jiesuanjia',
    component: 'Input',
    labelWidth: 80,
    render: ({ field, model }) => {
      return model[field] || '';
    },
  },
  {
    label: '新结算价',
    field: 'xinjiesuanjia',
    component: 'Input',
    labelWidth: 80,
    colProps: { span: 22 },
    componentProps: {
      maxlength: 50,
    },
    rules: [
      { required: true, message: '请输入结算价' },
      {
        validator: (_, value, callback) => {
          if (!/^(0|[1-9]\d*)(\.\d{1,2})?$/.test(value)) {
            callback('请输入数字，最多保留两位小数');
          } else {
            const num = parseFloat(value);
            if (num < 0 || num > 9999) {
              callback('结算价范围应为0-9999');
            } else {
              callback();
            }
          }
        },
      },
    ],
    ifShow: ({ values }) => !(values.openType == OpenDrawerType.detail),
  },
];
