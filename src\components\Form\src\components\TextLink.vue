<template>
  <AButton class="custom-btn" type="link" @click="handlePreview">{{ btnText }}</AButton>
  <!-- 隐藏的 a-image，只用来触发预览 -->
  <AImage
    :src="imageUrl[0]"
    :preview="{
      visible: previewVisible,
      onVisibleChange: handleVisibleChange,
    }"
    style="display: none; max-width: 80vw; max-height: 80vh; object-fit: contain"
  />
</template>
<script lang="ts">
  import { Button, Image } from 'ant-design-vue';
  import { defineComponent, ref } from 'vue';
  import { propTypes } from '/@/utils/propTypes';

  export default defineComponent({
    name: 'ApiSelect',
    components: {
      AButton: Button,
      AImage: Image,
    },
    inheritAttrs: false,
    props: {
      imageUrl: propTypes.array as any,
      btnText: propTypes.string,
    },
    setup(props) {
      const previewVisible = ref(false);

      const handlePreview = () => {
        previewVisible.value = true;
      };

      const handleVisibleChange = (val: boolean) => {
        previewVisible.value = val;
      };
      const handleClick = () => {
        window.open(props.imageUrl[0], '_blank');
      };
      return { previewVisible, handleClick, handlePreview, handleVisibleChange };
    },
  });
</script>
<style lang="less" scoped>
  .custom-btn {
    padding: 0 !important;
  }
</style>
<style>
  .ant-image-preview-img {
    width: 75%;
    height: 75%;
    object-fit: contain;
  }
</style>
