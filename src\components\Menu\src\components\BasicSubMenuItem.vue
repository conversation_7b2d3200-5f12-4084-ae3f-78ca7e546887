<template>
  <BasicMenuItem v-if="!menuHasChildren(item) && getShowMenu" v-bind="$props" :class="{ 'top-menu__first-menus': isFirst }" />

  <span v-if="menuHasChildren(item) && getShowMenu && !isFirst" class="custom-top-menu">
    <div class="custom-top-menu__menulist">
      <a-menu-item-group :title="item?.title">
        <template v-for="childrenItem in item.children || []" :key="childrenItem.path">
          <BasicSubMenuItem v-bind="$props" :item="childrenItem" :isFirst="false" />
        </template>
      </a-menu-item-group>
    </div>
  </span>
  <SubMenu
    style="background-color: #30374d"
    v-if="menuHasChildren(item) && getShowMenu && isFirst"
    :class="[theme]"
    :key="`submenu-${item.path}`"
    popupClassName="app-top-menu-popup custom-popup-menu"
  >
    <template #title>
      <MenuItemContent v-bind="$props" :item="item" />
    </template>

    <template v-for="childrenItem in item.children || []" :key="childrenItem.path">
      <BasicSubMenuItem v-bind="$props" :item="childrenItem" :isFirst="false" />
    </template>
  </SubMenu>
</template>
<script lang="ts">
  import type { Menu as MenuType } from '/@/router/types';
  import { defineComponent, computed } from 'vue';
  import { Menu } from 'ant-design-vue';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { checkChildrenHidden } from '/@/utils/common/compUtils';
  import { itemProps } from '../props';
  import BasicMenuItem from './BasicMenuItem.vue';
  import MenuItemContent from './MenuItemContent.vue';
  import CustomMenu from './CustomMenu.vue';

  export default defineComponent({
    name: 'BasicSubMenuItem',
    isSubMenu: true,
    components: {
      BasicMenuItem,
      SubMenu: Menu.SubMenu,
      MenuItemContent,
      CustomMenu,
    },
    props: itemProps,
    setup(props) {
      const { prefixCls } = useDesign('basic-menu-item');

      const getShowMenu = computed(() => !props.item.meta?.hideMenu);
      function menuHasChildren(menuTreeItem: MenuType): boolean {
        return !menuTreeItem.meta?.hideChildrenInMenu && Reflect.has(menuTreeItem, 'children') && !!menuTreeItem.children && menuTreeItem.children.length > 0 && checkChildrenHidden(menuTreeItem);
      }
      return {
        prefixCls,
        menuHasChildren,
        checkChildrenHidden,
        getShowMenu,
      };
    },
  });
</script>

<style lang="less" scoped>
  .custom-top-menu {
    width: 100%;
    margin-top: 8px;
    &:after {
      content: '';
      display: block;
      // width: calc(100% - 16px - 16px);
      margin: 0 auto;
      border-bottom: 1px dashed #dfdddd;
      background-color: #fff !important;
    }
    &:last-child {
      &:after {
        display: none;
      }
    }
    &__menulist {
      background-color: #fff !important;
      display: flex;
      justify-content: flex-start;
      flex-wrap: wrap;
      padding-bottom: 8px;
    }
  }

  .pop-title,
  .custom-top-menu__pop-title {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
    height: 50px;
    box-sizing: border-box;
    padding: 0 16px;
    // color: black;
    color: #fff;
    font-size: 16px;
    font-weight: 600;
  }
</style>
