import { BasicColumn, FormSchema } from '@/components/Table';

export const columns: BasicColumn[] = [
  {
    title: '企业ID',
    dataIndex: 'id',
    key: 'id',
    customRender: ({ text }) => text || '-',
  },
  {
    title: '企业名称',
    dataIndex: 'subBusinessName',
    key: 'subBusinessName',
    customRender: ({ text }) => text || '-',
  },
  {
    title: '统一社会信用代码',
    dataIndex: 'socialCreditCode',
    key: 'socialCreditCode',
    customRender: ({ text }) => text || '-',
  },
  {
    title: '行业',
    dataIndex: 'industry',
    key: 'industry',
    customRender: ({ text }) => text || '-',
  },
  {
    title: '关联订单数',
    dataIndex: 'orderNum',
    key: 'orderNum',
    customRender: ({ text }) => (text !== null ? text : '-'),
  },
  {
    title: '提车数量',
    dataIndex: 'pickUpNum',
    key: 'pickUpNum',
    customRender: ({ text }) => (text !== null ? text + '台' : '-'),
  },

  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    customRender: ({ text }) => text || '-',
    // width: 120,
  },
];
export const searchFormSchema: FormSchema[] = [
  {
    label: '企业名称',
    field: 'subBusinessName',
    component: 'Input',
    colProps: { span: 6 },
  },
];
