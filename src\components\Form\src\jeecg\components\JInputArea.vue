<template>
  <a-textarea v-bind="getBindValue" v-model:value="showText" @input="backValue"></a-textarea>
</template>

<script lang="ts">
  import { defineComponent,  ref, unref, watch } from 'vue';
  import { useAttrs } from '/@/hooks/core/useAttrs';
  import { propTypes } from '/@/utils/propTypes';

  export default defineComponent({
    name: 'JInputArea',
    inheritAttrs: false,
    props: {
      value: propTypes.string.def(''),
      placeholder: propTypes.string.def(''),
      trim: propTypes.bool.def(false),
    },
    emits: ['change', 'update:value'],
    setup(props, { emit }) {
      const attrs = useAttrs();
      //表单值
      const showText = ref('');
      //绑定属性
      const getBindValue = Object.assign({}, unref(props), unref(attrs));

      console.log("🚀 ~ setup ~ getBindValue:", getBindValue)
      //监听value变化
      watch(
        () => props.value,
        () => {
          initVal();
        },
        { immediate: true }
      );

      /**
       * 初始化数值
       */
      function initVal() {
        let text = !props.value ? '' : props.value;
        showText.value = text;
      }

      /**
       * 返回值
       */
      function backValue(e) {
        let text = e?.target?.value ?? '';
        if (text && !!props.trim) {
          text = text.trim();
        }
        emit('change', text);
        emit('update:value', text);
      }

      return { showText, attrs, getBindValue, backValue };
    },
  });
</script>

<style scoped></style>
