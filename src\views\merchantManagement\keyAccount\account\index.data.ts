import { BasicColumn, FormSchema } from '@/components/Table';
// import {phone} from '@/views/channelMerchants/manage/index.data'
const phone = (required, schema, model) => {
  return [
    {
      required: required,
      validator: async (_, value) => {
        if (required && !value) {
          return Promise.reject(`请输入${schema.label}`);
        }
        if (!/^1[3456789]\d{9}$/.test(value)) {
          return Promise.reject(`${schema.label}格式有误`);
        }
        return Promise.resolve();
      },
      trigger: 'change',
    },
  ];
};
export const columns: BasicColumn[] = [
  {
    title: '大客户名称',
    dataIndex: 'businessName',
    width: 280,
  },
  {
    title: '管理员姓名',
    dataIndex: 'realName',
    width: 100,
  },
  {
    title: '管理员手机号',
    dataIndex: 'phone',
    width: 100,
  },
  {
    title: '审核时间',
    dataIndex: 'reviewTime',
    width: 100,
  },
  {
    title: '账号状态',
    dataIndex: 'accountStatus',
    width: 120,
    slots: { customRender: 'accountStatus' },
  },
];
export const searchFormSchema: FormSchema[] = [
  {
    label: '大客户名称',
    field: 'businessName',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '管理员姓名',
    field: 'realName',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '管理员手机号',
    field: 'phone',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '账号状态',
    field: 'accountStatus',
    component: 'Select',
    colProps: { span: 6 },
    componentProps: {
      options: [
        {
          label: '正常',
          value: 0,
        },
        {
          label: '禁用',
          value: 1,
        },
      ],
    },
  },
];

export const formSchema: FormSchema[] = [
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    label: '大客户名称',
    field: 'businessName',
    component: 'Input',
    slot: 'nameText',
  },
  {
    label: '管理员姓名',
    field: 'realName',
    component: 'Input',
    slot: 'realNameText',
  },
  {
    label: '管理员手机号',
    field: 'phone',
    component: 'Input',
    slot: 'phoneText',
  },
  {
    label: '手机号码',
    field: 'basicPhone',
    component: 'Input',
    componentProps: {
      autoComplete: 'off',
      placeholder: '请输入手机号码',
    },
    dynamicRules: ({ model, schema }) => phone(true, schema, model),
  },
  {
    field: 'basicPassword',
    label: '登录密码',
    component: 'StrengthMeter',
    componentProps: {
      placeholder: '请输入登录密码',
      autoComplete: 'off',
      maxlength: 12,
    },
    helpMessage: ['密码强度校验5-12位,', '①至少一个大写字母、', '②至少一个小写字母、', '③至少一个数字'],
    required: true,
    rules: [
      {
        pattern: /^\S*(?=\S{5,})(?=\S*\d)(?=\S*[A-Z])(?=\S*[a-z])\S*$/,
        message: '请按要求填写密码',
        required: true,
      },
    ],
  },
  {
    field: 'secondPassword',
    label: '确认密码',
    component: 'InputPassword',
    dynamicRules: ({ model }) => {
      return [
        {
          required: true,
          validator: (_, value) => {
            if (!value) {
              return Promise.reject('请输入确认密码');
            }
            if (value != model.basicPassword) {
              return Promise.reject('两次输入的密码不一致');
            }
            return Promise.resolve();
          },
        },
      ];
    },
  },
];
