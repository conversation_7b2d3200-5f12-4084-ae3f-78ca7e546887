<template>
  <BasicDrawer
    v-bind="$attrs"
    @register="registerDrawer"
    :title="title"
    :width="adaptiveWidth"
    @ok="handleSubmit"
    :showFooter="showFooter"
    destroyOnClose
  >
    <BasicForm @register="registerForm">
      <template #treeSelectLevel2="{ model, field }">
        <a-tree-select
          v-model:value="model[field]"
          show-search
          style="width: 100%"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          placeholder="请选择类目"
          allow-clear
          dropdownClassName="customize-disabled-style"
          :tree-data="treeData"
          @change="changeSpuId"
        >
        </a-tree-select>
      </template>

      <!-- 单位选择 -->
      <template #unit="{ model, field }">
        <a-select v-model:value="model[field]" showSearch :allowClear="false" placeholder="请选择单位">
          <a-select-option v-for="item in unitList" :key="item.text" :value="item.text"> </a-select-option>
        </a-select>
      </template>
      <template #applications="{ model, field }">
        <a-tree-select
          v-model:value="model[field]"
          :disabled="model.openType === OpenDrawerType.detail"
          show-search
          style="width: 100%"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          placeholder="请选择适用车型"
          allow-clear
          multiple
          dropdownClassName="customize-disabled-style"
          tree-default-expand-all
          :tree-data="applicationsOptions"
        >
          <!-- <template #title="{ value, label }">
            {{ `${value}${label}` }}
          </template> -->
        </a-tree-select>
      </template>
    </BasicForm>
  </BasicDrawer>
</template>
<script lang="ts" setup>
  import { ref, onMounted, computed, unref } from 'vue';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { getFormSchema, OpenDrawerType } from './index.data';
  import { BasicDrawer, useDrawerInner } from '/@/components/Drawer';
  import { saveOrUpdate, list } from './index.api';
  import { useDrawerAdaptiveWidth } from '/@/hooks/jeecg/useAdaptiveWidth';
  import { initDictOptions } from '/@/utils/dict';

  const unitList = ref<any>([]);
  initDictOptions('accessory_sku_unit').then((res: any) => {
    unitList.value = res;
  });

  const treeData = ref([]);
  const spuIdToSPU = new Map();
  onMounted(() => {
    getTreeData();
  });
  async function getTreeData() {
    let result = await list({});
    console.log('🚀 ~ result:', result);
    result = result.map(item => {
      if (item.spuList && item.spuList.length > 0) {
        item.spuList.forEach(item => {
          item.label = item.categoryTwoName || '';
          item.value = item.id;
          spuIdToSPU.set(item.id, item.spuName);
        });
      }
      return {
        label: item.categoryName,
        id: `category${item.categoryId}`,
        value: `category${item.categoryId}`,
        children: item.spuList,
        disabled: true,
      };
    });
    treeData.value = result;
    console.log('🚀 ~ result:', result);
  }
  const applicationsOptions = ref([
    {
      label: 'parent 1',
      value: 'xx',
      disabled: true,
      children: [
        {
          label: 'parent 1-0',
          value: 'xx1',
        },
        {
          label: 'parent 1-1',
          value: 'xx2',
        },
      ],
    },
  ]);
  const props = defineProps({
    skuType: Number, // 	类型（1：项目；2：配件）
  });
  // 声明Emits
  const emit = defineEmits(['success', 'register']);
  const isUpdate = ref(true);
  //表单配置
  const [registerForm, { setProps, resetFields, setFieldsValue, validate }] = useForm({
    schemas: getFormSchema(props.skuType === 1),
    labelWidth: 200,
    showActionButtonGroup: false,
  });

  const showFooter = ref(true);

  //表单赋值
  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async data => {
    console.log('是否带过来数据...', data, data.record);
    getTreeData();
    await resetFields();
    showFooter.value = data?.showFooter ?? true;
    setDrawerProps({ confirmLoading: false, showFooter: showFooter.value });
    isUpdate.value = !!data?.isUpdate;
    let values: any = {};
    // 无论新增还是编辑，都可以设置表单值
    if (typeof data.record === 'object') {
      values = {
        ...data.record,
        skuType: props.skuType,
      };
    }
    if (data.openType) {
      values.openType = data.openType;
    }
    values.skuCode = '1231231';
    setFieldsValue(values);
    setProps({ disabled: !showFooter.value });
  });
  function changeSpuId(value) {
    setFieldsValue({
      spuName: spuIdToSPU.get(value) || '',
    });
  }
  //获取标题
  const title = computed(() => {
    if (unref(showFooter) == false) {
      return '详情';
    } else {
      return !unref(isUpdate) ? '新增' : '编辑';
    }
  });

  const { adaptiveWidth } = useDrawerAdaptiveWidth();

  //提交事件
  async function handleSubmit() {
    try {
      let values = await validate();
      console.log('🚀 ~ handleSubmit ~ values:', values);
      setDrawerProps({ confirmLoading: true });
      let isUpdateVal = unref(isUpdate);
      //提交表单
      await saveOrUpdate({ ...values }, isUpdateVal);
      //关闭弹窗
      closeDrawer();
      //刷新列表
      emit('success', { isUpdateVal, values });
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  }
</script>
<style lang="less">
  .customize-disabled-style {
    .ant-select-tree .ant-select-tree-treenode-disabled .ant-select-tree-node-content-wrapper {
      color: black;
    }
  }
</style>
