import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '@/hooks/web/useMessage';
import dayjs from 'dayjs';

const { createMessage } = useMessage();

enum Api {
  list = '/ov/api/externalGps/list',
  exportExcel = '/ov/api/externalGps/export',
  supplier = '/ov/api/gpsSupplier/list',
}

/**
 * 外接GPS-分页列表查询
 * @param params
 */

export const listApi = params => defHttp.post({ url: Api.list, params });
/**
 * 供应商列表
 * @param params
 */
export const supplierListApi = params => defHttp.get({ url: Api.supplier, params });

// 列表数据导出
// export const logExportFiles = Api.logExport;
export const exportFiles = async params => {
  const data = await defHttp.post(
    { url: Api.exportExcel, params: params, responseType: 'blob' },
    { isTransformResponse: false, isReturnNativeResponse: true }
  );

  // const contentDisposition = data.headers['content-disposition'];

  if (!data?.data) {
    createMessage.warning('文件下载失败');
    return;
  }
  // 处理name添加时间戳(模块名_年-月-日_时.分.秒)
  const name = `外接GPS_${dayjs().format('YYYY-MM-DD_HH.mm.ss')}`;
  const fileSuffix = '.xls';
  const blobOptions = { type: 'application/vnd.ms-excel' };
  const url = window.URL.createObjectURL(new Blob([data?.data], blobOptions));

  // if (contentDisposition) {
  //   const matches = contentDisposition.match(/filename="?([^"]+)"?/);
  //   if (matches != null && matches[1]) {
  //     const decodeName = decodeURIComponent(matches[1]); // 解码文件名，支持中文
  //     const bytes = new Uint8Array(decodeName.split('').map(char => char.charCodeAt(0)));
  //     name = new TextDecoder('utf-8').decode(bytes);
  //   }
  // }

  const link = document.createElement('a');
  link.style.display = 'none';
  link.href = url;
  link.setAttribute('download', name + fileSuffix);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link); //下载完成移除元素
  window.URL.revokeObjectURL(url); //释放掉blob对象
};
