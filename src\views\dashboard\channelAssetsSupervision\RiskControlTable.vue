<template>
  <div class="risk-control-table">
    <a-table
      :columns="columns"
      :data-source="expandedTableData"
      :loading="loading"
      :pagination="false"
      :scroll="{ y: screenHeight + 'px' }"
      bordered
      size="middle"
      row-key="id"
      :row-class-name="getRowClassName"
      :show-expand-column="false"
    >
      <!-- 自定义单元格渲染 -->
      <template #bodyCell="{ column, record, index }">
        <!-- 车辆型号列自定义渲染 -->
        <template v-if="column.key === 'model'">
          <div class="vehicle-model-cell">
            <span>{{ record.model }}</span>
            <a-button
              v-if="!record.isChild && record.children && record.children.length > 0"
              type="link"
              size="small"
              @click="toggleExpand(record, index)"
              class="expand-btn"
            >
              <DownOutlined :style="{ color: '#333' }" v-if="expandedRows.has(record.id)" />
              <RightOutlined :style="{ color: '#333' }" v-else />
            </a-button>
          </div>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, nextTick } from 'vue';
  import { getColumns } from './index.data';
  import { DownOutlined, RightOutlined } from '@ant-design/icons-vue';

  const props = withDefaults(
    defineProps<{
      data?: any[];
      loading?: boolean;
      selectedDate?: string;
      zoomRate?: number;
      screenHeight?: number;
    }>(),
    {
      data: () => [],
      loading: false,
      selectedDate: '2024/07/11',
      zoomRate: 1,
      screenHeight: window.screen.height,
    }
  );

  // 响应式数据，用于控制展开状态
  const expandedRows = ref<Set<string>>(new Set());

  // 表格列配置
  const columns = computed(() => getColumns(expandedRows.value, expandedTableData.value, props.selectedDate));

  // 处理真实数据
  const processedData = computed(() => {
    if (!props.data || props.data.length === 0) {
      return [];
    }

    // 使用原始数据，不进行排序
    const sortedData = props.data;

    // 按大区分组
    const regionGroups = new Map();
    sortedData.forEach((item: any) => {
      if (!regionGroups.has(item.cityName)) {
        regionGroups.set(item.cityName, []);
      }
      regionGroups.get(item.cityName).push(item);
    });

    const result: any[] = [];
    let globalIndex = 0;

    // 处理每个大区
    regionGroups.forEach(regionItems => {
      const regionStartIndex = result.length;

      // 按渠道分组（cityName + lesseeName）
      const channelGroups = new Map();
      regionItems.forEach((item: any) => {
        const channelKey = `${item.cityName}-${item.lesseeName}`;
        if (!channelGroups.has(channelKey)) {
          channelGroups.set(channelKey, []);
        }
        channelGroups.get(channelKey).push(item);
      });

      let channelIndex = 0;
      // 处理每个渠道
      channelGroups.forEach(channelItems => {
        // 为每个渠道创建一个"总体"行
        const firstItem = channelItems[0];
        const itemId = `main-${globalIndex}`;
        // 默认显示总体行
        const processedItem: any = {
          ...firstItem,
          id: itemId,
          isChild: false,
          channelRowSpan: 1, // 每个渠道只有一行
          children: channelItems
            .filter(ele => ele.model !== '总体')
            .map((item: any) => ({
              ...item,
              id: `${itemId}-child-${item.model}`,
            })), // 将所有型号数据作为children
          channelIndex,
        };
        result.push(processedItem);
        channelIndex++;
        globalIndex++;
      });

      // 设置大区合并
      const regionEndIndex = result.length - 1;
      const regionRowCount = regionEndIndex - regionStartIndex + 1;
      if (result[regionStartIndex]) {
        result[regionStartIndex].regionRowSpan = regionRowCount;
      }
      for (let i = regionStartIndex + 1; i <= regionEndIndex; i++) {
        if (result[i]) {
          result[i].regionRowSpan = 0;
        }
      }
    });

    // 九方总体合并单元格逻辑
    result[result.length - 1].regionColSpan = 2;
    result[result.length - 1].channelColSpan = 0;
    result[result.length - 1].summaryIndex = 0;

    return result;
  });

  // 扁平化数据处理
  const flattenedData = computed(() => {
    return processedData.value;
  });

  // 动态展开的数据
  const expandedTableData = computed(() => {
    const result: any[] = [];

    flattenedData.value.forEach((item: any) => {
      result.push(item);

      if (expandedRows.value.has(item.id) && item.children && item.children.length > 0) {
        item.children.forEach((child: any, childIndex: number) => {
          const childRow = {
            ...child,
            id: `${item.id}-child-${childIndex}`,
            cityName: '',
            lesseeName: '',
            model: child.model, // 直接使用子数据的原始model，不使用总体
            isChild: true,
          };
          result.push(childRow);
        });
      }
    });

    return result;
  });

  // 切换展开状态
  const toggleExpand = (record: any, index: number) => {
    const flag = index >= expandedTableData.value.length - 4;
    if (expandedRows.value.has(record.id)) {
      expandedRows.value.delete(record.id);
    } else {
      expandedRows.value.add(record.id);
    }
    if (flag && record.children?.length) {
      nextTick(() => {
        setTimeout(() => {
          scrollToBottom();
        }, 50); // 延迟更安全
      });
    }
  };

  const scrollToBottom = () => {
    const tableBody = document.querySelector('.ant-table-body');
    if (tableBody) {
      tableBody.scrollTop = tableBody.scrollHeight;
    }
  };
  /**
   * 在线率≥95%：无
   * 95%>在线率≥90%：橙黄
   * 90%＞在线率：橙红
   * 运营率≥85%：无
   * 85%>运营率≥75%：橙黄
   * 75%＞运营率：橙红
   */
  // 获取行的CSS类名
  const getRowClassName = (record: any) => {
    const classList: string[] = [];

    if (!record.thisWeekOnlineRate && !record.thisWeekOperatingRate) {
      classList.push('row-class');
    }

    if (record.lesseeName === '合计') {
      classList.push('summary-row');
    }

    const applyRateClass = (rateValue: number | undefined, type: number) => {
      if (rateValue === undefined) return;

      const rate = rateValue * 100;

      const isNotFirst = record.channelIndex > 0 || record.summaryIndex === 0;
      const isChild = record.isChild;
      if (type === 2) {
        if (rate < 75) {
          classList.push(isNotFirst ? 'low-not-first-row' : 'low-operation-rate');
          if (isChild) classList.push('low-child-row');
        } else if (rate < 85) {
          classList.push(isNotFirst ? 'medium-not-first-row' : 'medium-operation-rate');
          if (isChild) classList.push('medium-child-row');
        }
      } else {
        if (rate < 90) {
          classList.push(isNotFirst ? 'low-not-first-row' : 'low-operation-rate');
          if (isChild) classList.push('low-child-row');
        } else if (rate < 95) {
          classList.push(isNotFirst ? 'medium-not-first-row' : 'medium-operation-rate');
          if (isChild) classList.push('medium-child-row');
        }
      }
    };

    applyRateClass(record.thisWeekOnlineRate, 1);
    applyRateClass(record.thisWeekOperatingRate, 2);

    return classList.join(' ');
  };

  // 导出组件方法供外部使用
  defineExpose({
    // 可以在这里导出需要的方法
    expandedRows,
    expandedTableData,
  });
</script>

<style lang="less" scoped>
  .risk-control-table {
    .vehicle-model-cell {
      display: flex;
      align-items: center;
      // justify-content: center;
      position: relative;

      .expand-btn {
        position: absolute;
        right: 0;
        margin-right: 0;
        padding: 0;
        // min-width: 20px;

        :deep(.anticon) {
          font-size: 12px;
        }
      }
    }

    :deep(.ant-table-tbody) {
      .summary-row {
        td {
          font-weight: 600;
        }
      }
      .medium-not-first-row {
        td {
          background-color: #ffb143 !important; // 浅橙黄色
        }
      }
      // 橙黄色背景 - 75% <= 运营率 < 85%
      .medium-operation-rate {
        td:not(:first-child) {
          background-color: #ffb143 !important; // 浅橙黄色
        }
      }
      .medium-child-row {
        td {
          background-color: #ffb143 !important;
        }
      }
      .low-operation-rate {
        td:not(:first-child) {
          background-color: #ff7043 !important; // 浅橙红色
        }
      }
      .low-child-row {
        td {
          background-color: #ff7043 !important;
        }
      }
      .low-not-first-row {
        td {
          background-color: #ff7043 !important;
        }
      }
    }

    :deep(.ant-table) {
      border-color: #c5c4c4 !important;

      .ant-table-thead > tr > th {
        background-color: #f5f5f5;
        font-weight: 600;
        text-align: center;
        padding: 8px;
        border-color: #c5c4c4 !important;
        white-space: pre-line; // 支持换行显示
        font-size: var(--fontSize);
      }

      .ant-table-thead > tr:first-child > th {
        border-top: 1px solid #c5c4c4 !important;
      }

      .ant-table-tbody > tr > td {
        text-align: center;
        padding: 12px 8px;
        border-color: #c5c4c4 !important;
        font-size: var(--fontSize);
      }

      // 特殊处理大区列的表头样式
      .ant-table-thead > tr:first-child > th:first-child {
        position: relative;

        &::before {
          content: '';
          position: absolute;
          top: 50%;
          left: 0;
          right: 0;
          height: 1px;
          background-color: #c5c4c4;
          transform: translateY(-50%);
        }
      }
      .ant-table-container {
        border-color: #c5c4c4 !important;
        border-left: 1px solid #c5c4c4 !important;
      }
    }
  }
</style>
