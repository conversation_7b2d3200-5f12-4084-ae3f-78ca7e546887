import { message } from 'ant-design-vue';
import { useGlobSetting } from '/@/hooks/setting';
import { defHttp } from '/@/utils/http/axios';
const globSetting = useGlobSetting();
const baseUploadUrl = globSetting.uploadUrl;
enum Api {
  positionList = '/sys/position/list',
  userList = '/sys/user/list',
  roleList = '/sys/role/list',
  queryDepartTreeSync = '/sys/sysDepart/queryDepartTreeSync',
  queryTreeList = '/sys/sysDepart/queryTreeList',
  loadTreeData = '/sys/category/loadTreeData',
  loadDictItem = '/sys/category/loadDictItem/',
  getDictItems = '/sys/dict/getDictItems/',
  getTableList = '/sys/user/queryUserComponentData',
  getCategoryData = '/sys/category/loadAllData',
  getUserAll = '/sys/user/all',
  queryAllCity = '/veh/asset/queryAllCity',
  getVehicleColors = '/veh/asset/getVehicleColors',
  enterList = '/mdm/ent/user/list',
  // 审批发起接口
  dingTalkApproval = '/bo/leaseContract/submitApproval',
  personList = '/sys/user/list',

  submitApproval = '/bo/boLeaseOrder/submitApproval',
  pickupSubmitApproval = '/bo/boVehiclePickupApplication/submitApproval',
  pickupTaskSubmitApproval = '/bo/vehiclePickupTask/submitApproval',
  depositDetailsSubmitApproval = '/bo/depositDetails/submitApproval',
  remotecontrolSubmitApproval = '/veh/remotecontrol/submitApproval',
  // 审批详情接口
  // 租赁订单-获取审批信息
  leaseOrderGetApproval = '/bo/boLeaseOrder/getApproval',
  // 提车申请单-获取审批信息
  pickupApplicationGetApproval = '/bo/boVehiclePickupApplication/getApproval',
  // 远程控制-获取审批信息
  remotecontrolGetApproval = '/veh/remotecontrol/getApproval',
  // 交后管理-获取审批信息
  vehiclePickupTaskGetApproval = '/bo/vehiclePickupTask/getApproval',
  // 押金收支-获取审批信息
  depositDetailsGetApproval = '/bo/depositDetails/getApproval',
}

/**
 * 上传父路径
 */
export const uploadUrl = `${baseUploadUrl}/ov/api/sys/common/upload`;

/**
 * 职务列表
 * @param params
 */
export const getPositionList = params => {
  return defHttp.get({ url: Api.positionList, params });
};

/**
 * 用户列表
 * @param params
 */
export const getUserList = params => {
  return defHttp.get({ url: Api.userList, params });
};

/**
 * 角色列表
 * @param params
 */
export const getRoleList = params => {
  return defHttp.get({ url: Api.roleList, params });
};

/**
 * 异步获取部门树列表
 */
export const queryDepartTreeSync = (params?) => {
  return defHttp.get({ url: Api.queryDepartTreeSync, params });
};
/**
 * 获取部门树列表
 */
export const queryTreeList = (params?) => {
  return defHttp.get({ url: Api.queryTreeList, params });
};

/**
 * 分类字典树控件 加载节点
 */
export const loadTreeData = (params?) => {
  return defHttp.get({ url: Api.loadTreeData, params });
};

/**
 * 根据字典code加载字典text
 */
export const loadDictItem = (params?) => {
  return defHttp.get({ url: Api.loadDictItem, params });
};

/**
 * 根据字典code加载字典text
 */
export const getDictItems = dictCode => {
  return defHttp.get({ url: Api.getDictItems + dictCode }, { joinTime: false });
};
/**
 * 部门用户modal选择列表加载list
 */
export const getTableList = params => {
  return defHttp.get({ url: Api.getTableList, params });
};
/**
 * 加载全部分类字典数据
 */
export const loadCategoryData = params => {
  return defHttp.get({ url: Api.getCategoryData, params });
};
/**
 * 文件上传
 */
export const uploadFile = (params, success) => {
  return defHttp.uploadFile({ url: uploadUrl }, params, { success });
};
/**
 * 下载文件
 * @param url 文件路径
 * @param fileName 文件名
 * @param parameter
 * @returns {*}
 */
export const downloadFile = (url, fileName?, parameter?) => {
  return getFileblob(url, parameter).then(data => {
    if (!data || data.size === 0) {
      message.warning('文件下载失败');
      return;
    }
    if (typeof window.navigator.msSaveBlob !== 'undefined') {
      window.navigator.msSaveBlob(new Blob([data]), fileName);
    } else {
      const url = window.URL.createObjectURL(new Blob([data]));
      const link = document.createElement('a');
      link.style.display = 'none';
      link.href = url;
      link.setAttribute('download', fileName);
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link); //下载完成移除元素
      window.URL.revokeObjectURL(url); //释放掉blob对象
    }
  });
};

/**
 * 下载文件 用于excel导出
 * @param url
 * @param parameter
 * @returns {*}
 */
export const getFileblob = (url, parameter) => {
  return defHttp.get(
    {
      url: url,
      params: parameter,
      responseType: 'blob',
    },
    { isTransformResponse: false }
  );
};

/**
 * 【用于评论功能】自定义文件上传-方法
 */
export const uploadMyFile = (url, data) => {
  return defHttp.uploadMyFile(url, data);
};
/**
 * 用户管理-查询所有未删除的用户
 * @param params
 */
export const getUserAllList = params => {
  return defHttp.get({ url: Api.getUserAll, params });
};
/**
 * 获取企业标签数据
 * @param params
 */
export const getEnterList = params => {
  return defHttp.get({ url: Api.enterList, params: { ...params, pageNo: 1, pageSize: 99999 } });
};
// 获取所有车辆运营城市
export const queryAllCity = async params => {
  const result = await defHttp.get({ url: Api.queryAllCity, params });
  handleResToOptions(result, { value: 'areaCode', label: 'name' });
  return result;
};
/**
 * 将结果处理成ant design组件能处理的格式
 * @param arr 数组，可带children进行遍历
 * @param fieldConversion eg: {value: 'areaCode',label: 'name'}
 * @returns
 */
function handleResToOptions(arr: Array<any>, fieldConversion: Object) {
  if (!arr || arr.length === 0) {
    return arr || [];
  }
  const keys = Object.keys(fieldConversion);
  arr.forEach(item => {
    keys.forEach(key => {
      // key => value
      item[key] = item[fieldConversion[key]];
    });
    if (item.children) {
      handleResToOptions(item.children, fieldConversion);
    }
  });
}
// 车身颜色
export const getVehicleColors = () => defHttp.get({ url: Api.getVehicleColors });

/**
 * 审批详情
 * @param params
 */
export const getApprovalInfo = params => {
  const { id: businessId, busType: businessType, tempType: templateType, url, method } = params;
  return defHttp[method]({
    url,
    params: { businessId, businessType, templateType },
  });
};

/**
 * 发起钉钉审批
 * @param params
 */
export const initDingTalkApproval = params => {
  return defHttp.post({ url: Api.dingTalkApproval, params });
};

export const getPerson = params => defHttp.get({ url: Api.personList, params });


/**
 * 租赁订单-提交审批
 * @param params
 */
export const submitApproval = params => {
  return defHttp.post({ url: Api.submitApproval, params });
};
/**
 * 提车申请单-提交审批
 * @param params
 */
export const pickupSubmitApproval = params => {
  return defHttp.post({ url: Api.pickupSubmitApproval, params });
};
/**
 * 交后管理-提交审批
 * @param params
 */
export const pickupTaskSubmitApproval = params => {
  return defHttp.post({ url: Api.pickupTaskSubmitApproval, params });
};
/**
 * 押金收支-提交审批
 * @param params
 */
export const depositDetailsSubmitApproval = params => {
  return defHttp.post({ url: Api.depositDetailsSubmitApproval, params });
};
/**
 * 远程控制-提交审批
 * @param params
 */
export const remotecontrolSubmitApproval = params => {
  return defHttp.post({ url: Api.remotecontrolSubmitApproval, params });
};

/**
 * 租赁订单-提交审批
 * @param params
 */
export const leaseOrderGetApproval = params => {
  return defHttp.post({ url: Api.leaseOrderGetApproval, params });
};
/**
 * 提车申请单-提交审批
 * @param params
 */
export const pickupApplicationGetApproval = params => {
  return defHttp.post({ url: Api.pickupApplicationGetApproval, params });
};
/**
 * 远程控制-提交审批
 * @param params
 */
export const remotecontrolGetApproval = params => {
  return defHttp.post({ url: Api.remotecontrolGetApproval, params });
};
/**
 * 交后管理-提交审批
 * @param params
 */
export const vehiclePickupTaskGetApproval = params => {
  return defHttp.post({ url: Api.vehiclePickupTaskGetApproval, params });
};
/**
 * 押金收支-提交审批
 * @param params
 */
export const depositDetailsGetApproval = params => {
  return defHttp.post({ url: Api.depositDetailsGetApproval, params });
};