import type { GlobConfig } from '/#/config';

import { getAppEnvConfig } from '/@/utils/env';

export const useGlobSetting = (): Readonly<GlobConfig> => {
  const {
    VITE_GLOB_APP_CODE,
    VITE_GLOB_APP_TITLE,
    VITE_GLOB_API_URL,
    VITE_GLOB_APP_SHORT_NAME,
    VITE_GLOB_API_URL_PREFIX,
    VITE_GLOB_APP_CAS_BASE_URL,
    VITE_GLOB_APP_OPEN_SSO,
    VITE_GLOB_APP_OPEN_QIANKUN,
    VITE_GLOB_DOMAIN_URL,
    VITE_GLOB_ONLINE_VIEW_URL,
    VITE_GLOB_APP_MAP_AK,
    VITE_GLOB_APP_GAODE_MAP_AK,
    VITE_GLOB_APP_TIANDI_MAP_TK,
    VITE_GLOB_API_FILE_PRIVIEW, // 文件预览地址
    VITE_GLOB_ONLYOFFICE_URL, // onlyOffice 服务地址
    VITE_GLOB_ONLYOFFICE_PREVIEW_URL, // onlyOffice 文件预览地址
  } = getAppEnvConfig();

  if (!/[a-zA-Z\_]*/.test(VITE_GLOB_APP_SHORT_NAME)) {
    // warn(
    //   `VITE_GLOB_APP_SHORT_NAME Variables can only be characters/underscores, please modify in the environment variables and re-running.`
    // );
  }
  // Take global configuration
  const glob: Readonly<GlobConfig> = {
    code: VITE_GLOB_APP_CODE,
    title: VITE_GLOB_APP_TITLE,
    domainUrl: VITE_GLOB_DOMAIN_URL,
    apiUrl: VITE_GLOB_API_URL,
    shortName: VITE_GLOB_APP_SHORT_NAME,
    openSso: VITE_GLOB_APP_OPEN_SSO,
    openQianKun: VITE_GLOB_APP_OPEN_QIANKUN,
    casBaseUrl: VITE_GLOB_APP_CAS_BASE_URL,
    urlPrefix: VITE_GLOB_API_URL_PREFIX,
    uploadUrl: VITE_GLOB_DOMAIN_URL,
    viewUrl: VITE_GLOB_ONLINE_VIEW_URL,
    mapAk: VITE_GLOB_APP_MAP_AK,
    aMapAK: VITE_GLOB_APP_GAODE_MAP_AK,
    tiandiMapTk: VITE_GLOB_APP_TIANDI_MAP_TK,
    filePreview: VITE_GLOB_API_FILE_PRIVIEW,
    onlyOfficeUrl: VITE_GLOB_ONLYOFFICE_URL, // onlyOffice 服务地址
    onlyOfficePerviewUrl: VITE_GLOB_ONLYOFFICE_PREVIEW_URL, // onlyOffice 文件预览地址
  };
  // window._CONFIG['domianURL'] = VITE_GLOB_DOMAIN_URL;
  return glob as Readonly<GlobConfig>;
};
