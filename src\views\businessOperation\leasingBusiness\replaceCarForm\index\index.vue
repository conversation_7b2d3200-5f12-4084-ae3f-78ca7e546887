<template>
  <a-spin :spinning="pageLoading">
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!-- 交车状态查询 -->
      <template #form-operation="{ model, field }">
        <JMultipleTextSelector
          v-model:value="model[field]"
          :options="carStatusList"
          :is-multiple="true"
          value-key="value"
        />
      </template>
      <template #form-approval="{ model, field }">
        <JMultipleTextSelector
          v-model:value="model[field]"
          :options="approvalStatusList"
          :is-multiple="false"
          value-key="value"
        />
      </template>
      <!-- 交车状态 -->
      <template #deliveryStatus="{ record }">
        <div class="common-state-box">
          <div class="common-state-box__wrap">
            <span :class="['state-dot', `status-${record.deliveryStatus}`]"></span>
            <span>{{ getNameByValue(record.deliveryStatus, carStatusList) }}</span>
          </div>
        </div>
      </template>
      <!-- 审核状态 -->
      <template #approvalStatus="{ record }">
        <div v-if="record.approvalStatus !== null && record.approvalStatus !== void 0">
          <StateBox
            :text="approvalStatusObj[record.approvalStatus].text"
            :stateBC="approvalStatusObj[record.approvalStatus].stateBC"
          />
        </div>
        <div v-else>-</div>
      </template>
      <!-- 操作 -->
      <template #tableTitle v-if="hasPermission(`boReplaceOrder${myContractType}:export`)">
        <a-button v-if="hasPermission(`boReplaceOrder${myContractType}:export`)" type="primary" @click="handleExport"
          >导出替换车单</a-button
        >
        <a-button
          v-if="hasPermission(`boReplaceOrder${myContractType}:batchReturn`)"
          @click="handleBatchReturnCar"
          :disabled="returnCarBtnComputed"
          >批量申请退车</a-button
        >
      </template>
      <template #action="{ record }">
        <!-- 交车状态: 1、待交车 2、交车中 3、已交车  4、已作废 5、已退车 -->
        <div class="flex items-center">
          <!-- 作废 待交车状态，作废状态显示，待交车可操作-->
          <a-button
            v-if="record.deliveryStatus == '1' && hasPermission(`boReplaceOrder${myContractType}:changeStatus`)"
            type="primary"
            size="small"
            ghost
            @click="handleCancellation(record)"
          >
            作废
          </a-button>
          <!-- 申请退车 已交车且审批通过 可操作-->
          <a-button
            v-if="
              record.deliveryStatus == '3' &&
              record.approvalStatus == '1' &&
              hasPermission(`boReplaceOrder${myContractType}:return`)
            "
            :disabled="record.deliveryStatus != '3'"
            type="primary"
            size="small"
            :ghost="record.deliveryStatus == '3'"
            @click="handleReturn(record)"
          >
            申请退车
          </a-button>
          <!-- 提交审核 已交车且待发起审批 -->
          <a-button
            v-if="
              record.deliveryStatus === 1 &&
              [-1, 2, 3].includes(record.approvalStatus) &&
              hasPermission(`boReplaceOrder${myContractType}:submitApproval`)
            "
            type="primary"
            size="small"
            @click="handleDingTalkApproval(record)"
          >
            提交审核
          </a-button>
          <!-- 详情 -->
          <a-button
            v-if="hasPermission(`boReplaceOrder${myContractType}:info`)"
            type="link"
            size="small"
            @click="handleDetail(record.id)"
            >详情</a-button
          >
          <!-- 导出 -->
          <a-button
            v-if="
              !moreHasPermission(record) &&
              record.deliveryStatus &&
              hasPermission(`boReplaceOrder${myContractType}:exportPdf`)
            "
            type="link"
            size="small"
            @click="handleExprotPdf(record)"
            >导出</a-button
          >
          <!-- 更多 -->
          <a-dropdown>
            <template #overlay>
              <a-menu>
                <!-- 交车中、已交车、退车中可操作 -->
                <template v-if="!['1', '4'].includes(record.deliveryStatus)">
                  <a-menu-item
                    v-if="hasPermission(`boReplaceOrder${myContractType}:reminder`)"
                    key="1"
                    @click="handleReminder(record)"
                    >催单</a-menu-item
                  >
                  <a-menu-item
                    v-if="hasPermission(`boReplaceOrder${myContractType}:reassignment`)"
                    key="2"
                    @click="handleReassignment(record)"
                    >改派</a-menu-item
                  >
                  <a-menu-item
                    v-if="hasPermission(`boReplaceOrder${myContractType}:edit`)"
                    key="3"
                    @click="handleEdit(record)"
                    >编辑</a-menu-item
                  >
                </template>
                <a-menu-item
                  v-if="hasPermission(`boReplaceOrder${myContractType}:exportPdf`)"
                  key="4"
                  @click="handleExprotPdf(record)"
                  >导出</a-menu-item
                >
              </a-menu>
            </template>
            <!-- 待交车，交车中 -->
            <a-button v-if="moreHasPermission(record)" type="link" size="small"> 更多 </a-button>
          </a-dropdown>
        </div>
      </template>
    </BasicTable>
    <!-- 作废/申请退车弹框 -->
    <CancellationDia v-model="cancellationDiaVisible" :id="selectId" :status="carStatus" @success="reload" />
    <!-- 改派弹框 -->
    <ReassignmentDia @register="registerDrawer1" @success="reload" />
    <!-- 退车弹框 -->
    <ReturnDia @register="registerDrawer" @success="reload" />
    <!-- 发起钉钉审批 -->
    <DingTalkApprovalDrawer
      ref="dingTalkApprovalDrawerRef"
      :schemas="dingTalkSchemas"
      :submitApproval="submitDingTalkApproval"
      @handle-values="handleValues"
      @register="registerDingTalkDrawer"
      @success="reload"
    />
  </a-spin>
</template>
<script lang="ts">
  let name = '01';
</script>
<script lang="ts" setup>
  import { Modal } from 'ant-design-vue';
  import { ref, computed } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import dayjs from 'dayjs';
  import { useUserStore } from '/@/store/modules/user';
  import DingTalkApprovalDrawer from '@/views/businessOperation/leasingBusiness/component/DingTalkApprovalDrawer.vue';
  import type { DTApprovalDrawerRefType } from '@/views/businessOperation/leasingBusiness/component/DingTalkApprovalDrawer.vue';
  import { approvalStatusObj } from '@/enums/contract.enum';
  import StateBox from '@/components/common/StateBox.vue';
  import ReassignmentDia from '@/views/businessOperation/leasingBusiness/pickupTaskForm/workOrderComponents/ReassignmentDia.vue'; // 改派弹框
  import CancellationDia from './_components/CancellationDia.vue'; // 作废弹框
  import ReturnDia from './_components/ReturnDia.vue';
  import { exportPdfUrl, exportXlsUrl, handleSendReminder, list, submitDingTalkApproval } from './index.api';
  import { columns, searchFormSchema, DingTalkApprovalFormSchema } from './index.data';
  import { useDrawer } from '/@/components/Drawer';
  import JMultipleTextSelector from '/@/components/Form/src/jeecg/components/JMultipleTextSelector.vue';
  import { BasicTable, FormSchema } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { handleExportXls } from '/@/utils/common/renderUtils';
  import { useMessage } from '/@/hooks/web/useMessage';
  import { usePermission } from '/@/hooks/web/usePermission';
  import { initDictOptions } from '/@/utils/dict';
  import { EContractType } from '/@/views/businessOperation/leasingBusiness/leaseContract/index/index.api';
  import { downloadFile } from '@/api/common/api';

  const { hasPermission } = usePermission();
  const { createMessage } = useMessage();

  const route = useRoute();
  const myContractType = ref<EContractType>(EContractType.LEASE_CONTRACT); // 默认租赁合同
  // 设置合同类型
  // 修改查询条件,表格字段(配置在index.data.ts中),根据实际业务,修改对应的查询条件,字段展示
  if (route.fullPath.includes('leasingBusiness')) {
    // 租赁合同
    name = '01';
    myContractType.value = EContractType.LEASE_CONTRACT;
    searchFormSchema[0].defaultValue = '01';
  } else if (route.fullPath.includes('leasePurchase')) {
    // 以租代售合同
    name = '02';
    myContractType.value = EContractType.LEASE_PURCHASE_CONTRACT;
    searchFormSchema[0].defaultValue = '02';
  }
  defineOptions({
    name: `businessOperation-leasingBusiness-replaceCarForm-${name}`,
  });
  // 初始化字典-交车状态
  const carStatusList = ref<any>([]);
  const approvalStatusList = ref<any>([]);
  const contractTypeList = ref<any>([]);

  initDictOptions('bo_contract_type').then(res => {
    contractTypeList.value = res;
  });
  initDictOptions('bo_pickup_status').then((res: any) => {
    carStatusList.value = res.slice(0, -2);
  });
  initDictOptions('approval_status').then(res => {
    approvalStatusList.value = res;
  });

  // 根据字典值获取数据
  function getNameByValue(value: string, list: any[]) {
    return list.find((item: any) => +item.value === +value)?.text;
  }

  const moreHasPermission = record => {
    return (
      record.approvalStatus === 1 &&
      [1, 2].includes(record.deliveryStatus) &&
      (hasPermission(`boReplaceOrder${myContractType.value}:reminder`) ||
        hasPermission(`boReplaceOrder${myContractType.value}:reassignment`) ||
        hasPermission(`boReplaceOrder${myContractType.value}:edit`))
    );
  };

  const selectId = ref<string>(''); // 当前行id
  const carStatus = ref<string>(''); // 状态流转的值
  // 批量退车按钮禁用
  const returnCarBtnComputed = computed(() => {
    const lessorId = selectedRows.value[0]?.lessorId;
    return (
      selectedRowKeys.value.length === 0 ||
      selectedRows.value.some(item => item.lessorId !== lessorId) ||
      selectedRows.value.some(item => item.deliveryStatus !== 3 || item.approvalStatus !== 1)
    );
  });

  // 列表页面公共参数、方法
  const queryParams = ref({});
  const { tableContext } = useListPage({
    tableProps: {
      api: list,
      rowKey: 'id',
      columns: columns,
      showIndexColumn: true,
      formConfig: {
        schemas: searchFormSchema,
        showAdvancedButton: false,
        labelWidth: 100,
      },
      actionColumn: {
        width: 260,
        fixed: 'right',
      },
      beforeFetch: params => {
        queryParams.value = params;
        return params;
      },
    },
  });

  //注册table数据
  const [registerTable, { reload }, { rowSelection, selectedRowKeys, selectedRows }] = tableContext;

  const pageLoading = ref<boolean>(false);
  /**
   * 导出任务单
   */
  async function handleExport() {
    pageLoading.value = true;
    let contractType = contractTypeList.value.find((item: any) => item.value === myContractType.value)?.text;
    contractType = contractType.slice(0, -2);
    await handleExportXls(`${contractType}替换车单`, exportXlsUrl, queryParams.value, false, 'get', true);
    pageLoading.value = false;
  }

  /**
   * 批量退车事件
   */
  function handleBatchReturnCar() {
    if (selectedRowKeys.value.length === 0) {
      createMessage.error('请选择数据');
      return;
    }
    // 只能选择状态为3的
    if (selectedRows.value.some(item => item.deliveryStatus !== 3)) {
      createMessage.error('请选择状态为“已交车”的数据');
      return;
    }
    selectId.value = selectedRowKeys.value.join(',');
    handleReturn({ id: selectId.value, stationId: selectedRows.value[0].stationId });
  }

  const cancellationDiaVisible = ref<boolean>(false); // 作废弹框是否显示
  /**
   * 作废
   */
  function handleCancellation(record: any) {
    selectId.value = record.id;
    carStatus.value = '4'; //
    Modal.confirm({
      title: '确认作废该订单吗？',
      style: { top: '35%' },
      content: '提示：作废后，替换车订单将会取消。',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        cancellationDiaVisible.value = true;
      },
    });
  }

  /**
   * 详情
   */
  const router = useRouter();
  function handleDetail(id: string) {
    router.push({
      path: `/businessOperation/leasingBusiness/replaceCarForm/detail`,
      query: {
        id,
        type: myContractType.value,
        t: new Date().getTime(),
      },
    });
  }

  /**
   * 催单
   * @param record
   */
  function handleReminder(record) {
    Modal.confirm({
      title: '确认催单吗？',
      style: { top: '35%' },
      content: '提示：催单后，24小时后可再次催单。',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        try {
          return await handleSendReminder({ id: record.id, status: record.deliveryStatus });
        } catch {
          return null;
        }
      },
    });
  }

  // #region 钉钉审批
  // 发起钉钉审批-注册弹框
  const [registerDingTalkDrawer, { openDrawer: openDingTalkDrawer }] = useDrawer();
  /**
   * 发起钉钉审批
   */
  function handleDingTalkApproval(record: any) {
    openDingTalkDrawer(true, {
      record,
      contractType: myContractType.value,
      tenantSaas: false,
      openCallBack: initDingTalkParams,
    });
  }
  const dingTalkSchemas = ref<Array<FormSchema>>(DingTalkApprovalFormSchema);
  const dingTalkApprovalDrawerRef = ref<DTApprovalDrawerRefType>(null);

  function initDingTalkParams(record: Recordable) {
    dingTalkApprovalDrawerRef.value?.setFieldsValue({
      ...record,
      oldModel: record.oldModel ?? '',
      oldVehiclePlate: record.oldVehiclePlate ?? '',
      oldVin: record.oldVin ?? '',
      model: '',
      vehiclePlate: '',
      vin: '',
      createBy: useUserStore().getUserInfo.realname + '/' + useUserStore().getUserInfo.workNo,
      applyTime: dayjs().format('YYYY-MM-DD'),
    });
  }
  function handleValues(params) {
    console.log('params:', params);
    try {
      params.fileList = params.fileList.split(',');
    } catch (err) {}
  }
  // #endregion 钉钉审批

  //注册drawer
  const [registerDrawer, { openDrawer }] = useDrawer();
  const [registerDrawer1, { openDrawer: openDrawer1 }] = useDrawer();
  /**
   * 改派
   * @param record
   */
  function handleReassignment(record) {
    Modal.confirm({
      title: '确认改派吗？',
      style: { top: '35%' },
      content: '提示：正在交付车辆的订单将不可改派。',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        openDrawer1(true, {
          record,
        });
      },
    });
  }

  function handleReturn(record) {
    Modal.confirm({
      title: '确认退车吗？',
      style: { top: '35%' },
      content: '提示：正在交付车辆的订单将不可退单。',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        openDrawer(true, {
          record,
        });
      },
    });
  }
  // 编辑
  function handleEdit(record) {
    router.push({
      path: `/businessOperation/leasingBusiness/replaceCarForm/edit`,
      query: {
        id: record.id,
        type: myContractType.value,
        t: new Date().getTime(),
      },
    });
  }
  async function handleExprotPdf(record: Recordable) {
    pageLoading.value = true;
    let contractType = contractTypeList.value.find((item: any) => item.value === myContractType.value)?.text;
    contractType = contractType.slice(0, -2);
    let name = `${contractType}替换车单_${record?.replaceOrderNo}.pdf`;
    await downloadFile(exportPdfUrl, name, { id: record.id })
      .then(() => {
        createMessage.success('下载成功');
      })
      .finally(() => {
        pageLoading.value = false;
      });
  }
</script>
<style lang="less" scoped>
  @import url('../common/styles.less');
</style>
