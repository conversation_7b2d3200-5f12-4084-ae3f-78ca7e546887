<template>
  <div ref="chartRef" :style="{ height, width }"></div>
</template>
<script lang="ts">
  import { defineComponent, PropType, ref, Ref, reactive, watchEffect } from 'vue';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { GaugeChart } from 'echarts/charts';
  import { cloneDeep } from 'lodash-es';
  import echarts from "@/utils/lib/echarts";

  export default defineComponent({
    name: 'Gauge',
    props: {
      chartData: {
        type: Object as any,
        default: () => ({}),
      },
      option: {
        type: Object,
        default: () => ({}),
      },
      series: {
        type: Object,
        default: () => ({}),
      },
      width: {
        type: String as PropType<string>,
        default: '100%',
      },
      height: {
        type: String as PropType<string>,
        default: 'calc(100vh - 78px)',
      },
    },
    setup(props) {
      const chartRef = ref<HTMLDivElement | null>(null);
      const { setOptions, echarts } = useECharts(chartRef as Ref<HTMLDivElement>);
      const option = reactive<any>({
        series: [
          // {
          //   type: 'gauge',
          //   progress: {
          //     show: false,
          //     width: 12,
          //   },
          //   axisLine: {
          //     lineStyle: {
          //       width: 12,
          //     },
          //   },
          //   axisTick: {
          //     show: false,
          //   },
          //   splitLine: {
          //     length: 12,
          //     lineStyle: {
          //       width: 1,
          //       color: '#999',
          //     },
          //   },
          //   axisLabel: {
          //     distance: 25,
          //     color: '#999',
          //     fontSize: 12,
          //   },
          //   anchor: {
          //     show: true,
          //     showAbove: true,
          //     size: 20,
          //     itemStyle: {
          //       borderWidth: 5,
          //     },
          //   },
          //   title: {},
          //   detail: {
          //     valueAnimation: true,
          //     fontSize: 25,
          //     formatter: '{value}%',
          //     offsetCenter: [0, '80%'],
          //   },
          //   data: [
          //     {
          //       value: 70,
          //       name: '本地磁盘',
          //     },
          //   ],
          // },
          {
            name: 'Pressure',
            type: 'gauge',
            detail: {
              formatter: '{value}',
            },
            data: [
              {
                value: 50,
                name: 'SCORE',
              },
            ],
            colorBy: 'data',
            center: ['50%', '50%'],// 图形中心点的偏移量
            // radius: '50', // 相对于容器，缩放图形
            splitNumber: 5,
            min: 0,
            max: 100,
            axisLine: {
              show: true,
              roundCap: true,
              lineStyle: {
                width: 20,
                shadowColor: '#015193',
                color: [
                  [
                    1,
                    new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                      {
                        offset: 0.1,
                        color: '#007cfd',
                      },
                      {
                        offset: 0.6,
                        color: '#00fef9',
                      },
                      {
                        offset: 1,
                        color: '#02ffa6',
                      },
                    ]),
                  ],
                ],
              },
            },
          },
        ],
      });

      watchEffect(() => {
        props.chartData && initCharts();
      });

      function initCharts() {
        echarts.use(GaugeChart);
        if (props.option) {
          console.log(option);
          mergeOptions(option, props.option.series, props.series);
        }
        console.log(option);
        option.series[0].data[0].name = props.chartData?.name;
        option.series[0].data[0].value = props.chartData?.value;
        setOptions(option);
      }

      function mergeOptions(option: any, optionSeries: any, series) {
        if (optionSeries) {
          Object.assign(option, cloneDeep(props.option));
        } else {
          Object.assign(option.series[0], cloneDeep(series[0]));
        }
      }

      return { chartRef };
    },
  });
</script>
