<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <a-button type="primary" v-auth="'vehicle:asset:import'" @click="handleImport">导入车辆</a-button>
        <a-button type="primary" v-auth="'vehicle:asset:export'" @click="handleExportXLS">导出列表</a-button>
      </template>

      <template #prepareStatus="{ record }">
        <div class="state-box">
          <div class="state-box__wrap"
            ><span :class="['state-dot', record.prepareStatus == 1 ? 'green' : 'red']"></span>
            <span>{{ record.prepareStatus == 1 ? '已整备' : '需整备' }}</span></div
          >
        </div>
      </template>
      <template #operationStatus="{ record }">
        <div class="state-box">
          <div class="state-box__wrap"
            ><span
              :class="['state-dot', record.operationStatus == 3 || record.operationStatus == 2 ? 'green' : 'red']"
            ></span>
            <span>{{
              record?.operationStatus == 1 ? '待运营' : record?.operationStatus == 2 ? '运营中' : '运营结束'
            }}</span></div
          >
        </div>
      </template>
      <template #businessStatus="{ record }">
        <div class="state-box">
          <div class="state-box__wrap"
            ><span
              :class="['state-dot', record.businessStatus == 1 ? 'blue' : 'green']"
              v-if="record?.businessStatus_dictText"
            ></span>
            <span>{{ record?.businessStatus_dictText }}</span></div
          >
        </div>
      </template>

      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)" />
      </template>
    </BasicTable>
    <vehicleAssetsDrawer @register="registerDrawer" @success="handleSuccess" :handleImport="onImportXls" />
    <a-modal
      v-model:visible="isVisible"
      title="批量查询"
      class="vehicleAssets-modal"
      @cancel="handleModalCancel"
      @ok="handleModalOk"
    >
      <BasicForm @register="registerForm">
        <template #fuzzyCarPlate="{ model, field }">
          <a-textarea
            v-model:value="model[field]"
            :rows="8"
            :maxlength="1000"
            class="modal-textArea"
            showCount
            @change="
              e => {
                handleInputChange(e, 'carPlate');
              }
            "
          />
        </template>
        <template #fuzzyVin="{ model, field }">
          <a-textarea
            v-model:value="model[field]"
            :rows="8"
            :maxlength="1000"
            class="modal-textArea"
            showCount
            @change="
              e => {
                handleInputChange(e, 'vin');
              }
            "
          />
        </template>
      </BasicForm>
    </a-modal>
  </div>
</template>

<script lang="ts" name="vehicleManagement-vehicleAssets" setup>
  //ts语法
  import { ref, nextTick, onMounted } from 'vue';
  import { BasicTable, TableAction, ActionItem } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import vehicleAssetsDrawer from './vehicleAssetsDrawer.vue';
  import { useDrawer } from '/@/components/Drawer';
  import { columns, searchFormSchema } from './index.data';
  import {
    list,
    deleteAssetsInfo,
    getExportUrl,
    getImportUrl,
    getBrandTree,
    getUserAll,
    downloadXls,
    getStations,
  } from './index.api';
  import { queryAllCity, getVehicleColors, getEnterList } from '@/api/common/api';
  import { BasicForm, useForm } from '/@/components/Form/index';
  import { createSessionStorage } from '/@/utils/cache';
  import { useGo } from '/@/hooks/web/usePage';
  import { initDictOptions } from '/@/utils/dict';
  import { usePermission } from '/@/hooks/web/usePermission';
  const { hasPermission } = usePermission();

  const carBrandOptions = ref<any>([]);
  const carModelOptions = ref<any>([]);
  const isVisible = ref(false);
  // const newWindow = ref();
  const ss = createSessionStorage();

  onMounted(() => {
    const { updateSchema } = getForm();
    Promise.all([initDictOptions('vehicle_color'),getVehicleColors()]).then(([resList,resExit])=>{
      const res = resList.filter(item=>{
        return resExit.includes(item.value)
      })
      updateSchema([{ field: 'vehicleColor', componentProps: { options: res || [], placeholder: '车身颜色', showSearch: false } }]);
    })

    initDictOptions('vehicle_prepare_status').then(res => {
      const arr = res.map(item => {
        return {
          key: item?.value,
          title: item?.label,
        };
      });
      updateSchema([{ field: 'prepareStatus', componentProps: { options: arr || [] } }]);
    });

    initDictOptions('vehicle_operation_status	').then(res => {
      const handleRes = res?.map(item => {
        return {
          key: item?.value,
          title: item?.title,
        };
      });
      updateSchema([{ field: 'operationStatus', componentProps: { options: handleRes || [] } }]);
    });
    initDictOptions('vehicle_business_status').then(res => {
      const handleRes = res?.map(item => {
        return {
          key: item?.value,
          title: item?.title,
        };
      });
      updateSchema([{ field: 'businessStatus', componentProps: { options: handleRes || [] } }]);
    });
    initDictOptions('vehicle_crates').then(res => {
      updateSchema([
        { field: 'crates', componentProps: { options: res || [], placeholder: '货箱类型', showSearch: false } },
      ]);
    });

    getStations().then(res => {
      const handleRes = res?.map(item => {
        return {
          value: item?.id || '',
          title: item?.stationName || '',
          label: item?.stationName || '',
          Text: item?.stationName || '',
        };
      });
      updateSchema([
        {
          field: 'stationId',
          componentProps: { options: handleRes || [], placeholder: '停放场站', showSearch: false },
        },
      ]);
    });

    getUserAll({
      pageSize: 10000,
    }).then(res => {
      let arr = [];
      if (res?.records?.length) {
        arr = res.records.map((item: any) => {
          return {
            value: item?.workNo || '',
            text: item?.realname || '',
            label: item?.realname || '',
            children: () => [{ children: item?.realname || '' }],
          };
        });
      }
      updateSchema([{ field: 'createBy', componentProps: { options: arr, placeholder: '请选择创建人' } }]);
    });
    initSearchOptions();
  });
  function initSearchOptions() {
    const { updateSchema } = getForm();
    getBrandTree().then((res: any) => {
      carBrandOptions.value = res.data || [];
      let handleRes = [];
      if (res?.data?.length) {
        handleRes = res.data.map((item: any) => {
          return { value: item?.vehicleBrandId || '', label: item?.vehicleBrand || '' };
        });
      }
      updateSchema([
        {
          field: 'brandId',
          componentProps: {
            options: handleRes,
            placeholder: '车辆品牌',
            onChange: e => {
              const { setFieldsValue } = getForm();
              setFieldsValue({ modelId: undefined });
              if (e) {
                const find = carBrandOptions.value.find(item => item.vehicleBrandId == e);
                carModelOptions.value = find?.modelList || [];
                let handleRes = carModelOptions.value.map(item => {
                  return { value: item?.id || '', label: item?.model || '' };
                });
                updateSchema([
                  {
                    field: 'modelId',
                    componentProps: { options: handleRes, placeholder: '车辆型号', showSearch: false },
                  },
                ]);
              } else {
                carModelOptions.value = [];
                updateSchema([
                  { field: 'modelId', componentProps: { options: [], placeholder: '车辆型号', showSearch: false } },
                ]);
              }
            },
            showSearch: false,
          },
        },
      ]);
    });
    queryAllCity({}).then(res => {
      updateSchema([
        {
          field: 'operationCityCode',
          componentProps: { apiOptions: res, isAreaSelect: true,showArea: false, placeholder: '运营城市' },
        },
      ]);
    });
    getEnterList({ tags: 3 }).then(res => {
      const handleRes =
        res?.records?.map(item => {
          return { value: item?.id || '', label: item?.entName || '' };
        }) || [];
      updateSchema([
        {
          field: 'assetOwnershipId',
          componentProps: { options: handleRes, placeholder: '资产归属', showSearch: false },
        },
      ]);
    });
    getEnterList({ tags: 4 }).then(res => {
      const handleRes =
        res?.records?.map(item => {
          return { value: item?.id || '', label: item?.entName || '' };
        }) || [];
      updateSchema([
        {
          field: 'operationOwnershipId',
          componentProps: { options: handleRes, placeholder: '运营归属', showSearch: false },
        },
      ]);
    });
  }

  const [registerDrawer, { openDrawer }] = useDrawer();
  //注册drawer
  // 列表页面公共参数、方法
  const { tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: '品牌管理',
      api: list,
      indexColumnProps: {
        width: 100,
      },
      rowKey: 'id',
      rowSelection: { type: 'checkbox' },
      columns: columns,
      size: 'small',
      showIndexColumn: true,
      formConfig: {
        schemas: searchFormSchema,
        labelWidth: 90,
        actionColOptions: { offset: 12 },
        resetFunc: () => {
          const { updateSchema } = getForm();
          updateSchema([
            { field: 'modelId', componentProps: { options: [], placeholder: '车辆型号', showSearch: false } },
          ]);
        },
      },
      actionColumn: {
        width: 120,
        fixed: 'right',
        ifShow: () => hasPermission('vehicle:asset:info'),
      },
      beforeFetch: params => {
        if (params?.createDate) {
          const handleArr = params.createDate?.split(',');
          if (handleArr?.length >= 2) {
            params.createStartTime = handleArr[0] + ' 00:00:00';
            params.createEndTime = handleArr[1] + ' 23:59:59';
          }
          delete params.createDate;
        }
        params.carPlate = params.carPlate && params.carPlate.length ? params.carPlate.replace(/\n|\r\n/g, ',') : '';
        params.vin = params.vin && params.vin.length ? params.vin.replace(/\n|\r\n/g, ',') : '';
        return Object.assign({ column: 'createTime', order: 'desc' }, params);
      },
    },
    importConfig: {
      url: getImportUrl,
    },
    exportConfig: {
      url: getExportUrl,
    },
  });

  //注册table数据
  const [registerTable, { reload, getForm }, { rowSelection, selectedRowKeys }] = tableContext;

  const [
    registerForm,
    {
      validateFields: validateModalFields,
      resetFields: resetModalFields,
      setFieldsValue: setModalFieldsValue,
      updateSchema: updateModalSchemas,
      clearValidate: clearModalValidate,
      getFieldsValue: getModalFieldsValue,
    },
  ] = useForm({
    labelWidth: 80,
    showActionButtonGroup: false,
    schemas: [
      {
        field: 'fuzzyCarPlate',
        label: '车牌号',
        component: 'InputTextArea',
        ifShow: false,
        componentProps: {
          rows: 5,
          maxlength: 1000,
          placeholder: '批量查询，用换行分隔',
          showCount: true,
          class: 'modal-textArea',
          allowClear: true,
        },
        slot: 'fuzzyCarPlate',
        colProps: { span: 24 },
        required: true,
      },
      {
        field: 'fuzzyVin',
        label: '车架号',
        component: 'InputTextArea',
        ifShow: false,
        componentProps: {
          rows: 5,
          maxlength: 1000,
          placeholder: '批量查询，用换行分隔',
          showCount: true,
          class: 'modal-textArea',
          allowClear: true,
        },
        colProps: { span: 24 },
        slot: 'fuzzyVin',
        required: true,
      },
    ],
  });

  function handleInputChange(e, type) {
    if (!e.target.value) return;
    const { setFieldsValue } = getForm();

    setFieldsValue({ [type]: '' });
    if (type == 'carPlate' || type == 'vin') {
      setModalFieldsValue({ [type]: '' });
    }
  }

  /**
   * 新增事件
   */
  function handleImport() {
    openDrawer(true, {
      isUpdate: false,
      showFooter: true,
      tenantSaas: false,
    });
  }
  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    const handleIds = selectedRowKeys.value.join(',');
    await deleteAssetsInfo({ ids: handleIds }, () => {
      selectedRowKeys.value = [];
      reload();
    });
  }
  /**
   * 成功回调
   */
  function handleSuccess() {
    reload();
    initSearchOptions();
  }

  const go = useGo();
  function handleDetail(record: Recordable) {
    const id = record?.id || '';
    ss.set('VEHICLE_ASSETS_DETAIL_ID', id);
    const url = `/vehicleManagement/vehicleAssets/detail?t=${new Date().getTime()}`;
    go(url);
  }

  /**
   * 操作栏
   */
  function getTableAction(record): ActionItem[] {
    return [
      {
        label: '详情',
        // onClick: handleEdit.bind(null, record),
        onClick: handleDetail.bind(null, record),
        ifShow: () => hasPermission('vehicle:asset:info'),
      },
    ];
  }
  /**
   * 下拉操作栏
   */
  function getDropDownAction(record): ActionItem[] {
    let actions = [];
    return actions;
  }

  async function handleClick(e, type = '') {
    e.preventDefault();
    isVisible.value = true;
    if (type == 'vin') {
      nextTick(() => {
        updateModalSchemas([
          {
            field: 'fuzzyVin',
            label: '车架号',
            ifShow: true,
          },
          {
            field: 'fuzzyCarPlate',
            label: '车牌号',
            ifShow: false,
          },
        ]);
      });
    }
    if (type == 'carPlate') {
      nextTick(() => {
        updateModalSchemas([
          {
            field: 'fuzzyCarPlate',
            label: '车牌号',
            ifShow: true,
          },
          {
            field: 'fuzzyVin',
            label: '车架号',
            ifShow: false,
          },
        ]);
      });
    }
    nextTick(() => {
      clearModalValidate();
    });
  }

  function handleModalCancel() {
    isVisible.value = false;
  }

  async function handleModalOk() {
    const res = await validateModalFields();
    isVisible.value = false;

    const { setFieldsValue } = getForm();
    if (res) {
      Object.keys(res).forEach(item => {
        let handle = '';
        if (res[item]) {
          handle = res[item].replace(/\n/g, ',');
        }
        setFieldsValue({ [item]: handle });
      });
    }
  }

  async function handleExportXLS() {
    const { validate } = getForm();
    const params = await validate();
    downloadXls(params);
  }
</script>

<style lang="less">
  .custom-item__width {
    width: 90px !important;
  }

  .vehicleAssets-input {
    &:hover {
      .ant-input-suffix .vehicleAssets-input__icon {
        display: inline-block;
      }
    }

    &__icon {
      cursor: pointer;
      display: none;
    }
  }
  .modal-textArea {
    &::after {
      margin-bottom: 0;
      position: absolute;
      right: 10px;
      bottom: 25px;
      z-index: 1;
      font-size: 14px;
    }
  }
  .vehicleAssets-modal {
    & > .ant-modal-content {
      & > .ant-modal-body {
        padding: 20px 0 0 0;
      }
    }
    .ant-col-20 {
      flex: 1;
      max-width: 100%;
    }

    .ant-col-4 {
      display: none;
    }
  }

  .custom-select {
    .ant-select-selector {
      .ant-select-selection-placeholder {
        color: black;
      }
      .ant-select-selection-item {
        color: #3a78f9 !important;
        font-size: 13px;
      }
    }
    .ant-select-arrow {
      color: black;
    }
  }

  .custom-search {
    .ant-btn {
      & > span {
        &:first-of-type {
          display: none;
        }
        .app-iconify {
          color: gray;
        }
      }
    }
  }

  .hide-separator .ant-picker-range-separator {
    display: none;
  }
</style>
<style scoped lang="less">
  .state-box {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    text-align: left;

    &__wrap {
      display: flex;
      align-items: center;
      width: 80%;
      min-width: 59.5px;
    }

    .state-dot {
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 15%;

      &.red {
        background-color: rgba(227, 96, 80, 1);
      }

      &.green {
        background-color: rgba(107, 217, 169, 1);
      }

      &.blue {
        background-color: rgba(74, 120, 241, 1);
      }
    }
  }
</style>
