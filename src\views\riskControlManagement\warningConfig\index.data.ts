import { BasicColumn, FormSchema } from '/@/components/Table';

//列表字段
export const columns: BasicColumn[] = [
  {
    title: 'BI报表名称',
    dataIndex: 'tableName',
    key: 'tableName',
  },
  {
    title: '推送规则',
    dataIndex: 'pushRules',
    key: 'pushRules',
    slots: { customRender: 'pushRules' },
  },
  {
    title: '群名称',
    dataIndex: 'pushScenario',
    key: 'pushScenario',
    slots: { customRender: 'pushScenario' },
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    slots: { customRender: 'status' },
  },
  {
    title: '更新人',
    dataIndex: 'updateBy',
    key: 'updateBy',
    customRender: ({ text, record }: { text: string; record: any }) => text ?? record.createBy,
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
  },
];
//查询字段
export const searchFormSchema: FormSchema[] = [
  {
    label: 'BI报表名称',
    field: 'tableName',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'warning_config_table_name',
      type: 'select',
      showChooseOption: false,
      stringToNumber: false,
    },
    colProps: { span: 6 },
  },
  {
    label: '群名称',
    field: 'groupName',
    component: 'Input',
    colProps: { span: 6, style: { marginLeft: '30px' } },
    componentProps: {
      placeholder: '请输入',
    },
  },
];

//新增字段
export const formSchema: FormSchema[] = [
  {
    label: 'id',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    label: '状态',
    field: 'status',
    component: 'Input',
    defaultValue: 1,
    show: false,
  },
  {
    label: 'BI报表',
    field: 'tableName',
    component: 'Select',
    slot: 'tableName',
    required: true,
  },
  {
    label: '执行规则',
    field: 'pushRules',
    component: 'Select',
    slot: 'pushRules',
    required: true,
  },
  {
    label: '执行时间',
    field: 'pushTime',
    component: 'DatePicker',
    required: true,
    slot: 'pushTime',
  },
  {
    label: '推送场景',
    field: 'pushScenario',
    component: 'Select',
    slot: 'pushScenario',
    required: true,
  },
  {
    label: '群名称',
    field: 'groupName',
    component: 'Input',
    componentProps: {
      placeholder: '请输入群名称',
    },
    required: true,
  },
  {
    label: 'Webhook',
    field: 'webhook',
    component: 'Input',
    componentProps: {
      placeholder: '请输入',
    },
    required: true,
  },
  {
    label: '加签',
    field: 'sign',
    component: 'Input',
    componentProps: {
      placeholder: '请输入',
    },
    required: true,
  },
  {
    label: '@群成员',
    field: 'atList',
    component: 'Input',
    slot: 'atList',
  },
];
