import { BasicColumn } from '/@/components/Table';
// 类型定义
export interface RegionInfo {
  id: string;
  name: string;
  code: string;
}

export interface ChannelInfo {
  name: string;
  modelList: VehicleStatisticsItem[];
}

export interface VehicleStatisticsItem {
  model: string;
  normalVehicle: number;
  abnormalVehicle: number;
  normalOperation: number;
  inefficient: number;
  longPause: number;
  offline30: number;
  offline15_30: number;
  offline15: number;
  offlineAll: number;
  notActivated: number;
  total: number;
}

export interface RiskControlTableItem {
  cityName: string;
  lesseeName: string;
  model: string;
  businessStatus: string;
  quantityDelivered: number;
  normal: number;
  inefficient: number;
  longPause: number;
  offlineAll: number;
  operatingRate: number;
  lastWeekOperatingRate: number;
  expanded?: boolean;
  children?: Array<{
    vehicleType: string;
    businessSubType: string;
    count: number;
  }>;
}

// 表格列配置函数，接收展开状态和表格数据
export const getColumns = (expandedRows: Set<string>, tableData?: any[]): BasicColumn[] => [
  {
    title: '业务中心',
    dataIndex: 'cityName',
    key: 'cityName',
    align: 'center',
    customRender: ({ record }: any) => {
      if (record.isChild) {
        return '';
      }
      // 如果是总体汇总或九方总体，显示合并的文本
      if (record.lesseeName && record.lesseeName.includes('总体')) {
        return record.lesseeName; // 显示渠道名称作为合并后的文本
      }
      return record.cityName;
    },

    customCell: (record: any) => {
      // console.log('record', record);
      // 展开的子行处理 - 所有子行都被父行合并
      if (record.isChild) {
        return {
          rowSpan: 0, // 被父行合并
          colSpan: 0, // 不占用列空间
        };
      }
      // 计算父行的 rowSpan（包括展开的子行）
      let rowSpan = record.regionRowSpan === 0 ? 0 : record.regionRowSpan || 1;
      let colSpan = record.regionColSpan === 0 ? 0 : record.regionColSpan || 1;

      // 特殊处理：总体汇总和九方总体需要合并大区和渠道列
      if (record.lesseeName && record.lesseeName.includes('总体')) {
        // 如果是渠道合并的主行，则在大区列显示并合并到渠道列
        if (record.channelRowSpan > 0) {
          rowSpan = record.channelRowSpan; // 使用渠道的 rowSpan
          colSpan = 2; // 合并大区和渠道两列
        } else {
          // 如果是被渠道合并的行，则在大区列也不显示
          rowSpan = 0;
          colSpan = 0;
        }
      }

      return {
        rowSpan: rowSpan,
        colSpan: colSpan,
      };
    },
    width: '8%',
  },
  {
    title: '渠道',
    dataIndex: 'lesseeName',
    key: 'lesseeName',
    align: 'left',
    customRender: ({ record }: any) => {
      if (record.isChild) {
        return '';
      }
      // 如果是总体汇总或九方总体，不显示内容（被大区列合并）
      if (record.lesseeName && record.lesseeName.includes('总体')) {
        return '';
      }
      return record.lesseeName;
    },
    customCell: (record: any) => {
      // 展开的子行处理 - 所有子行都被父行合并
      if (record.isChild) {
        return {
          rowSpan: 0, // 被父行合并
          colSpan: 0, // 不占用列空间
        };
      }

      // 特殊处理：总体汇总和九方总体的渠道列被大区列合并
      if (record.lesseeName && record.lesseeName.includes('总体')) {
        // 所有总体行的渠道列都被大区列合并
        return {
          rowSpan: 0, // 被大区列合并
          colSpan: 0, // 不占用列空间
        };
      }

      // 计算父行的 rowSpan（包括展开的子行）
      let rowSpan = record.channelRowSpan === 0 ? 0 : record.channelRowSpan || 1;
      const colSpan = record.channelColSpan === 0 ? 0 : record.channelColSpan || 1;

      // 使用 expandedRows 判断展开状态
      const isExpanded = expandedRows.has(record.id);

      // 如果该行已展开且有子数据，动态设置 rowSpan
      if (isExpanded && record.children && record.children.length > 0) {
        if (rowSpan > 0) {
          // 如果原来有合并，增加子行数量
          rowSpan += record.children.length;
        }
      }

      // 如果这是合并的主行（rowSpan > 0），检查同组内其他行的展开状态
      if (rowSpan > 0 && tableData) {
        // 查找同组内的其他展开行
        expandedRows.forEach((expandedId: string) => {
          if (expandedId !== record.id) {
            // 在 tableData 中找到对应的行
            const expandedRow = tableData.find(row => row.id === expandedId);
            if (expandedRow) {
              // 普通行：检查同一渠道的行
              const isSameChannel =
                expandedRow.cityName === record.cityName && expandedRow.lesseeName === record.lesseeName;

              if (isSameChannel) {
                rowSpan += expandedRow.children?.length || 2;
              }
            }
          }
        });
      }
      return {
        rowSpan,
        colSpan,
      };
    },
  },
  {
    title: '业务类型',
    dataIndex: 'businessStatus',
    key: 'businessStatus',
    align: 'left',
    width: '9%',
  },
  {
    title: '交付数量',
    dataIndex: 'quantityDelivered',
    key: 'quantityDelivered',
    align: 'left',
    width: '9%',
  },
  {
    title: '正常运营',
    dataIndex: 'normal',
    key: 'normal',
    align: 'left',
    width: '9%',
  },
  {
    title: '低效运营',
    dataIndex: 'inefficient',
    key: 'inefficient',
    align: 'left',
    width: '9%',
  },
  {
    title: '久停车辆',
    dataIndex: 'longPause',
    key: 'longPause',
    align: 'left',
    width: '9%',
  },
  {
    title: '离线车辆',
    dataIndex: 'offlineAll',
    key: 'offlineAll',
    align: 'left',
    width: '9%',
  },
  {
    title: '资产运营率',
    dataIndex: 'operatingRate',
    key: 'operatingRate',
    align: 'left',
    customRender: ({ text }) => (text ? (text * 100).toFixed(0) + '%' : '-'),
    width: '9%',
  },
  {
    title: '上周资产运营率',
    dataIndex: 'lastWeekOperatingRate',
    key: 'lastWeekOperatingRate',
    align: 'left',
    customRender: ({ text }) => (text ? (text * 100).toFixed(0) + '%' : '-'),
    width: '9%',
  },
];
