<template>
  <Cascader v-bind="attrs" :value="state" :options="getOptions" @change="handleChange" />
</template>
<script lang="ts">
  import { defineComponent, PropType, ref, reactive, watchEffect, computed, unref, watch, onMounted } from 'vue';
  import { Cascader } from 'ant-design-vue';
  import { provinceAndCityData, regionData, provinceAndCityDataPlus, regionDataPlus, matchTiandi } from '../../utils/areaDataUtil';
  import { useRuleFormItem } from '/@/hooks/component/useFormItem';
  import { propTypes } from '/@/utils/propTypes';
  import { useAttrs } from '/@/hooks/core/useAttrs';
  export default defineComponent({
    name: 'JAreaLinkage',
    components: {
      Cascader,
    },
    inheritAttrs: false,
    props: {
      value: propTypes.oneOfType([propTypes.object, propTypes.array]),
      //是否显示区县
      showArea: propTypes.bool.def(true),
      //是否是全部
      showAll: propTypes.bool.def(false),
      //省份是否有全部
      provinceHideAll: propTypes.bool.def(false),
      //匹配天地图
      tianDiMatch: propTypes.bool.def(false),
    },
    emits: ['options-change', 'change'],
    setup(props, { emit, refs }) {
      const emitData = ref<any[]>([]);
      const attrs = useAttrs();
      const [state] = useRuleFormItem(props, 'value', 'change', emitData);
      const getOptions = computed(() => {
        // console.log(props?.tianDiMatch,'tianDiMatch');
        // console.log(regionDataPlus,regionData,provinceAndCityData,provinceAndCityDataPlus);
        if (props.showArea && props.showAll) {
          let val = props?.tianDiMatch ? matchTiandi(regionDataPlus, props.showAll) : [...regionDataPlus];
          if (props.provinceHideAll) {
            val.shift();
          }
          return val;
        }
        if (props.showArea && !props.showAll) {
          return props?.tianDiMatch ? matchTiandi(regionData, props.showAll) : regionData;
        }
        if (!props.showArea && !props.showAll) {
          return props?.tianDiMatch ? matchTiandi(provinceAndCityData, props.showAll) : provinceAndCityData;
        }
        if (!props.showArea && props.showAll) {
          let val = props?.tianDiMatch ? matchTiandi(provinceAndCityDataPlus, props.showAll) : [...provinceAndCityDataPlus];
          if (props.provinceHideAll) {
            val.shift();
          }
          return val;
        }
      });
      /**
       * 监听value变化
       */
      watchEffect(() => {
        props.value && initValue();
      });

      /**
       * 将字符串值转化为数组
       */
      function initValue() {
        let value = props.value ? props.value : [];
        if (value && typeof value === 'string' && value != 'null' && value != 'undefined') {
          state.value = value.split(',');
        }
      }

      function handleChange(array, ...args) {
        // emitData.value = args;
        //update-begin-author:taoyan date:2022-6-27 for: VUEN-1424【vue3】树表、单表、jvxe、erp 、内嵌子表省市县 选择不上
        // 上面改的v-model:value导致选中数据没有显示
        state.value = array;
        //update-end-author:taoyan date:2022-6-27 for: VUEN-1424【vue3】树表、单表、jvxe、erp 、内嵌子表省市县 选择不上
      }
      return {
        state,
        attrs,
        regionData,
        getOptions,
        handleChange,
      };
    },
  });
</script>
