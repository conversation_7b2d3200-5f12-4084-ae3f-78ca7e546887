<template>
  <a-spin :spinning="loading" wrapperClassName="detail-spinning">
    <div class="common-detail-container">
      <div class="detail-box">
        <div class="box-title">基本信息</div>
        <BaseInfo :formData="formData" :myContractType="myContractType" />
      </div>
      <!-- 已作废、已退车后，提车任务单明细不显示任何内容 -->
      <div class="detail-box m-t-10px flex-1">
        <div class="box-title">提车任务单明细</div>
        <a-tabs v-model:activeKey="activeKey" tab-position="left" @change="handleTabsChange">
          <a-tab-pane key="info" tab="工单信息">
            <VehicleInspectionInformationEdit
              :id="formData.id"
              :formData="formData"
              :myContractType="myContractType"
              :detail="true"
            />
          </a-tab-pane>
          <a-tab-pane key="logs" tab="工单日志">
            <TicketLogs :id="formData.replaceOrderNo" :history="formData?.workOrderLogs || []" />
          </a-tab-pane>
          <a-tab-pane
            key="approval"
            tab="审核日志"
            v-if="
              formData.approvalStatus !== null && formData.approvalStatus !== void 0 && formData.approvalStatus !== -1
            "
          >
            <ApprovalInfo
              ref="approvalRef"
              :formData="formData"
              :apiUrl="approvalUrl"
              :apiMethod="'get'"
              :showTopInfo="false"
            />
          </a-tab-pane>
        </a-tabs>
      </div>
    </div>
  </a-spin>
</template>
<!-- 交车状态: 1、待交车 2、交车中 3、已交车  4、已作废 5、已退车 -->
<script lang="ts" name="businessOperation-leasingBusiness-replaceCarForm-detail" setup>
  import { onMounted, ref } from 'vue';
  import { useRoute } from 'vue-router';
  import { getDetailInfo, approvalUrl } from './index.api';
  import TicketLogs from '@/views/businessOperation/leasingBusiness/pickupTaskForm/workOrderComponents/TicketLogs.vue'; // 工单日志
  import VehicleInspectionInformationEdit from '@/views/businessOperation/leasingBusiness/pickupTaskForm/workOrderComponents/VehicleInspectionInformationEdit.vue'; // 验车信息
  import BaseInfo from './_components/BaseInfo.vue'; // 基本信息
  import ApprovalInfo from '../../component/ApprovalInfo.vue'; // 审核日志
  import { ITaskInfo } from './index.data';
  import { EContractType } from '/@/views/businessOperation/leasingBusiness/leaseContract/index/index.api';

  const route = useRoute();
  const loading = ref<boolean>(false); // 加载状态
  // 当前步骤对应的值
  const activeKey = ref<string>('info');
  // 是否是租赁合同/以租代售合同/展示车合同
  const myContractType = ref<EContractType>((route.query?.type as EContractType) || EContractType.LEASE_CONTRACT);
  const approvalRef = ref<InstanceType<typeof ApprovalInfo>>();
  //表单信息
  const formData = ref<ITaskInfo>({
    id: '',
    upstreamOrderNo: '',
    replaceOrderNo: '',
    deliveryStatus: 1,
    approvalStatus: -1,
    lesseeId: '',
    lesseeName: '',
    lessorName: '',
    customerName: '',
    applicationType: '',
    applicationCause: '',
    oldVin: '',
    oldVehiclePlate: '',
    vin: '',
    vehiclePlate: '',
    deliveryPerson: '',
    stationName: '',
    createTime: '',
    requestPickupDate: '',
    checkImgs: '',
    slaInfo: [],
    history: [],
    modelName: '',
    pickupTime: '',
    reason: '',
    orderAttributionSalesName: '',
  });

  // 初始化数据
  const initData = async () => {
    const id = route.query?.id as string;
    loading.value = true;
    await getDetailInfo({ id })
      .then(res => {
        formData.value = Object.assign({}, formData.value, res);
      })
      .finally(() => {
        loading.value = false;
      });
  };
  const handleTabsChange = async tab => {
    console.log('tab', tab);
    if (tab === 'approval') {
      approvalRef.value?.initData();
    }
  };

  onMounted(initData);
</script>
<style lang="less" scoped>
  @import url('../common/styles.less');
</style>
