<template>
  <a-spin :spinning="pageLoading">
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <template #productList="{ record }">
        <div
          v-for="item in record.boApplicationProductList"
          :key="item.id"
          :title="`${item.productName} ${item.productSpecifications}`"
          class="product-info-item"
        >
          <span>{{ item.productName || '' }}</span>
          <span>{{ item.productSpecifications }}</span>
        </div>
      </template>
      <template #approvalStatus="{ record }">
        <div v-if="record.approvalStatus !== null">
          <div style="display: flex;justify-content: flex-start;">
            <StateBox
              :text="approvalStatusObj[record.approvalStatus].text"
              :stateBC="approvalStatusObj[record.approvalStatus].stateBC"
            ></StateBox>
            <a-tooltip v-if="record.errorRemark" :title="record.errorRemark">
              <Icon icon="ant-design:info-circle-outlined" color="red" :size="20" />
            </a-tooltip>
          </div>
        </div>
      </template>

      <template #productNum="{ record }">
        <div v-for="item in record.boApplicationProductList" :key="item.id" class="product-info-item">
          <span>{{ item.productNum || item.productNum === 0 ? `${item.productNum}台` : '' }}</span>
        </div>
      </template>
      <template #tableTitle v-if="hasPermission(`bo:bo_vehicle_pickup_application${myContractType}:exportXls`)">
        <a-button type="primary" @click="handleExportPickup">导出提车申请单</a-button>
      </template>
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)"></TableAction>
      </template>
    </BasicTable>
    <!-- 发起钉钉审批 -->
    <DingTalkApprovalDrawer
      ref="dingTalkApprovalDrawerRef"
      :schemas="dingTalkSchemas"
      :submitApproval="pickupSubmitApproval"
      @handleValues="handleValues"
      @register="registerDingTalkDrawer"
      @success="reload"
    />
  </a-spin>
</template>
<script lang="ts">
  let name = '01';
</script>
<!-- 提车申请单(租赁合同，展示车合同,以租代售两种类型的合同公用)-->
<script lang="ts" setup>
  // #region
  import dayjs from 'dayjs';
  import { pickupSubmitApproval } from '@/api/common/api';
  import { useUserStore } from '/@/store/modules/user';
  import DingTalkApprovalDrawer from '@/views/businessOperation/leasingBusiness/component/DingTalkApprovalDrawer.vue';
  import type { DTApprovalDrawerRefType } from '@/views/businessOperation/leasingBusiness/component/DingTalkApprovalDrawer.vue';
  import { FormSchema, TableAction } from '/@/components/Table';
  import { useDrawer } from '/@/components/Drawer';
  import { getDingTalkApprovalFormSchema } from './index.data';
  import { genPickupTasks } from './index.api';
  import { initDictOptions } from '/@/utils/dict';
  import { approvalStatusObj, EContractType } from '@/enums/contract.enum';
  import StateBox from '@/components/common/StateBox.vue';
  import Icon from '/@/components/Icon';
  // #endregion

  import { onMounted, ref } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { exportXlsUrl, list } from './index.api';
  import { columns, searchFormSchema } from './index.data';
  import { BasicTable } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useMethods } from '/@/hooks/system/useMethods';
  import { usePermission } from '/@/hooks/web/usePermission';

  const { hasPermission } = usePermission();
  const router = useRouter();
  const route = useRoute();

  const myContractType = ref<EContractType>(EContractType.LEASE_CONTRACT); // 默认租赁合同
  // 设置合同类型
  // 修改查询条件,表格字段(配置在index.data.ts中),根据实际业务,修改对应的查询条件,字段展示
  if (route.fullPath.includes('leasingBusiness')) {
    // 租赁合同
    name = '01';
    myContractType.value = EContractType.LEASE_CONTRACT;
    searchFormSchema[0].defaultValue = '01';
    searchFormSchema[1].label = '租赁订单编号';
    searchFormSchema[1].field = 'leaseOrderNo';
    searchFormSchema[3].label = '承租方';
    searchFormSchema[3].field = 'lesseeName';
    searchFormSchema[4].label = '出租方';
    searchFormSchema[4].field = 'lessorName';
    // 表格字段修改
    columns[0].title = '租赁订单编号';
    columns[0].dataIndex = 'leaseOrderNo';
    columns[0].key = 'leaseOrderNo';
    columns[2].title = '出租方';
    columns[2].dataIndex = 'lessorName';
    columns[3].title = '承租方';
    columns[3].dataIndex = 'lesseeName';
  } else if (route.fullPath.includes('showCarBusiness')) {
    // 展示车合同
    name = '03';
    myContractType.value = EContractType.SHOW_CAR_CONTRACT;
    searchFormSchema[0].defaultValue = '03';
    searchFormSchema[1].label = '合同编号';
    searchFormSchema[1].field = 'contractNo';
    searchFormSchema[3].label = '甲方';
    searchFormSchema[3].field = 'lesseeName';
    searchFormSchema[4].label = '乙方';
    searchFormSchema[4].field = 'lessorName';
    // 表格字段修改
    columns[0].title = '合同编号';
    columns[0].dataIndex = 'contractNo';
    columns[0].key = 'contractNo';
    columns[2].title = '甲方';
    columns[2].dataIndex = 'lesseeName';
    columns[3].title = '乙方';
    columns[3].dataIndex = 'lessorName';
  } else if (route.fullPath.includes('leasePurchase')) {
    // 以租代售合同
    name = '02';
    myContractType.value = EContractType.LEASE_PURCHASE_CONTRACT;
    // 查询合同类型为以租代售的数据（查询条件修改）
    searchFormSchema[0].defaultValue = '02';
    searchFormSchema[1].label = '以租代售订单编号';
    searchFormSchema[1].field = 'leaseOrderNo';
    searchFormSchema[3].label = '承租方';
    searchFormSchema[3].field = 'lesseeName';
    searchFormSchema[4].label = '出租方';
    searchFormSchema[4].field = 'lessorName';
    // 表格字段修改
    columns[0].title = '以租代售订单编号';
    columns[0].dataIndex = 'leaseOrderNo';
    columns[0].key = 'leaseOrderNo';
    columns[2].title = '出租方';
    columns[2].dataIndex = 'lessorName';
    columns[3].title = '承租方';
    columns[3].dataIndex = 'lesseeName';
  }
  defineOptions({
    name: `businessOperation-leasingBusiness-pickupApplicationForm-${name}`,
  });
  const pageLoading = ref<boolean>(false);
  // 列表页面公共参数、方法
  const queryParams = ref({});
  //注册drawer
  // 列表页面公共参数、方法
  const { tableContext } = useListPage({
    tableProps: {
      api: list,
      rowKey: 'id',
      showIndexColumn: true,
      rowSelection: { type: 'checkbox' },
      columns: columns,
      formConfig: {
        schemas: searchFormSchema,
        labelWidth: 120,
      },
      actionColumn: {
        width: 230,
        fixed: 'right',
      },
      beforeFetch: params => {
        // 处理提车时间
        if (params.pickupTime) {
          const dates: string[] = params.pickupTime.split(',');
          params.requestPickupTimeStart = dates[0];
          params.requestPickupTimeEnd = dates[1];
        }
        queryParams.value = params;
        return Object.assign({ column: 'createTime', order: 'desc' }, params);
      },
    },
  });

  //注册table数据
  const [registerTable, { reload, getForm }, { rowSelection }] = tableContext;

  //导出方法
  const { handleExportXls } = useMethods();
  // 导出账单
  const handleExportPickup = async () => {
    pageLoading.value = true;
    await handleExportXls(`提车申请单`, exportXlsUrl, queryParams.value);
    pageLoading.value = false;
  };
  // 去往详情-需要带id
  const handleGoDetail = (record: Recordable) => {
    router.push({
      path: `/businessOperation/leasingBusiness/pickupApplicationFormDetail/${myContractType.value}/${record.id}`,
      query: {
        t: new Date().getTime(),
      },
    });
  };
  // #region 钉钉审批
  // 发起钉钉审批-注册弹框
  const [registerDingTalkDrawer, { openDrawer: openDingTalkDrawer }] = useDrawer();
  /**
   * 发起钉钉审批
   */
  function handleDingTalkApproval(record: any) {
    openDingTalkDrawer(true, {
      record,
      contractType: myContractType.value,
      tenantSaas: false,
      openCallBack: initDingTalkParams,
    });
  }
  const dingTalkSchemas = ref<Array<FormSchema>>(getDingTalkApprovalFormSchema());
  const dingTalkApprovalDrawerRef = ref<DTApprovalDrawerRefType>(null);

  function initDingTalkParams(record) {
    console.log('🚀 ~ initDingTalkParams ~ record:', record);
    let startMode = startList.value.filter(item => item.value == record.startMode);
    let productNeed = record.boApplicationProductList.map(item=>item.productSpecifications+' '+item.productNum+'台').join('\n');
    dingTalkApprovalDrawerRef.value?.setFieldsValue({
      id: record.id,
      contractNo: record.orderNo,
      cityName: record.cityName,
      stationName: record.stationName,
      stationAfterName: record.stationAfterName,
      requestPickupTime: record.requestPickupTime,
      startMode: startMode.length == 1 ? startMode[0].label : '',
      lesseeName: record.lesseeName,
      customerName: record.customerName,
      applicant: useUserStore().getUserInfo.realname + '/' + useUserStore().getUserInfo.workNo,
      applicationDate: dayjs().format('YYYY/MM/DD'),
      orderProduct: productNeed,
      operationOwnership: record.lessorName
    });
  }
  function handleValues(params) {}
  const startList = ref<any>([]);
  initDictOptions('bo_start_mode').then(res => {
    startList.value = res;
  });
  // #endregion 钉钉审批

  /**
   * 操作栏
   */
  function getTableAction(record) {
    const arr:any = [];
    if(record.errorRemark && record.approvalStatus == 1){
      arr.push({
        label: '生成提车任务单',
        popConfirm: {
          title: '是否确认生成提车任务单',
          confirm: handleGeneratePickupTask.bind(null, record),
        },
      })
    }
    if([-1,2,3].includes(record.approvalStatus as number)){
      arr.push({
        label: '提交审核',
        onClick: handleDingTalkApproval.bind(null, record),
        type: "primary"
      })
    }
    if(hasPermission(`bo:bo_vehicle_pickup_application${myContractType.value}:detail`)){
      arr.push({
        label: '详情',
        onClick: handleGoDetail.bind(null, record),
      })
    }
    return arr;
  }
  /**
   * 生成提车任务单
   */
  async function handleGeneratePickupTask(record) {
    await genPickupTasks({ id: record.id });
    await reload();
  }
  onMounted(() => {
    const { setFieldsValue } = getForm();
    setFieldsValue({
      pickupTime: [dayjs().subtract(89, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
    });
  });
</script>

<style scoped lang="less">
  .state-box {
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: left;

    &__wrap {
      display: flex;
      align-items: center;
      width: 30%;
      min-width: 59.5px;
    }

    .state-dot {
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 15%;

      &.red {
        background-color: rgb(227 96 80 / 100%);
      }

      &.green {
        background-color: rgb(107 217 169 / 100%);
      }
    }
  }

  .product-info-item {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;

    span:first-child {
      margin-right: 25px;
    }
  }
</style>
