import { defHttp } from '/@/utils/http/axios';
import { downloadXlsTools } from '/@/utils/file/download';
enum Api {
  list = '/veh/vehViolateSync/page',
  retry = '/veh/vehViolateSync/retry', // get
  exportExcel = '/veh/vehViolateSync/exportExcel', // post
}

/**
 * 列表接口
 * @param params
 */
export const list = params => defHttp.post({ url: Api.list, params });

/**
 * 重试接口
 * @param params
 */
export const vehViolateSyncRetry = params => defHttp.get({ url: Api.retry, params });

/**
 * 导出接口
 * @param params
 */
export const downloadXls = params => {
  downloadXlsTools(`${Api.exportExcel}`, params, '违章同步数据');
};
