import { BasicColumn, FormSchema } from '/@/components/Table';
import { list } from './index.api';

export const columns: BasicColumn[] = [
  {
    title: '一级类目',
    dataIndex: 'categoryName',
    key: 'categoryName',
    fixed: 'left',
  },
  {
    title: '二级类目',
    dataIndex: 'categoryTwoName',
    key: 'categoryTwoName',
  },
  {
    title: '类目SPU',
    dataIndex: 'spuName',
    key: 'spuName',
  },
  {
    title: '创建人',
    dataIndex: 'createBy',
    key: 'createBy',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '类目名称',
    field: 'categoryName',
    component: 'Input',
    colProps: { span: 8 },
    componentProps: {
      autocomplete: 'off',
    },
    rules: [
      {
        min: 2,
        message: '类目名称模糊搜索至少输入两位',
      },
    ],
  },
  {
    label: '类目SPU',
    field: 'spuName',
    component: 'Input',
    colProps: { span: 8 },
  },
];
export enum CATEGORY_TYPE {
  LEVEL_1 = '1',
  LEVEL_2 = '2',
}
export const formSchema: FormSchema[] = [
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    field: 'menuType',
    label: '类型',
    component: 'RadioButtonGroup',
    defaultValue: '1',
    labelWidth: 80,
    componentProps: {
      options: [
        { label: '一级类目', value: CATEGORY_TYPE.LEVEL_1 },
        { label: '二级类目', value: CATEGORY_TYPE.LEVEL_2 },
      ],
    },
    dynamicDisabled: ({ values }) => {
      return values.id ? true : false;
    },
  },
  {
    label: '类目名称',
    field: 'categoryName',
    component: 'Input',
    labelWidth: 80,
    colProps: { span: 18 },
    componentProps: {
      maxlength: 30,
    },
    rules: [{ required: true, message: '请输入类目名称' }],
    ifShow: ({ values }) => {
      return values.menuType === CATEGORY_TYPE.LEVEL_1;
    },
  },
  {
    label: '一级类目',
    field: 'categoryId',
    component: 'ApiSelect',
    labelWidth: 80,
    colProps: { span: 18 },
    componentProps: {
      api: list,
      labelField: 'categoryName',
      valueField: 'categoryId',
      resultField: 'categoryId',
      placeholder: '请选择一级类目',
      showSearch: true,
      immediate: true,
    },
    rules: [{ required: true, message: '请选择一级类目' }],
    ifShow: ({ values }) => {
      return values.menuType === CATEGORY_TYPE.LEVEL_2;
    },
  },
  {
    label: '二级类目',
    field: 'categoryTwoName',
    component: 'Input',
    labelWidth: 80,
    colProps: { span: 18 },
    componentProps: {
      maxlength: 30,
      position: 'left',
    },
    rules: [{ required: true, message: '请输入二级类目' }],
    ifShow: ({ values }) => {
      return values.menuType === CATEGORY_TYPE.LEVEL_2;
    },
  },

  {
    label: '类目SPU',
    field: 'spuName',
    component: 'Input',
    labelWidth: 80,
    colProps: { span: 18 },
    componentProps: {
      maxlength: 30,
    },
    rules: [
      { required: true, message: '请输入类目SPU' },
      {
        max: 30,
        message: '类目SPU不能超过30个字',
      },
      {
        pattern: /^[a-zA-Z0-9]+$/,
        message: '类目SPU仅支持数字和字母',
      },
    ],
    ifShow: ({ values }) => {
      return values.menuType === CATEGORY_TYPE.LEVEL_2;
    },
    dynamicDisabled: ({ values }) => {
      return values.id;
    },
  },
];
