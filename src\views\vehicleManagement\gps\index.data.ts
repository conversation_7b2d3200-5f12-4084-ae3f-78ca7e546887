import { BasicColumn, FormSchema } from '@/components/Table';
import { h } from 'vue';
import { Badge, Tag } from 'ant-design-vue';
import dayjs from 'dayjs';
import { DescItem } from '@/components/Description';

function customValidator(value, maxlength) {
  if (value?.trim()?.length > 0 && value?.trim()?.length < maxlength) {
    return Promise.reject();
  }
  return Promise.resolve();
}
const withEmptyCellHandler = columns => {
  return columns.map(col => {
    if (!col.customRender) {
      col.customRender = ({ text }: any) => {
        return text !== null ? text : '-';
      };
    }
    return col;
  });
};

const indexColumns: BasicColumn[] = [
  {
    title: '车牌号',
    dataIndex: 'carPlate',
    fixed: 'left',
  },
  {
    title: '车架号',
    dataIndex: 'vin',
    fixed: 'left',
    width: 210,
  },
  {
    title: '车辆品牌',
    dataIndex: 'brand',
  },
  {
    title: '车辆型号',
    dataIndex: 'model',
  },
  // {
  //   title: '终端编码',
  //   dataIndex: 'code',
  // },
  {
    title: '车辆启动状态',
    dataIndex: 'vehStatus',
    customRender({ text, record }) {
      // 1-启动, 2-熄火, 3-其他, 0xfe-异常, 0xff无效
      const styles = [
        {
          backgroundColor: '#f4f4f5',
          borderColor: '#cccccc',
          color: '#909090',
        },
        {
          backgroundColor: '#e8faf0',
          borderColor: '#d0f5e1',
          color: '#10D753',
        },
      ];
      let style = styles[0];
      if (text == 1) {
        style = styles[1];
      }
      // 离线、正常
      return h(Tag, { style: style }, record?.vehStatus_dictText);
    },
  },
  {
    title: '充电状态',
    dataIndex: 'chargeStatus',
    customRender({ text, record }) {
      // 充电状态 1-停车充电, 2-行驶充电, 3-未充电, 4-充电完成, 0xfe-异常, 0xff无效
      let color = '#f55448';
      if (text == 1 || text == 2) {
        color = '#2cdc9b';
      } else if (text == 3) {
        color = '#909090';
      } else if (text == 4) {
        color = '#2e75ff';
      }
      return h(Badge, { color, text: record?.chargeStatus_dictText });
    },
  },
  {
    title: '资产状态',
    dataIndex: 'assetsStatus',
    customRender({ text, record }) {
      const styles = [
        {
          backgroundColor: '#ffedee',
          borderColor: '#f49da2',
          color: '#EC7290',
        },
        {
          backgroundColor: '#e8faf0',
          borderColor: '#d0f5e1',
          color: '#10D753',
        },
      ];
      // 离线、正常
      return h(Tag, { style: styles[text] }, record?.assetsStatus_dictText);
    },
  },
  {
    title: '指令下发渠道',
    dataIndex: 'issueChannel',
  },
  {
    title: '锁车状态',
    dataIndex: 'lockStatus',
    customRender({ text, record }) {
      const styles = [
        {
          backgroundColor: '#f4f4f5',
          borderColor: '#cccccc',
          color: '#909090',
        },
        {
          backgroundColor: '#ffedee',
          borderColor: '#f49da2',
          color: '#EC7290',
        },
        {
          backgroundColor: '#D6E8FD',
          borderColor: '#7bacff',
          color: '#2e75ff',
        },
      ];
      let style = styles[2];
      if (text == 0) {
        style = styles[0];
      } else if (text == 3 || text == 5) {
        style = styles[1];
      }
      // 锁车状态, 0-未锁车, 1-下发锁车中, 2-等待锁车生效, 3-限速10Km/hm, 5-限速0Km/h, 6-下发解锁中, 7-等待解锁生效
      return h(Tag, { style: style }, record?.lockStatus_dictText);
    },
  },
  {
    title: 'T-box实时状态',
    dataIndex: 'tboxStatus',
    customRender({ text, record }) {
      const styles = [
        {
          backgroundColor: '#e8faf0',
          borderColor: '#d0f5e1',
          color: '#10D753',
        },
        {
          backgroundColor: '#ffedee',
          borderColor: '#f49da2',
          color: '#EC7290',
        },
      ];
      //tbox实时状态(0=在线，1=离线)
      return h(Tag, { style: styles[text] }, record?.tboxStatus_dictText);
    },
  },
  {
    title: '是否写入IP',
    dataIndex: 'writeIp',
    customRender({ text, record }) {
      const styles = [
        {
          backgroundColor: '#f4f4f5',
          borderColor: '#cccccc',
          color: '#909090',
        },
        {
          backgroundColor: '#D6E8FD',
          borderColor: '#7bacff',
          color: '#2e75ff',
        },
      ];
      //是否写入IP(0=否，1=是)
      return h(Tag, { style: styles[text] }, record?.writeIp_dictText);
    },
  },
  {
    title: '累计里程(km)',
    dataIndex: 'accumMileage',
  },
  {
    title: 'GPS数据源',
    dataIndex: 'gpsName',
    width: 150,
  },
  {
    title: 'GPS定位时间',
    dataIndex: 'positioningTime',
    width: 180,
  },
  {
    title: 'GPS最后位置',
    dataIndex: 'gpsAddress',
    width: 280,
  },
  {
    title: 'TBOX定位时间',
    dataIndex: 'locateTime',
    width: 180,
  },
  {
    title: 'TBOX最后位置',
    dataIndex: 'latestAddress',
    width: 280,
  },
  {
    title: '最近登录时间',
    dataIndex: 'onlineTime',
    helpMessage: ['车辆最后一次直接登入系统时间, 仅指第二链路, 不包括第一链路转发'],
    width: 180,
  },
  {
    title: '运营城市',
    dataIndex: 'operationCity',
  },
  {
    title: '运营归属',
    dataIndex: 'operationOwnership',
  },
  {
    title: '终端入网时间',
    dataIndex: 'terminalAccessTime',
    width: 180,
  },
  {
    title: '数据上报时间',
    dataIndex: 'reportTime',
    width: 180,
  },
];
export const columns = withEmptyCellHandler(indexColumns);
export const searchFormSchema: FormSchema[] = [
  {
    label: '车牌号',
    labelWidth: 90,
    field: 'carPlateList1',
    component: 'JIsolateInputModal',
    colProps: { span: 8 },
    componentProps: ({ formActionType }) => {
      return {
        textareaLabel: '车牌号', // 弹出框标题
        placeholder: '点击加号，批量搜索',
        textareaPlaceholder: '批量查询，用换行分隔',
        textareaBindField: 'carPlateList', //  绑定的查询字段
        formActionType, // 获取form的action
      };
    },
    rules: [
      {
        message: '车牌号模糊搜索至少输入3个字符',
        trigger: 'blur',
        validator: (_rule, value) => customValidator(value, 3),
      },
    ],
  },
  {
    label: '车架号',
    labelWidth: 90,
    field: 'vinList1',
    component: 'JIsolateInputModal',
    colProps: { span: 8 },
    componentProps: ({ formActionType }) => {
      return {
        textareaLabel: '车架号', // 弹出框标题
        placeholder: '点击加号，批量搜索',
        textareaPlaceholder: '批量查询，用换行分割',
        textareaBindField: 'vinList', //  绑定的查询字段
        formActionType, // 获取form的action
      };
    },
    rules: [
      {
        message: '车架号模糊搜索至少输入6个字符',
        trigger: 'blur',
        validator: (_rule, value) => customValidator(value, 6),
      },
    ],
  },
  {
    label: '指令下发渠道',
    labelWidth: 120,
    field: 'issueChannel',
    component: 'Input',
    colProps: { span: 8 },
    componentProps: {
      placehodler: '请输入渠道下发指令',
    },
  },
  {
    label: '充电状态',
    field: 'chargeStatusList',
    hideByQuickSearchHide: true,
    component: 'JMultipleTextSelector',
    colProps: {
      span: 22,
    },
  },
  {
    label: '资产状态',
    field: 'assetsStatusList',
    hideByQuickSearchHide: true,
    component: 'JMultipleTextSelector',
    colProps: {
      span: 22,
    },
  },
  {
    label: '锁车状态',
    field: 'lockStatusList',
    hideByQuickSearchHide: true,
    component: 'JMultipleTextSelector',
    colProps: {
      span: 22,
    },
  },
  {
    label: 'Tbox状态',
    field: 'tboxStatusList',
    hideByQuickSearchHide: true,
    component: 'JMultipleTextSelector',
    colProps: {
      span: 14,
    },
  },
  {
    label: '启动状态',
    field: 'vehStatusList',
    hideByQuickSearchHide: true,
    component: 'JMultipleTextSelector',
    colProps: {
      span: 22,
    },
  },
  {
    label: '是否写入IP',
    field: 'writeIpList',
    hideByQuickSearchHide: true,
    component: 'JMultipleTextSelector',
    colProps: {
      span: 14,
    },
  },
  {
    field: 'quickSearchFirst',
    isQuickSearch: true,
    componentList: [
      {
        label: '',
        field: 'brandId',
        hideLabel: true,
        component: 'JSelectInputText',
        componentProps: {
          options: [],
          placeholder: '车辆品牌',
          showSearch: false,
        },
        colProps: {
          style: { marginLeft: '-11px' },
          span: 2,
        },
      },
      {
        label: '',
        hideLabel: true,
        field: 'modelId',
        component: 'JSelectInputText',
        componentProps: {
          options: [],
          placeholder: '车辆型号',
          showSearch: false,
        },
        colProps: {
          span: 2,
        },
      },
      {
        label: '',
        hideLabel: true,
        field: 'operationCityCode',
        component: 'CityDropdownSelectText',
        componentProps: {
          isAreaSelect: true,
          showArea: false,
          placeholder: '运营城市',
        },
        colProps: { span: 2 },
      },
      {
        label: '',
        hideLabel: true,
        field: 'operationOwnershipId',
        component: 'JSelectInputText',
        componentProps: {
          options: [],
          placeholder: '运营归属',
          showSearch: false,
        },
        colProps: {
          span: 2,
        },
      },
    ],
  },
  {
    field: 'quickSearchSecond',
    isQuickSearch: true,
    componentList: [
      {
        label: '最近登录时间',
        labelWidth: 100,
        field: 'onlineTime',
        component: 'RangePicker',
        componentProps: {
          //是否显示时间
          // showTime: true,
          //日期格式化
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          //范围文本描述用集合
          // placeholder: ['请选择日期范围', ''],
          disabledDate: currentDate => {
            return currentDate && currentDate.isAfter(dayjs().endOf('day'));
          },
          style: { width: '100%' },
        },
        colProps: { span: 8 },
      },
      {
        label: '终端入网时间',
        labelWidth: 100,
        field: 'terminalAccessTime',
        component: 'RangePicker',
        componentProps: {
          //是否显示时间
          // showTime: true,
          //日期格式化
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          //范围文本描述用集合
          // placeholder: ['请选择日期范围', ''],
          disabledDate: currentDate => {
            return currentDate && currentDate.isAfter(dayjs().endOf('day'));
          },
          style: { width: '100%' },
        },
        colProps: { span: 8 },
      },
      {
        label: '最新定位时间',
        labelWidth: 100,
        field: 'locateTime',
        component: 'RangePicker',
        componentProps: {
          //是否显示时间
          // showTime: true,
          //日期格式化
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          //范围文本描述用集合
          // placeholder: ['请选择日期范围', ''],
          disabledDate: currentDate => {
            return currentDate && currentDate.isAfter(dayjs().endOf('day'));
          },
          style: { width: '100%' },
        },
        colProps: { span: 8 },
      },
    ],
  },
];
export const detailFormSchema: FormSchema[] = [
  {
    label: '车牌号',
    field: 'carPlate',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '车架号',
    field: 'vin',
    component: 'Input',
    colProps: { span: 6 },
  },
  {
    label: '定位数据源',
    field: 'supplierId',
    labelWidth: 100,
    component: 'Select',
    colProps: { span: 6 },
  },
];
export const detailDescirptSchema: DescItem[] = [
  {
    field: 'vin',
    label: '车架号',
    render: val => val ?? '-',
  },
  {
    field: 'brand',
    label: '车辆品牌',
    render: val => val ?? '-',
  },
  {
    field: 'model',
    label: '车辆型号',
    render: val => val ?? '-',
  },
  {
    field: 'latitude',
    label: '经度',
    render: val => val ?? '-',
  },
  {
    field: 'longitude',
    label: '纬度',
    render: val => val ?? '-',
  },
  {
    field: 'latestAddress',
    label: '地址',
    render: val => val ?? '-',
  },
  {
    field: 'positioningTime',
    label: '最新定位时间',
    render: val => val ?? '-',
  },
];
export const tboxDetailDescirptSchema: DescItem[] = [
  {
    field: 'vin',
    label: '车架号',
  },
  {
    field: 'brand',
    label: '车辆品牌',
  },
  {
    field: 'model',
    label: '车辆型号',
  },
  {
    field: 'accumMileage',
    label: '累计里程(km)',
  },
  {
    field: 'vehStatus_dictText',
    label: '车辆启动状态',
  },
  {
    field: 'chargeStatus_dictText',
    label: '充电状态',
  },
  {
    field: 'assetsStatus_dictText',
    label: '资产状态',
  },
  {
    field: 'issueChannel',
    label: '指令下发渠道',
  },
  {
    field: 'lockStatus',
    label: '锁车状态',
    render: val => {
      return val == 0 ? '未锁车' : val == 1 ? '已锁车' : '';
    },
  },
  {
    field: 'tboxStatus_dictText',
    label: 'T-box实时状态',
  },
  {
    field: 'writeIp',
    label: '是否写入IP',
    render: val => {
      return val == 0 ? '否' : val == 1 ? '是' : '';
    },
  },
  {
    field: 'power',
    label: '电池电量',
    render: val => {
      return val == undefined ? '' : `${val}%`;
    },
  },
  {
    field: 'speed',
    label: '车速',
    render: val => {
      return val == undefined ? '' : `${val}km/h`;
    },
  },
  {
    field: 'latitude',
    label: '经度',
  },
  {
    field: 'longitude',
    label: '纬度',
  },
  {
    field: 'address',
    label: '地址',
  },
  {
    field: 'reportTime',
    label: '数据上报时间',
  },
  {
    field: 'locateTime',
    label: '最新定位时间',
  },
  {
    field: 'onlineTime',
    label: '最近登录时间',
  },
];
