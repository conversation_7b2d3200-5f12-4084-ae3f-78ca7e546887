<!-- 自定义选择列，表头实现部分 -->
<template>
  <template v-if="isRadio">
    <!-- radio不存在全选，所以放个空标签 -->
    <span></span>
  </template>
  <a-checkbox v-else :checked="checked" :indeterminate="isHalf" @update:checked="onChange" />
  <!--  <div style="background: #fff; position: fixed; bottom: 0; left: 0; z-index: 99999; display: none" id="cqf-demo">-->
  <!--    <div>{{ props.flattedData.map(i => i.id) }}</div>-->
  <!--    <div>{{ props.selectedKeys }}</div>-->
  <!--    <div>{{ props.selectedLength }}</div>-->
  <!--    <div>{{ props.pageSize }}</div>-->
  <!--    <div>{{ props.rowKey }}</div>-->
  <!--    <div>{{ _selectedLength }}</div>-->
  <!--  </div>-->
</template>
<script setup lang="ts">
  import { computed } from 'vue';
  const props = defineProps({
    isRadio: {
      type: Boolean,
      required: true,
    },
    selectedLength: {
      type: Number,
      required: true,
    },
    // 当前页条目数
    pageSize: {
      type: Number,
      required: true,
    },
    rowKey: {
      type: String,
    },
    selectedKeys: {
      type: Array,
    },
    flattedData: {
      type: Array,
    },
  });
  const emit = defineEmits(['select-all']);
  const _selectedLength = computed(() => {

    return props.flattedData.filter(data => props.selectedKeys.includes(data[props.rowKey || 'id'])).length;
  });

  // 是否全选
  const checked = computed(() => {
    if (props.isRadio) {
      return false;
    }
    return _selectedLength.value > 0 && _selectedLength.value >= props.pageSize;
  });

  // 是否半选
  const isHalf = computed(() => {
    if (props.isRadio) {
      return false;
    }
    return _selectedLength.value > 0 && _selectedLength.value < props.pageSize;
  });

  function onChange(checked: boolean) {
    emit('select-all', checked);
  }
</script>

<style scoped lang="scss"></style>
