<template>
  <Menu
    :selectedKeys="selectedKeys"
    :defaultSelectedKeys="defaultSelectedKeys"
    :mode="mode"
    :openKeys="getOpenKeys"
    :inlineIndent="inlineIndent"
    :theme="theme"
    @open-change="handleOpenChange"
    :class="getMenuClass"
    @click="handleMenuClick"
    :subMenuOpenDelay="0.2"
    v-bind="getInlineCollapseOptions"
    class="custom-top-menu"
  >
    <template v-for="item in items" :key="item.path">
      <BasicSubMenuItem :item="item" :theme="theme" :isHorizontal="isHorizontal" :isFirst="true" />
    </template>
  </Menu>
</template>
<script lang="ts">
  import { Menu } from 'ant-design-vue';
  import { computed, defineComponent, reactive, ref, toRefs, unref, watch } from 'vue';
  import { RouteLocationNormalizedLoaded, useRouter } from 'vue-router';
  import BasicSubMenuItem from './components/BasicSubMenuItem.vue';
  import { basicProps } from './props';
  import type { MenuState } from './types';
  import { useOpenKeys } from './useOpenKeys';
  import { MenuModeEnum, MenuTypeEnum } from '/@/enums/menuEnum';
  import { useMenuSetting } from '/@/hooks/setting/useMenuSetting';
  import { useDesign } from '/@/hooks/web/useDesign';
  import { listenerRouteChange } from '/@/logics/mitt/routeChange';
  import { REDIRECT_NAME } from '/@/router/constant';
  import { getAllParentPath } from '/@/router/helper/menuHelper';
  import { getCurrentParentPath } from '/@/router/menus';
  import { isFunction } from '/@/utils/is';

  export default defineComponent({
    name: 'BasicMenu',
    components: {
      Menu,
      BasicSubMenuItem,
    },
    props: basicProps,
    emits: ['menuClick'],
    setup(props, { emit }) {
      const isClickGo = ref(false);
      const currentActiveMenu = ref('');

      const menuState = reactive<MenuState>({
        defaultSelectedKeys: [],
        openKeys: [],
        selectedKeys: [],
        collapsedOpenKeys: [],
      });

      const { prefixCls } = useDesign('basic-menu');
      const { items, mode, accordion } = toRefs(props);

      const { getCollapsed, getTopMenuAlign, getSplit } = useMenuSetting();

      const { currentRoute } = useRouter();

      const { handleOpenChange, setOpenKeys, getOpenKeys } = useOpenKeys(menuState, items, mode as any, accordion);

      const getIsTopMenu = computed(() => {
        const { type, mode } = props;

        return (type === MenuTypeEnum.TOP_MENU && mode === MenuModeEnum.HORIZONTAL) || (props.isHorizontal && unref(getSplit));
      });

      const getMenuClass = computed(() => {
        const align = props.isHorizontal && unref(getSplit) ? 'start' : unref(getTopMenuAlign);
        return [
          prefixCls,
          `justify-${align}`,
          {
            [`${prefixCls}__second`]: !props.isHorizontal && unref(getSplit),
            [`${prefixCls}__sidebar-hor`]: unref(getIsTopMenu),
          },
        ];
      });

      const getInlineCollapseOptions = computed(() => {
        const isInline = props.mode === MenuModeEnum.INLINE;

        const inlineCollapseOptions: { inlineCollapsed?: boolean } = {};
        if (isInline) {
          inlineCollapseOptions.inlineCollapsed = props.mixSider ? false : unref(getCollapsed);
        }

        return inlineCollapseOptions;
      });

      listenerRouteChange(({route}) => {
        if (route.name === REDIRECT_NAME) return;
        handleMenuChange(route);
        currentActiveMenu.value = route.meta?.currentActiveMenu as string;

        if (unref(currentActiveMenu)) {
          menuState.selectedKeys = [unref(currentActiveMenu)];
          setOpenKeys(unref(currentActiveMenu));
        }
      });

      !props.mixSider &&
        watch(
          () => props.items,
          () => {
            handleMenuChange();
          }
        );

      //update-begin-author:taoyan date:2022-6-1 for: VUEN-1144 online 配置成菜单后，打开菜单，显示名称未展示为菜单名称
      async function handleMenuClick({ item, key }: { item: any; key: string; keyPath: string[] }) {
        const { beforeClickFn } = props;
        if (beforeClickFn && isFunction(beforeClickFn)) {
          const flag = await beforeClickFn(key);
          if (!flag) return;
        }
        emit('menuClick', key, item);
        //update-end-author:taoyan date:2022-6-1 for: VUEN-1144 online 配置成菜单后，打开菜单，显示名称未展示为菜单名称

        isClickGo.value = true;
        // const parentPath = await getCurrentParentPath(key);

        // menuState.openKeys = [parentPath];
        menuState.selectedKeys = [key];
      }

      async function handleMenuChange(route?: RouteLocationNormalizedLoaded) {
        if (unref(isClickGo)) {
          isClickGo.value = false;
          return;
        }
        const path = (route || unref(currentRoute)).meta?.currentActiveMenu || (route || unref(currentRoute)).path;
        setOpenKeys(path);
        if (unref(currentActiveMenu)) return;
        if (props.isHorizontal && unref(getSplit)) {
          const parentPath = await getCurrentParentPath(path);
          menuState.selectedKeys = [parentPath];
        } else {
          const parentPaths = await getAllParentPath(props.items, path);
          menuState.selectedKeys = parentPaths;
        }
      }

      return {
        handleMenuClick,
        getInlineCollapseOptions,
        getMenuClass,
        handleOpenChange,
        getOpenKeys,
        ...toRefs(menuState),
      };
    },
  });
</script>
<style lang="less">
  @import url('./index.less');

  .ant-menu-sub {
    // background-color: #30374d !important;
    background-color: #fff !important;

    .ant-menu-submenu-selected {
      color: #3a78f9;

      &:hover {
        & > .ant-menu-submenu-title {
          color: #3a78f9 !important; /* 鼠标移入时的文本颜色 */
        }
      }
    }
  }

  .custom-top-menu {
    // background-color: #30374d !important;
    // background-color: #fff !important;
    background-color: var(--header-bg-color) !important;
    justify-content: flex-start !important;
    & > .ant-menu-submenu-selected {
      color: #3a78f9;

      &:hover {
        & > .ant-menu-submenu-title {
          color: #3a78f9 !important; /* 鼠标移入时的文本颜色 */
        }
      }
    }
  }

  .custom-popup-menu {
    border-radius: 8px;
    overflow: auto;
    box-shadow: 2px 2px 4px rgb(0 0 0 / 20%) !important;

    & > .ant-menu {
      // background-color: rgb(228, 228, 228) !important;
      // background-color: #30374d !important;
      background-color: #fff !important;
      display: flex;
      justify-content: flex-start;
      align-items: flex-start;
      flex-wrap: wrap;
      max-width: 635px;
      min-width: 200px;
      min-height: 100px;
      padding: 5px 10px;
      box-sizing: border-box;

      & > .ant-menu-submenu {
        // background-color: rgb(228, 228, 228) !important;
        // background-color: #30374d !important;
        background-color: #fff !important;
        height: 40px;
        // color: black;
        color: #ffffffa6;

        .ant-menu-submenu-title {
          &:hover {
            // color: #3a78f9 !important; /* 鼠标移入时的文本颜色 */
            color: #fff !important; /* 鼠标移入时的文本颜色 */
          }

          & > .ant-menu-submenu-arrow {
            display: none;
          }
        }

        &.ant-menu-submenu-selected {
          color: #3a78f9 !important; /* 鼠标移入时的文本颜色 */

          &:hover {
            & > .ant-menu-submenu-title {
              color: #3a78f9 !important; /* 鼠标移入时的文本颜色 */
            }
          }
        }
      }
    }
  }
</style>
