import { defHttp } from '/@/utils/http/axios';
import { useMessage } from '@/hooks/web/useMessage';

const { createConfirm } = useMessage();

enum Api {
  list = '/mdmKeyCustomers/account',
  edit = '/mdmKeyCustomers/accountEdit',
  enable = '/mdmKeyCustomers/accountEnable',
  updatePhone = '/dis/account/updatePhone',
  restoreDis = '/dis/account/restoreDis',
  disableDis = '/dis/account/disableDis',
}
/**
 * 列表
 * @param params
 */
export const list = params => defHttp.get({ url: Api.list, params });

export const restoreDis = params => defHttp.get({ url: Api.restoreDis, params });

export const disableDis = params => defHttp.get({ url: Api.disableDis, params });

export const updatePhone = params => defHttp.post({ url: Api.updatePhone, params });

export const editAccount = params => defHttp.post({ url: Api.edit, params });

export const changeStatus = params => defHttp.get({ url: Api.enable, params });
