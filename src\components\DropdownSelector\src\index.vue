<template>
  <a-dropdown v-model:open="visible" trigger="click" @visibleChange="handleVisibleChange" v-if="!isAreaSelect">
    <a class="ant-dropdown-link" @click.prevent> {{ title }} {{ selectedLabel }} <DownOutlined /> </a>
    <template #overlay>
      <a-menu @click="handleSelect">
        <a-menu-item v-for="item in menuItems" :key="item.value">
          {{ item.label }}
        </a-menu-item>
      </a-menu>
    </template>
  </a-dropdown>
  <Cascader
    v-bind="attrs"
    :value="state"
    :options="getOptions"
    @change="handleChange"
    placeholder="运营城市"
    class="custom-select"
    style="width: 95px"
    :bordered="false"
    v-else
  />
  <!-- <a class="ant-dropdown-link" @click.prevent> 省市选择<DownOutlined /> </a> -->
  <!-- </Cascader> -->
</template>

<script lang="ts">
  import { defineComponent, ref, watch, computed, PropType, reactive, watchEffect, unref, onMounted } from 'vue';
  import { DownOutlined } from '@ant-design/icons-vue';
  import { Cascader } from 'ant-design-vue';
  import { provinceAndCityData, regionData, provinceAndCityDataPlus, regionDataPlus } from '../js/util.js';
  import { useRuleFormItem } from '/@/hooks/component/useFormItem';
  import { propTypes } from '/@/utils/propTypes';
  import { useAttrs } from '/@/hooks/core/useAttrs';

  export default defineComponent({
    name: 'DropdownSelector',
    components: { DownOutlined, Cascader },
    props: {
      modelValue: {
        type: String,
        default: '', // 当前选中值
      },
      options: {
        type: Array as () => { label: string; value: string }[], // 下拉选项
      },
      title: {
        type: String,
        default: '',
      },
      isAreaSelect: {
        type: Boolean,
        default: false,
      },
      value: propTypes.oneOfType([propTypes.object, propTypes.array]),
      //是否显示区县
      showArea: propTypes.bool.def(true),
      //是否是全部
      showAll: propTypes.bool.def(false),
    },
    emits: ['update:modelValue', 'options-change', 'change'],
    setup(props, { emit }) {
      const visible = ref(true); // 控制下拉菜单的显示状态
      const selectedValue = ref(props.modelValue); // 当前选中的值
      const selectedLabel = ref(''); // 当前选中的文本

      // 计算菜单项
      const menuItems = computed(() => {
        let handle = props.options.map((item) => ({
          label: item.label,
          value: item.value,
        }));
        const empty = {
          label: '请选择',
          value: '',
        };
        handle.unshift(empty);
        return handle;
      });

      // 更新选中状态
      const handleSelect = ({ key }: { key: string }) => {
        const selectedOption = props.options.find((option) => option.value === key);
        if (selectedOption) {
          selectedValue.value = selectedOption.value;
          selectedLabel.value = selectedOption.label;
          emit('update:modelValue', selectedOption.value);
        } else {
          selectedValue.value = '';
          selectedLabel.value = '';
          emit('update:modelValue', '');
        }
        visible.value = false; // 关闭菜单
      };

      // 处理下拉菜单显示变化
      const handleVisibleChange = (val: boolean) => {
        visible.value = val;
      };

      // 同步外部传入的 modelValue
      watch(
        () => props.modelValue,
        (newValue) => {
          let option;

          if (!props.isAreaSelect) {
            option = props.options.find((o) => o.value === newValue);
          }

          if (option) {
            selectedValue.value = option.value;
            selectedLabel.value = option.label;
          }
          if (!newValue) {
            selectedValue.value = '';
            selectedLabel.value = '';
            state.value = '';
          }
        }
      );

      const emitData = ref<any[]>([]);
      const attrs = useAttrs();
      const [state] = useRuleFormItem(props, 'value', 'change', emitData);
      const getOptions = computed(() => {
        if (props.showArea && props.showAll) {
          return regionDataPlus;
        }
        if (props.showArea && !props.showAll) {
          return regionData;
        }
        if (!props.showArea && !props.showAll) {
          return provinceAndCityData;
        }
        if (!props.showArea && props.showAll) {
          return provinceAndCityDataPlus;
        }
      });
      /**
       * 监听value变化
       */
      watchEffect(() => {
        props.value && initValue();
      });

      /**
       * 将字符串值转化为数组
       */
      function initValue() {
        let value = props.value ? props.value : [];
        if (value && typeof value === 'string' && value != 'null' && value != 'undefined') {
          state.value = value.split(',');
        }
      }

      function handleChange(array, ...args) {
        // emitData.value = args;
        //update-begin-author:taoyan date:2022-6-27 for: VUEN-1424【vue3】树表、单表、jvxe、erp 、内嵌子表省市县 选择不上
        // 上面改的v-model:value导致选中数据没有显示

        state.value = array;
        const res = array?.join('');
        emit('update:modelValue', res);
        //update-end-author:taoyan date:2022-6-27 for: VUEN-1424【vue3】树表、单表、jvxe、erp 、内嵌子表省市县 选择不上
      }

      return {
        visible,
        selectedValue,
        selectedLabel,
        menuItems,
        handleSelect,
        handleVisibleChange,
        title: props.title,
        state,
        attrs,
        regionData,
        getOptions,
        handleChange,
        isAreaSelect: props.isAreaSelect,
      };
    },
  });
</script>

<style scoped>
  .ant-dropdown-link {
    display: inline-block;
    padding: 4px 8px;
    cursor: pointer;
    user-select: none;
  }
</style>
