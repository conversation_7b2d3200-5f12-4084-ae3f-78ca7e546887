<template>
  <div class="word-multiple-box">
    <span v-if="(isSelectAll && isMultiple) || (notMultipleShowSelectAll && !isMultiple)" class="word-multiple-item" :class="{ selected: isSelectedAll }" @click="toggleSelect(selectAllOptions, true)">
      {{ selectAllOptions.label }}
    </span>
    <span class="word-multiple-item" :class="{ selected: isSelected(item) }" v-for="item in options" :key="item[valueKey]" @click="toggleSelect(item)">
      {{ item[labelKey] }}
    </span>
  </div>
</template>

<script lang="ts">
  import { defineComponent, ref, unref, onMounted, computed, watch } from 'vue';
  import { useAttrs } from '/@/hooks/core/useAttrs';
  import { propTypes } from '/@/utils/propTypes';

  // item对象的属性可以是title，key，也可以是label，也可以是value
  // 此处做个扩展，将item对象转换为{key:string,title:string}
  interface IObj {
    [attr: string]: string;
  }

  export default defineComponent({
    name: 'MultipleTextSelector',
    props: {
      value: {
        type: [String, Array],
        default: '',
      },
      options: {
        type: Array as () => { key: string; title: string; dataIndex: string }[],
        required: true,
      },
      // 新增
      labelKey: propTypes.string.def('title'), // 显示文字的key值，默认为title
      valueKey: propTypes.string.def('key'), // 值key，默认为key
      isMultiple: propTypes.bool.def(true), // 是否多选，默认为多选
      defaultValue: propTypes.string.def(''), // 重置时的默认值
      notMultipleShowSelectAll: propTypes.bool.def(false),
      // 全选功能
      isSelectAll: propTypes.bool.def(false),
      selectAllOptions: {
        type: Object as () => { label: string; key: string; value: '' },
        default: { label: '全部', key: 'all', value: '' },
      },
    },
    emits: ['update:value', 'change'],
    setup(props, { emit }) {
      const attrs = useAttrs();
      const selectedKeys = ref<string[]>([]);
      watch(
        () => props.value,
        (newValue: any) => {
          console.log('🚀 ~ setup ~ newValue:', newValue);
          if (props.isMultiple) {
            // 多选模式下强制转换为数组
            selectedKeys.value = Array.isArray(newValue) ? newValue : [];
          } else {
            // 单选模式下处理默认值
            if (newValue || newValue === 0) {
              selectedKeys.value = [newValue];
            } else {
              if (props.defaultValue) {
                selectedKeys.value = [props.defaultValue];
              } else {
                selectedKeys.value = [''];
              }
            }
          }
        },
        { immediate: true }
      );
      // 初始化时，若 defaultValue 有值（且是单选模式），默认选中它
      onMounted(() => {
        // 单选有默认值，给其赋值
        if (!props.isMultiple && props.defaultValue) {
          selectedKeys.value = [props.defaultValue];
          emit('update:value', props.defaultValue);
          emit('change', props.defaultValue);
        }
      });
      const isSelectedAll = computed(() => {
        return selectedKeys.value.length === 0 || !selectedKeys.value[0];
      });
      const getBindValue = Object.assign({}, unref(props), unref(attrs));
      const isSelected = (item: IObj) => {
        return selectedKeys.value.includes(item[props.valueKey]);
      };
      /**
       * 选择逻辑
       * @param item
       * @param selectAllFlag
       */
      const toggleSelect = (item: IObj, selectAllFlag: Boolean = false) => {
        const itemKey = item[props.valueKey];
        if (props.isMultiple) {
          // 多选逻辑
          if (selectAllFlag) {
            // 全选就清空
            selectedKeys.value = [];
          } else if (isSelected(item)) {
            selectedKeys.value = selectedKeys.value.filter(key => key !== itemKey);
          } else {
            selectedKeys.value.push(itemKey);
          }
          console.log('🚀 ~ toggleSelect ~ selectedKeys.value:', selectedKeys.value);
          // 账单明细，处理全选问题
          emit('update:value', selectedKeys.value);
          emit('change', selectedKeys);
        } else {
          if (selectAllFlag) {
            selectedKeys.value = [];
            emit('update:value', '');
            emit('change', '');
            return;
          }
          // **单选逻辑**
          if (isSelected(item)) {
            return;
          }
          // **正常切换单选状态**
          selectedKeys.value = [itemKey];
          emit('update:value', itemKey);
          emit('change', itemKey);
        }
      };

      return {
        selectedKeys,
        isSelected,
        getBindValue,
        toggleSelect,
        isSelectedAll,
      };
    },
  });
</script>

<style scoped>
  .word-multiple-box {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
  }

  .word-multiple-item {
    display: inline-block;
    /* min-width: 76px; */
    padding: 6px 0px 6px 0;
    cursor: pointer;
    user-select: none;
    margin-right: 30px;
  }

  .word-multiple-item.selected {
    /* background-color: #409eff; */

    /* color: #fff; */
    color: #409eff;

    /* border-color: #409eff; */
  }
</style>
