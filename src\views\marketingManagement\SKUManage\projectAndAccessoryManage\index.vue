<template>
  <div>
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <template #form-treeSelect="{ model, field }">
        <a-tree-select
          v-model:value="model[field]"
          show-search
          style="width: 100%"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          placeholder="请选择类目"
          allow-clear
          :tree-data="treeData"
          tree-node-filter-prop="value"
        >
        </a-tree-select>
      </template>
      <!-- 配件属性 -->
      <template #attribute="{ record }">
        {{ ConsumablesObj[record.attribute] }}
      </template>
      <template #recordStatus="{ record }">
        <template v-if="[0, 1].includes(record.status)">
          <StateBox :text="statusObj[record.status].text" :stateBC="statusObj[record.status].stateBC"></StateBox>
        </template>
        <template v-else>
          {{ record.status }}
        </template>
      </template>
      <template #skuCode="{ record }">
        <a-button type="link" @click="handleEdit(null, OpenDrawerType.detail)">{{ record.skuCode }}</a-button>
      </template>
      <template #tableTitle>
        <a-button
          v-if="hasPermission(`market:sku${props.skuType}:add`)"
          type="primary"
          @click="handleEdit(null, OpenDrawerType.add)"
          >新增SKU</a-button
        >
        <!-- 导入 -->
        <a-button v-if="hasPermission(`market:sku${props.skuType}:import`)" type="primary" @click="handleImport"
          >批量新增</a-button
        >
        <a-button v-if="hasPermission(`market:sku${props.skuType}:disable`)" @click="disabledOrEnable('disable')"
          >禁用</a-button
        >
        <a-button v-if="hasPermission(`market:sku${props.skuType}:enable`)" @click="disabledOrEnable('enable')"
          >启用</a-button
        >
        <a-button v-if="hasPermission(`market:sku${props.skuType}:exportExcel`)" type="primary" @click="handleExportXLS"
          >导出</a-button
        >
      </template>
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
    </BasicTable>
    <ProjectDrawer :skuType="skuType" @register="registerDrawer" @success="handleSuccess" />
    <ImportDrawer
      :skuType="skuType"
      :handleImport="onImportXls"
      @register="registerImportDrawer"
      @success="handleSuccess"
    ></ImportDrawer>
  </div>
</template>
<script lang="ts" setup name="marketingManagement-attributeManagement-pool">
  import { skuPageList, list, getImportUrl, downloadXls, disabledDate, enableDate, deleteItemById } from './index.api';
  import ProjectDrawer from './projectDrawer.vue';
  import ImportDrawer from './ImportDrawer.vue';
  import { getColumns, getSearchFormSchema, ConsumablesObj, OpenDrawerType } from './index.data';
  import { ActionItem, BasicTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useDrawer } from '/@/components/Drawer';
  import { Modal } from 'ant-design-vue';
  import StateBox from '@/components/common/StateBox.vue';
  import { message } from 'ant-design-vue';
  import { usePermission } from '/@/hooks/web/usePermission';
  const { hasPermission } = usePermission();
  import { initDictOptions } from '/@/utils/dict';

  initDictOptions('accessory_sku_unit').then((res: any) => {
    unitList.value = res;
  });

  const props = defineProps({
    skuType: Number, // 	类型（1：项目；2：配件）
  });
  // #region 初始化差异参数
  const columns = getColumns(props.skuType == 1);
  // #endregion

  const unitList = ref<any>([]);
  const statusObj = {
    '0': {
      text: '禁用',
      stateBC: '#f59b25',
    },
    '1': {
      text: '正常',
      stateBC: '#2cdc9c',
    },
  };

  // #region 搜索表单
  import { ref, onMounted } from 'vue';

  const treeData = ref([]);

  onMounted(() => {
    getTreeData();
  });

  async function getTreeData() {
    let result = await list({});
    console.log('🚀 ~ result:', result);
    result = result.map(item => {
      if (item.spuList && item.spuList.length > 0) {
        item.spuList.forEach(item => {
          item.label = item.categoryTwoName || '';
          item.value = item.categoryTwoName || '';
        });
      }
      return {
        label: item.categoryName,
        value: item.categoryName,
        id: `category${item.categoryId}`,
        children: item.spuList,
      };
    });
    treeData.value = result;
    console.log('🚀 ~ result:', result);
  }
  // #endregion
  // #region 表格
  const { tableContext, onImportXls } = useListPage({
    tableProps: {
      title: '',
      showIndexColumn: true,
      rowKey: 'id',
      rowSelection: { type: 'checkbox' },
      api: skuPageList,
      columns,
      size: 'small',
      formConfig: {
        schemas: getSearchFormSchema(props.skuType == 1),
      },
      actionColumn: {
        width: 200,
        fixed: 'right',
      },
      beforeFetch: params => {
        params.skuType = props.skuType;
      },
    },
    importConfig: {
      url: `${getImportUrl}?skuType=${props.skuType}`,
    },
  });
  //注册table数据
  const [registerTable, { reload, getForm }, { rowSelection, selectedRowKeys }] = tableContext;

  /**
   *@description 删除事件
   */
  async function handleDelete(record) {
    await deleteItemById({ id: record.id }, reload);
  }
  function handleDeleteCheck(record) {
    Modal.confirm({
      title: '温馨提示',
      content: '删除后无法恢复，请确认是否继续删除操作？',
      onOk() {
        handleDelete(record);
      },
    });
  }
  /**
   *@description 表格操作
   */
  function getTableAction(record): ActionItem[] {
    return [
      {
        auth: 'market:pool:edit',
        label: '编辑',
        onClick: handleEdit.bind(null, record, OpenDrawerType.edit),
        ifShow: hasPermission(`market:sku${props.skuType}:update`),
      },
      {
        auth: 'market:pool:delete',
        label: '删除',
        onClick: handleDeleteCheck.bind(null, record),
        ifShow: hasPermission(`market:sku${props.skuType}:delete`),
      },
    ];
  }
  // 导出
  async function handleExportXLS() {
    const { validate } = getForm();
    const params = await validate();
    downloadXls({ ...params, skuType: props.skuType });
  }
  // 启用、禁用
  async function disabledOrEnable(flag) {
    const handleIds = selectedRowKeys.value.join(',');
    if (!handleIds && handleIds != '0') {
      message.warning('请先勾选列表SKU数据');
      return;
    }
    if (flag === 'disable') {
      await disabledDate({ idList: handleIds });
    } else {
      await enableDate({ idList: handleIds });
    }
    // 取消选项状态
    selectedRowKeys.value = [];
    reload();
  }

  /**
   * 导入
   */
  function handleImport() {
    openImportDrawer(true, {
      isUpdate: false,
      showFooter: true,
      tenantSaas: false,
    });
  }
  // #endregion 表格

  // #region 弹框 -- 编辑、新增
  const [registerDrawer, { openDrawer }] = useDrawer();
  /**
   *@description 编辑事件
   */
  function handleEdit(record, openType: OpenDrawerType) {
    openDrawer(true, {
      record,
      isUpdate: record ? true : false,
      showFooter: true,
      openType,
    });
  }

  /**
   *@description 成功回调
   */
  function handleSuccess() {
    reload();
  }
  // #endregion
  // #region 弹框 -- 批量新增
  const [registerImportDrawer, { openDrawer: openImportDrawer }] = useDrawer();
  // #endregion
</script>
<style lang="less"></style>
