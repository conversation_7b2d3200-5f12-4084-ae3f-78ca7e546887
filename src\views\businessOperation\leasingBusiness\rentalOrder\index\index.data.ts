import { getEnterList, getPerson } from '@/api/common/api';
import { BasicColumn, FormSchema } from '/@/components/Table';
import { commonColPropsThree } from '/@/views/businessOperation/leasingBusiness/leaseContract/index/index.data';

export const columns: BasicColumn[] = [
  {
    title: '合同编号',
    dataIndex: 'contractNo',
    key: 'contractNo',
    fixed: 'left',
    width: 200,
  },
  {
    title: '租赁订单编号',
    dataIndex: 'orderNo',
    key: 'orderNo',
    fixed: 'left',
    width: 230,
  },
  {
    title: '出租方',
    dataIndex: 'lessorName',
    key: 'lessorName',
  },
  {
    title: '承租方',
    dataIndex: 'lesseeName',
    key: 'lesseeName',
  },
  {
    title: '提车需求',
    dataIndex: 'boOrderProductVOList',
    key: 'boOrderProductVOList',
    width: 280,
    slots: { customRender: 'productList' },
  },
  {
    title: '数量',
    dataIndex: 'productNum',
    key: 'productNum',
    width: '100px',
    slots: { customRender: 'productNum' },
  },
  {
    title: '租期',
    dataIndex: 'leaseTermStr',
    key: 'leaseTermStr',
  },
  {
    title: '订单状态',
    dataIndex: 'orderStatus',
    key: 'orderStatus',
    slots: { customRender: 'status' },
  },
  {
    title: '意见',
    dataIndex: 'opinion',
    key: 'opinion',
    width: 180,
    slots: { customRender: 'opinion' },
  },
  {
    title: '业务员',
    dataIndex: 'salesmanName',
    customRender: ({ text }) => text || '-',
  },
  {
    title: '创建时间',
    dataIndex: 'createTimeStr',
    width: 180,
    key: 'createTimeStr',
  },
];

export const searchFormSchema: FormSchema[] = [
  {
    label: '合同类型',
    field: 'contractType',
    component: 'Input',
    defaultValue: '01',
    show: false,
  },
  {
    label: '',
    field: 'orderNo',
    component: 'Input',
    colProps: commonColPropsThree,
  },
  {
    label: '是否为标准合同',
    field: 'standardContract',
    component: 'Input',
    defaultValue: 1,
    show: false,
  },
  {
    label: '承租方',
    field: 'lesseeName',
    component: 'ApiSelect',
    labelWidth: 80,
    componentProps: {
      api: getEnterList,
      params: { tags: '1,2,7,8' },
      labelField: 'entName',
      valueField: 'entName',
      resultField: 'records',
      placeholder: '请选择承租方',
      showSearch: true,
      immediate: false,
    },
    colProps: commonColPropsThree,
  },
  {
    label: '出租方',
    field: 'lessorName',
    component: 'ApiSelect',
    labelWidth: 80,
    componentProps: {
      api: getEnterList,
      params: { tags: '4' },
      labelField: 'entName',
      valueField: 'entName',
      resultField: 'records',
      placeholder: '请选择出租方',
      showSearch: true,
      immediate: false,
    },
    colProps: commonColPropsThree,
  },
  {
    label: '订单状态',
    field: 'orderStatus',
    component: 'JMultipleTextSelector',
    componentProps: {
      options: [
        { key: '2', title: '待确认' },
        { key: '1', title: '已确认' },
        { key: '5', title: '审核中' },
        { key: '6', title: '审核未通过' },
        { key: '4', title: '已归档' },
      ],
    },
    colProps: {
      span: 24,
    },
  },
];

export const formSchema: FormSchema[] = [
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
  {
    label: '合同id',
    field: 'contractId',
    component: 'Input',
    show: false,
  },
  {
    label: '租赁订单编号',
    field: 'orderNo',
    component: 'Input',
    componentProps: {
      disabled: true,
    },
    colProps: { span: 24 },
    itemProps: {
      wrapperCol: { sm: 24 },
    },
    rules: [{ required: true, message: '请输入租赁订单编号' }],
  },
  {
    label: '',
    field: 'boOrderProductList',
    component: 'Input',
    slot: 'product',
    labelWidth: 0,
    colProps: { span: 24 },
    itemProps: {
      wrapperCol: { sm: 24 },
    },
  },
  {
    label: '出租方',
    field: 'lessorId',
    component: 'Select',
    slot: 'lessorId',
    colProps: { span: 24 },
    itemProps: {
      wrapperCol: { sm: 24 },
    },
    rules: [{ required: true, message: '请选择出租方' }],
    ifShow: false,
  },
  {
    label: '',
    field: 'lessorName',
    component: 'Input',
    ifShow: false,
  },
  {
    label: '承租方',
    field: 'lesseeId',
    component: 'Select',
    slot: 'lesseeId',
    colProps: { span: 24 },
    itemProps: {
      wrapperCol: { sm: 24 },
    },
    rules: [{ required: true, message: '请选择承租方' }],
    ifShow: false,
  },
  {
    label: '',
    field: 'lesseeName',
    component: 'Input',
    colProps: { span: 24 },
    ifShow: false,
  },
  {
    label: '城市',
    field: 'cityId',
    slot: 'city',
    component: 'Select',
    rules: [{ required: true, message: '请选择城市' }],
    colProps: { span: 24 },
    itemProps: {
      wrapperCol: { sm: 24 },
    },
  },
  {
    label: '场站',
    field: 'stationId',
    slot: 'station',
    component: 'Select',
    rules: [{ required: true, message: '请选择场站' }],
    colProps: { span: 24 },
    itemProps: {
      wrapperCol: { sm: 24 },
    },
  },
  {
    label: '订单归属销售',
    field: 'orderAttributionSalesId',
    component: 'ApiSelect',
    componentProps: ({ formModel }) => {
      return {
        api: getPerson,
        params: {
          pageSize: 30000,
        },
        labelField: 'realname',
        valueField: 'id',
        resultField: 'records',
        immediate: true,
        showSearch: true,
        onChange: (v, o) => {
          console.log(o);
          formModel.orderAttributionSalesName = o?.label || undefined;
        },
      };
    },
    colProps: {
      span: 24,
    },
    rules: [{ required: true, message: '请选择订单归属销售', trigger: 'change' }],
  },
  {
    label: '交车员工',
    field: 'stationAfterSales',
    slot: 'afterSales',
    component: 'Select',
    colProps: { span: 24 },
    itemProps: {
      wrapperCol: { sm: 24 },
    },
  },
  {
    label: '要求提车时间',
    field: 'requestPickupTime',
    component: 'DatePicker',
    rules: [{ required: true, message: '请选择要求提车时间' }],
    colProps: { span: 24 },
    itemProps: {
      wrapperCol: { sm: 24 },
    },
  },
  {
    label: '起租方式',
    field: 'startMode',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'bo_start_mode',
      type: 'select',
      showChooseOption: false,
      placeholder: '请选择起租方式',
    },
    rules: [{ required: true }],
    colProps: { span: 24 },
    itemProps: {
      wrapperCol: { sm: 24 },
    },
  },
  {
    label: '客户提车人',
    field: 'customerName',
    component: 'Input',
    componentProps: {
      maxlength: 10,
    },
    rules: [{ required: true, message: '请输入客户提车人' }],
    colProps: { span: 24 },
    itemProps: {
      wrapperCol: { sm: 24 },
    },
  },
  {
    label: '联系电话',
    field: 'phone',
    component: 'Input',
    componentProps: {
      maxlength: 11,
    },
    rules: [
      { required: true, message: '请输入联系电话' },
      { pattern: /^1[3456789]\d{9}$/, message: '手机号码格式有误' },
    ],
    colProps: { span: 24 },
    itemProps: {
      wrapperCol: { sm: 24 },
    },
  },
  {
    label: '备注',
    field: 'remark',
    component: 'InputTextArea',
    //属性
    componentProps: {
      //可以点击清除图标删除内容
      allowClear: true,
      //是否展示字数
      showCount: true,
      //自适应内容高度，可设置为 true | false 或对象：{ minRows: 2, maxRows: 6 }
      maxlength: 200,
      autoSize: {
        //最小显示行数
        minRows: 2,
        //最大显示行数
        maxRows: 3,
      },
    },
    colProps: { span: 24 },
    itemProps: {
      wrapperCol: { sm: 24 },
    },
  },
];
// 新增提车申请单-表头
export const pickUpCarColumns: BasicColumn[] = [
  {
    title: '产品名称',
    dataIndex: 'primaryKey',
    key: 'primaryKey',
    width: '150px',
  },
  {
    title: '产品规格',
    dataIndex: 'tempTpecificationId',
    key: 'tempTpecificationId',
    width: '150px',
  },
  {
    title: '数量',
    dataIndex: 'productNum',
    key: 'productNum',
    width: '150px',
  },
  {
    title: '操作',
    dataIndex: 'opt',
    width: '100px',
    fixed: 'right',
    key: 'opt',
  },
];