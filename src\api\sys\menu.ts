import { defHttp } from '/@/utils/http/axios';
import { useGlobSetting } from '/@/hooks/setting';

enum Api {
  GetMenuList = '/sys/permission/getUserPermissionByToken',
  SwitchVue3Menu = '/sys/switchVue3Menu',
  sysPermissionAccessLogAdd = '/sys/sysPermissionAccessLog/add',
}

/**
 * @description: Get user menu based on id
 */

export const getMenuList: any = (params) => {
  return new Promise((resolve) => {
    //为了兼容mock和接口数据
    params = {
      appCode: window?.name || useGlobSetting().code,
    };
    defHttp.get({ url: Api.GetMenuList, params }, { errorMessageMode: 'none', successMessageMode: 'none' }).then((res) => {
      if (Array.isArray(res)) {
        resolve(res);
      } else {
        resolve(res['menu']);
      }
    });
  });
  return;
};

/**
 * 切换成vue3菜单
 */
export const switchVue3Menu = () => {
  return new Promise((resolve) => {
    defHttp.get({ url: Api.SwitchVue3Menu });
  });
};

/**
 * 系统菜单访问日志-添加
 */
export const sysPermissionAccessLogAdd = (params) => {
  return defHttp.post({ url: Api.sysPermissionAccessLogAdd, params });
};
