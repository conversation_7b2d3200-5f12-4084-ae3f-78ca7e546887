<template>
  <div class="risk-control-table">
    <a-table
      :columns="columns"
      :data-source="expandedTableData"
      :loading="loading"
      :pagination="false"
      :scroll="{ y: screenHeight + 'px' }"
      bordered
      size="middle"
      row-key="id"
      :row-class-name="getRowClassName"
      :show-expand-column="false"
    >
      <!-- 业务类型列自定义渲染 -->
      <template #bodyCell="{ column, record, index }">
        <template v-if="column.key === 'businessStatus'">
          <div class="business-type-cell">
            <span v-if="!record.isChild">{{ record.model }}</span>
            <span v-else>{{ record.businessStatus }}</span>
            <a-button
              v-if="!record.isChild && record.children && record.children.length > 0"
              type="link"
              size="small"
              @click="toggleExpand(record, index)"
              class="expand-btn"
            >
              <down-outlined :style="{ color: '#333' }" v-if="expandedRows.has(record.id)" />
              <right-outlined :style="{ color: '#333' }" v-else />
            </a-button>
          </div>
        </template>
      </template>
    </a-table>
  </div>
</template>

<script lang="ts" setup>
  import { ref, computed, nextTick } from 'vue';
  import { DownOutlined, RightOutlined } from '@ant-design/icons-vue';
  import { RiskControlTableItem, getColumns } from './index.data';

  const props = withDefaults(
    defineProps<{
      data?: RiskControlTableItem[];
      loading?: boolean;
      zoomRate?: number;
      screenHeight?: number;
    }>(),
    {
      data: () => [],
      loading: false,
      zoomRate: 1,
      screenHeight: document.documentElement.clientHeight,
    }
  );
  // 响应式数据，用于控制展开状态
  const expandedRows = ref<Set<string>>(new Set());

  // 动态列配置
  const columns = computed(() => getColumns(expandedRows.value, expandedTableData.value));

  // 扁平化数据，包含主行和子行
  const flattenedData = computed(() => {
    if (!props.data || props.data.length === 0) {
      return [];
    }

    const result: any[] = [];

    // 按大区分组
    const regionGroups = new Map();

    props.data.forEach(item => {
      if (!regionGroups.has(item.cityName)) {
        regionGroups.set(item.cityName, []);
      }
      regionGroups.get(item.cityName).push(item);
    });
    // 处理每个大区
    regionGroups.forEach(cityItem => {
      const regionModel = new Set();
      const regionStartIndex = result.length;
      // 按渠道分组
      const channelGroups = new Map();
      cityItem.forEach(item => {
        if (!channelGroups.has(item.lesseeName)) {
          channelGroups.set(item.lesseeName, []);
        }
        channelGroups.get(item.lesseeName).push(item);
      });

      // 处理每个渠道
      channelGroups.forEach(channelItems => {
        // 获取每个渠道下的主行数据
        const channelStartIndex = result.length;
        // 处理渠道下的每个业务类型
        let channelIndex = 0;
        channelItems.forEach(item => {
          if (!item.businessStatus) {
            if (!regionModel.has(item.model)) {
              regionModel.add(item.model);
            }

            const mainRow = {
              ...item,
              isChild: false,
              children: channelItems.filter(ele => ele.model === item.model && ele.businessStatus), // 保存子数据
              channelIndex,
              sameRegionDiffChannelFirstRow:
                result.some(row => row.cityName === item.cityName && row.lesseeName !== item.lesseeName) &&
                channelIndex === 0,
            };
            result.push(mainRow);
            channelIndex++;
          }
        });

        // 设置渠道合并
        const channelEndIndex = result.length - 1;
        const channelRowCount = channelEndIndex - channelStartIndex + 1;

        // 设置第一行的合并信息
        if (result[channelStartIndex]) {
          result[channelStartIndex].channelRowSpan = channelRowCount;
        }

        // 其他行设置为0（子行不设置，保持正常显示）
        for (let i = channelStartIndex + 1; i <= channelEndIndex; i++) {
          if (result[i]) {
            result[i].channelRowSpan = 0;
          }
        }
      });
      const regionEndIndex = result.length - 1;
      const regionRowCount = regionEndIndex - regionStartIndex + 1;
      if (result[regionStartIndex]) {
        result[regionStartIndex].regionRowSpan = regionRowCount - regionModel.size;
      }
      for (let i = regionStartIndex + 1; i <= regionEndIndex; i++) {
        if (result[i]) {
          result[i].regionRowSpan = 0;
        }
      }
    });
    // 九方总体合并单元格逻辑
    return result;
  });

  // 动态展开的数据（包含展开的子行）
  const expandedTableData = computed(() => {
    const result: any[] = [];

    flattenedData.value.forEach((item: any) => {
      // 检查该行是否已展开
      const isExpanded = expandedRows.value.has(item.id);

      // 创建父行的副本
      const parentRow = {
        ...item,
        isExpanded: isExpanded, // 添加展开状态标记
      };

      result.push(parentRow);

      // 如果该行已展开且有子数据，添加子行
      if (expandedRows.value.has(item.id) && item.children && item.children.length > 0) {
        item.children.forEach((child: any) => {
          const childRow = {
            ...child,
            id: `${item.id}`, // 确保子行有唯一ID
            isChild: true,
            parentItem: item, // 保存父行信息，用于合并逻辑
            childrenCount: item.children.length, // 子行总数
            cityName: item.cityName, // 继承父行的大区信息
            lesseeName: item.lesseeName, // 继承父行的渠道信息
            regionRowSpan: 0, // 子行不参与大区合并
            channelRowSpan: 0, // 子行不参与渠道合并
          };
          result.push(childRow);
        });
      }
    });

    // 调整有 regionRowSpan > 0 的行，加上展开的子行数量
    result.forEach(item => {
      if (item.lesseeName.includes('总体')) {
        // 计算从这一行开始，同一大区内所有展开的子行数量
        let totalChildRows = 0;

        // 找到这个大区的所有行（从当前行开始，直到下一个有 regionRowSpan > 0 的行）
        const currentIndex = result.indexOf(item);

        for (let i = currentIndex; i < result.length; i++) {
          const currentItem = result[i];

          // 如果遇到下一个大区的开始，停止
          if (i > currentIndex && currentItem.regionRowSpan > 0) {
            break;
          }
          // 如果是同一大区的行且已展开，计算子行数量
          if (
            !currentItem.isChild &&
            currentItem.cityName === item.cityName &&
            expandedRows.value.has(currentItem.id) &&
            currentItem.children
          ) {
            totalChildRows += currentItem.children.length;
          }
        }
        // 调整这一行的 regionRowSpan
        item.channelRowSpan += item.channelIndex === 0 ? totalChildRows : 0;
      } else if (!item.isChild && item.regionRowSpan > 0) {
        // 计算从这一行开始，同一大区内所有展开的子行数量
        let totalChildRows = 0;

        // 找到这个大区的所有行（从当前行开始，直到下一个有 regionRowSpan > 0 的行）
        const currentIndex = result.indexOf(item);

        for (let i = currentIndex; i < result.length; i++) {
          const currentItem = result[i];

          // 如果遇到下一个大区的开始，停止
          if (i > currentIndex && currentItem.regionRowSpan > 0) {
            break;
          }
          // 如果是同一大区的行且已展开，计算子行数量
          if (
            !currentItem.isChild &&
            currentItem.cityName === item.cityName &&
            expandedRows.value.has(currentItem.id) &&
            currentItem.children &&
            !currentItem.lesseeName.includes('总体')
          ) {
            totalChildRows += currentItem.children.length;
          }
        }
        // 调整这一行的 regionRowSpan
        item.regionRowSpan += totalChildRows;
      }
    });

    return result;
  });

  // 切换展开状态
  const toggleExpand = (record: any, index: number) => {
    const flag = index >= expandedTableData.value.length - 4;
    if (expandedRows.value.has(record.id)) {
      expandedRows.value.delete(record.id);
    } else {
      expandedRows.value.add(record.id);
    }
    if (flag) {
      nextTick(() => {
        setTimeout(() => {
          scrollToBottom();
        }, 50); // 延迟更安全
      });
    }
  };
  const scrollToBottom = () => {
    const tableBody = document.querySelector('.ant-table-body');
    if (tableBody) {
      tableBody.scrollTop = tableBody.scrollHeight;
    }
  };
  // 获取行的CSS类名
  const getRowClassName = (record: any) => {
    let className = '';
    if (record.lesseeName.includes('总体')) {
      className += 'summary-row ';
    }

    if (record.operatingRate) {
      // 提取百分比数值
      const rate = record.operatingRate * 100;
      if (rate < 75) {
        // 若为渠道的第一行
        if (record.channelIndex === 0) {
          // 若为统一大区下不同渠道的第一行
          if (record.sameRegionDiffChannelFirstRow || record.lesseeName === '九方总体') {
            className += 'first-row-diff-channel-low';
          } else {
            className += 'low-operation-rate';
          }
        } else {
          // 若改行数据不是第一行
          if ((record.channelIndex && record.channelIndex !== 0) || record.isChild) {
            className += 'not-first-row-low';
          }
        }
      } else if (rate >= 75 && rate < 85) {
        if (record.channelIndex === 0) {
          if (record.sameRegionDiffChannelFirstRow || record.lesseeName === '九方总体') {
            className += 'first-row-diff-channel-medium';
          } else {
            className += 'medium-operation-rate';
          }
        } else {
          if ((record.channelIndex && record.channelIndex !== 0) || record.isChild) {
            className += 'not-first-row-medium';
          }
        }
      }
      // rate >= 85% 无特殊样式
    }
    return className.trim();
  };
  // 导出组件方法供外部使用
  defineExpose({
    // 可以在这里导出需要的方法
    expandedRows,
    expandedTableData,
  });
</script>

<style lang="less" scoped>
  .risk-control-table {
    .business-type-cell {
      display: flex;
      align-items: center;
      // justify-content: center;
      position: relative;

      &.child-business-type {
        justify-content: flex-start;
        padding-left: 20px; // 子行缩进
      }

      .expand-btn {
        position: absolute;
        right: 0;
        padding: 0;
        // min-width: 20px;

        :deep(.anticon) {
          font-size: 12px;
        }
      }
    }

    // 子行样式已移除，保持原有表格样式

    // 根据资产运营率设置行背景颜色
    :deep(.ant-table-tbody) {
      .summary-row {
        td {
          font-weight: 600;
        }
      }
      // 橙红色背景 - 运营率 < 75%

      .low-operation-rate {
        td:not(:first-child):not(:nth-child(2)) {
          background-color: #ff7043 !important; // 浅橙红色
        }
      }
      .first-row-diff-channel-low {
        td:not(:first-child) {
          background-color: #ff7043 !important; // 浅橙红色
        }
      }
      .not-first-row-low {
        td {
          background-color: #ff7043 !important; // 浅橙红色
        }
      }

      // 橙黄色背景 - 75% <= 运营率 < 85%
      .medium-operation-rate {
        td:not(:first-child):not(:nth-child(2)) {
          background-color: #ffb143 !important; // 浅橙黄色
        }
      }
      .first-row-diff-channel-medium {
        td:not(:first-child) {
          background-color: #ffb143 !important; // 浅橙黄色
        }
      }
      .not-first-row-medium {
        td {
          background-color: #ffb143 !important; // 浅橙黄色
        }
      }
    }
    :deep(.ant-table) {
      // 设置表格边框颜色为更深的灰色
      border-color: #c5c4c4 !important;

      .ant-table-thead > tr > th {
        background-color: #f5f5f5;
        font-weight: 600;
        text-align: center;
        padding: 8px 8px; // 增加表头高度
        border-color: #c5c4c4 !important; // 更深的边框颜色
        // 移除表头的额外边框设置，保持默认样式
        font-size: var(--fontSize);
      }

      .ant-table-tbody > tr > td {
        text-align: center;
        padding: 16x 8px; // 增加单元格高度，从8px改为16px
        border-color: #c5c4c4 !important; // 更深的边框颜色
        border-right: 1px solid #c5c4c4 !important;
        font-size: var(--fontSize);
      }

      // 修复最后一行的边框重叠问题
      .ant-table-tbody > tr:last-child > td {
        // border-bottom: none !important; // 移除最后一行的下边框，避免与表格外边框重叠
      }

      .ant-table-tbody > tr:hover > td {
        background-color: #e6f7ff;
      }

      // 设置表格容器的边框
      .ant-table-container {
        border-color: #c5c4c4 !important;
      }

      // 设置表格内部边框
      .ant-table-content {
        border-color: #c5c4c4 !important;
      }

      // 确保表格外边框也是正确的颜色，但避免重叠
      &.ant-table-bordered {
        border-top: 1px solid #c5c4c4 !important;
        border-bottom: 1px solid #c5c4c4 !important;

        .ant-table-container {
          border-color: #c5c4c4 !important;
          border-left: 1px solid #c5c4c4 !important;
        }
      }
    }
  }
</style>
