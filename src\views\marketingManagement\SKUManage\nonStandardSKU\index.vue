<template>
  <div>
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <template #recordStatus="{ record }">
        <template v-if="[0, 1].includes(record.status)">
          <StateBox :text="statusObj[record.status].text" :stateBC="statusObj[record.status].stateBC"></StateBox>
        </template>
        <template v-else>
          {{ record.status }}
        </template>
      </template>
      <template #tableTitle>
        <!-- 导入 -->
        <a-button type="primary" @click="disabledOrEnable(1)">批量下架</a-button>
        <a-button type="primary" @click="disabledOrEnable(2)">批量上架</a-button>
        <a-button @click="handleImport">批量更新</a-button>
        <a-button @click="handleExportXLS">导出</a-button>
      </template>
    </BasicTable>
    <ImportDrawer @register="registerImportDrawer" @success="handleSuccess"></ImportDrawer>
  </div>
</template>
<script lang="ts" setup name="marketingManagement-attributeManagement-pool">
  import { list, downloadXls, disabledOrEnableDate } from './index.api';
  import ImportDrawer from './ImportDrawer.vue';
  import { columns, OnShelves, searchFormSchema } from './index.data';
  import { BasicTable } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { useDrawer } from '/@/components/Drawer';
  import StateBox from '@/components/common/StateBox.vue';
  import { message } from 'ant-design-vue';

  const statusObj = {
    [OnShelves.no]: {
      text: '已下架',
      stateBC: '#f59b25',
    },
    [OnShelves.yes]: {
      text: '已上架',
      stateBC: '#2cdc9c',
    },
  };

  // #region 表格
  const { tableContext } = useListPage({
    tableProps: {
      title: '',
      showIndexColumn: true,
      rowKey: 'id',
      rowSelection: { type: 'checkbox' },
      api: list,
      columns: columns,
      size: 'small',
      formConfig: {
        schemas: searchFormSchema,
      },
      actionColumn: {
        width: 200,
        fixed: 'right',
      },
    },
  });
  //注册table数据
  const [registerTable, { reload, getForm }, { rowSelection, selectedRowKeys }] = tableContext;

  // 导出
  async function handleExportXLS() {
    const { validate } = getForm();
    const params = await validate();
    downloadXls(params);
  }
  // 启用、禁用
  async function disabledOrEnable(flag) {
    const handleIds = selectedRowKeys.value.join(',');
    if (!handleIds && handleIds != '0') {
      message.warning('请先勾选列表SKU数据');
      return;
    }
    await disabledOrEnableDate({ ids: handleIds, flag });
    // 取消选项状态
    selectedRowKeys.value = [];
    reload();
  }

  /**
   * 导入
   */
  function handleImport() {
    openImportDrawer(true, {
      isUpdate: false,
      showFooter: true,
      tenantSaas: false,
    });
  }
  // #endregion 表格

  /**
   *@description 成功回调
   */
  function handleSuccess() {
    reload();
  }
  // #endregion
  // #region 弹框 -- 批量新增
  const [registerImportDrawer, { openDrawer: openImportDrawer }] = useDrawer();
  // #endregion
</script>
<style lang="less"></style>
