export interface BasicPageParams {
  page: number;
  pageSize: number;
}

export interface BasicFetchResult<T extends any> {
  items: T[];
  total: number;
}

export interface BasicResult<T extends any> {
  records: T[];
  total: number;
}

//新增
export interface BasicRowsResult<T extends any> {
  rows: T[];
  total: number;
}


export interface BasicDataResult<T extends any> {
  data: T[];
  total: number;
}