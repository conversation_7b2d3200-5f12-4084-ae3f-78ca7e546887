<template>
  <div class="risk-control-report" :style="{ '--fontSize': tableFontSize }">
    <div class="top-wrap">
      <div class="search-wrap">
        <div class="label">日期：</div>
        <a-date-picker
          class="date-select"
          v-model:value="searchDate"
          format="YYYY/MM/DD"
          placeholder="请选择日期"
          :disabled-date="disabledDate"
          :default-value="dayjs().subtract(1, 'day')"
        />
      </div>
      <div class="btn-wrap">
        <a-button type="primary" size="large" @click="loadData">查询</a-button>
        <a-button type="default" size="large" plain @click="handleReset">重置</a-button>
      </div>
    </div>
    <a-card :bordered="false" class="main-card">
      <div class="title">渠道资产概况</div>
      <!-- 大区切换Tab -->
      <a-tabs v-model:activeKey="activeRegionId" type="card" class="region-tabs" @change="onRegionChange">
        <a-tab-pane v-for="region in regionList" :key="region.id" :tab="region.name">
          <div class="tab-content">
            <!-- 柱状图区域 -->
            <div class="chart-section" :loading="chartLoading">
              <a-card :bordered="false" class="chart-card">
                <div
                  :class="['bar-chart-container', chartData.length > 7 ? 'scroll' : '']"
                  :style="{ height: '400px' }"
                >
                  <RiskControlChart :data="chartData" :loading="chartLoading" height="400px" />
                </div>
              </a-card>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
      <a-spin :spinning="tableLoading">
        <div class="table-section" :loading="tableLoading">
          <a-card :bordered="false" class="table-card">
            <div ref="tableContainerRef">
              <RiskControlTable
                ref="tableRef"
                :data="tableData"
                :loading="tableLoading"
                :zoomRate="zoomRate"
                :screenHeight="screenHeight"
              />
            </div>
          </a-card>
        </div>
      </a-spin>
    </a-card>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted, onUnmounted } from 'vue';
  import { message } from 'ant-design-vue';
  import { RegionInfo, RiskControlTableItem, ChannelInfo, getColumns } from './index.data';
  import { getBarData, getTableData } from './index.api';
  import RiskControlChart from './RiskControlChart.vue';
  import RiskControlTable from './RiskControlTable.vue';
  import dayjs, { Dayjs } from 'dayjs';
  // 响应式数据
  const activeRegionId = ref<number>(1);
  const regionList = ref<RegionInfo[]>([]);
  const chartData = ref<ChannelInfo[]>([]);
  const tableData = ref<RiskControlTableItem[]>([]);
  const chartLoading = ref(false);
  const tableLoading = ref(false);
  const searchDate = ref<Dayjs | null>(dayjs().subtract(1, 'day'));
  const barData = ref<any[]>([]);
  const tableRef = ref<InstanceType<typeof RiskControlTable>>();
  const tableContainerRef = ref<HTMLElement>(); // 表格容器引用
  const zoomRate = ref<number>(1); // 缩放比例
  const tableFontSize = ref<string>('16px');
  const initScreenWidth = ref<number>(window.innerWidth);
  const screenHeight = ref<number>(document.documentElement.clientHeight - 230);

  // 禁用日期函数
  const disabledDate = (current: any) => {
    // 禁用今天及之后的日期
    const today = dayjs().startOf('day');
    // 禁用数据保存日期之前的日期
    const minDate = dayjs('2025-08-08').startOf('day');
    return current && (current >= today || current < minDate);
  };
  const handleReset = () => {
    searchDate.value = dayjs().subtract(1, 'day');
    loadData();
  };

  const initHandleData = () => {
    const zoomRate = document.documentElement.clientWidth / initScreenWidth.value;
    screenHeight.value = document.documentElement.clientHeight - 230 / zoomRate;
    if (initScreenWidth.value <= 1920) {
      // tableFontSize.value = '14px';
      tableFontSize.value = 14 * zoomRate + 'px';
    } else if (initScreenWidth.value > 1920) {
      tableFontSize.value = 16 * zoomRate + 'px';
      // tableFontSize.value = '16px';
    }
  };

  // 初始化页面
  onMounted(async () => {
    initHandleData();
    addEventListener('resize', () => initHandleData());
    await loadData();
    // 设置表头可见性监听
  });

  // 清理资源
  onUnmounted(() => {
    removeEventListener('resize', () => {
      console.log('remove resize');
    });
  });
  /**
   * 加载大区列表
   */
  const loadRegionData = async () => {
    try {
      const barChartData = await getBarData({
        date: searchDate.value ? dayjs(searchDate.value).format('YYYY-MM-DD') : void 0,
      });
      // 对大区数量进行排序，数量最多的大区最先显示
      barData.value = barChartData.sort((a: any, b: any) => {
        const totalA = a.data.reduce((regionSum, company) => {
          const companySum = company.modelList.reduce((companySum, model) => companySum + model.total, 0);
          return regionSum + companySum;
        }, 0);
        const totalB = b.data.reduce((regionSum, company) => {
          const companyTotal = company.modelList.reduce((companySum, model) => {
            return companySum + (model.total || 0);
          }, 0);
          return regionSum + companyTotal;
        }, 0);
        return totalB - totalA;
      });
      // 获取大区数据
      regionList.value = barChartData.length
        ? barChartData.map((item: any, index: number) => {
            return {
              id: index + 1,
              name: item.label,
            };
          })
        : [{ id: 1, name: '深圳' }];
      handleChartData(barChartData);
    } catch (error) {
      console.error('❌ 柱状图数据加载失败:', error);
    }
  };

  /**
   * 加载数据
   */
  const loadData = async () => {
    await Promise.all([loadRegionData(), loadTableData()]);
  };

  /**
   * 加载图表数据
   */
  const handleChartData = async (barData: any) => {
    chartLoading.value = true;
    try {
      const { data } = barData[activeRegionId.value - 1] || [];
      let result = data
        ? [...data]
            .sort((a: any, b: any) => {
              const totalA = a.modelList.reduce((sum: number, model: any) => sum + model.total, 0);
              const totalB = b.modelList.reduce((sum: number, model: any) => sum + model.total, 0);
              return totalB - totalA; // 降序排序
            })
            .reverse()
        : [];
      chartData.value = result;
    } catch (error) {
      console.error('加载图表数据失败:', error);
      message.error('加载图表数据失败');
    } finally {
      chartLoading.value = false;
    }
  };

  /**
   * 加载表格数据
   */
  const loadTableData = async () => {
    if (!activeRegionId.value) return;

    tableLoading.value = true;
    try {
      // 这里替换为您的API调用
      const result = await getTableData({
        date: searchDate.value ? dayjs(searchDate.value).format('YYYY-MM-DD') : void 0,
      });

      tableData.value = result;
    } catch (error) {
      console.error('加载表格数据失败:', error);
      message.error('加载表格数据失败');
    } finally {
      tableLoading.value = false;
    }
  };

  /**
   * 大区切换事件
   */
  const onRegionChange = () => {
    handleChartData(barData.value);
  };
</script>

<style lang="less" scoped>
  .risk-control-report {
    padding: 16px;
    background-color: #f0f2f5;
    min-height: calc(100vh - 64px);
    .top-wrap {
      padding: 24px;
      background-color: #fff;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      display: flex;
      align-items: center;
      justify-content: space-between;
      .search-wrap {
        display: flex;
        align-items: center;
        .label {
          font-size: 16px;
          margin-right: 12px;
        }
        .date-select {
          width: 300px;
        }
      }
    }
    .main-card {
      margin-top: 10px;
      border-radius: 8px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      :deep(.ant-card-body) {
        padding: 24px;
      }

      .title {
        position: relative;
        font-size: 18px;
        font-weight: 600;
        color: #262626;
        margin-bottom: 24px;
        &::before {
          content: '';
          position: absolute;
          top: 5px;
          left: -10px;
          width: 4px;
          height: 20px;
          background-color: #2e75ff;
        }
      }
      .region-tabs {
        :deep(.ant-tabs-nav) {
          margin-bottom: 6px;

          .ant-tabs-nav-wrap {
            .ant-tabs-nav-list {
              .ant-tabs-tab {
                padding: 12px 24px;
                font-size: 14px;
                font-weight: 600;
                border-radius: 0;
                border: none;
                margin-right: 8px;
                background-color: transparent;
                .ant-tabs-tab-btn {
                  color: #333;
                  font-size: 16px;
                }
                &.ant-tabs-tab-active {
                  background-color: transparent;
                  color: #1890ff;
                  position: relative;
                  .ant-tabs-tab-btn {
                    color: #1890ff;
                  }
                  &::after {
                    content: '';
                    position: absolute;
                    bottom: 0px;
                    left: 14%;
                    width: 70%;
                    height: 2px;
                    background-color: #1890ff;
                  }
                }

                &:hover:not(.ant-tabs-tab-active) {
                  color: #1890ff;
                }
              }
            }
          }
        }

        :deep(.ant-tabs-content-holder) {
          .ant-tabs-content {
            .ant-tabs-tabpane {
              padding: 0;
            }
          }
        }
      }

      .tab-content {
        .chart-section {
          margin-bottom: 24px;

          .chart-card {
            border-radius: 8px;
            .fixed-height {
              height: 600px;
            }
            .scroll {
              overflow-y: scroll;
            }
            :deep(.ant-card-head) {
              border-bottom: 1px solid #f0f0f0;

              .ant-card-head-title {
                font-size: 16px;
                font-weight: 600;
                color: #262626;
              }
            }

            :deep(.ant-card-body) {
              padding: 20px 0;
            }
          }
        }
      }
      .table-section {
        .table-card {
          border-radius: 8px;

          :deep(.ant-card-head) {
            border-bottom: 1px solid #f0f0f0;

            .ant-card-head-title {
              font-size: 16px;
              font-weight: 600;
              color: #262626;
            }
          }

          :deep(.ant-card-body) {
            padding: 20px 0;
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .risk-control-report {
      padding: 8px;

      .main-card {
        :deep(.ant-card-body) {
          padding: 16px;
        }

        .region-tabs {
          :deep(.ant-tabs-tab) {
            padding: 8px 16px;
            font-size: 12px;
          }
        }

        .tab-content {
          .chart-section,
          .table-section {
            .chart-card,
            .table-card {
              :deep(.ant-card-body) {
                padding: 12px;
              }
            }
          }
        }
      }
    }
  }
</style>
