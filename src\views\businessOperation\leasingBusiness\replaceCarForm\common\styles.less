.state-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 15px;

  &.status-1 {
    background-color: #3979f9;
  }

  &.status-2 {
    background-color: #e2cd11;
  }

  &.status-3 {
    background-color: #2cdc9b;
  }

  &.status-4 {
    background-color: #d5d5d5;
  }

  &.status-5 {
    background-color: #cf0c0c;
  }

  &.status-6 {
    background-color: #e2cd11;
  }

  &.status-yc-1 {
    background-color: #2cdc9b;
  }

  &.status-yc-4 {
    background-color: #cf0c0c;
  }
}

.detail-spinning {
  height: 100%;

  :deep(.ant-spin-container) {
    height: 100%;
  }
}

.common-detail-container {
  height: 100%;
  padding: 10px;
  display: flex;
  flex-direction: column;

  .common-state-box {
    margin-left: 16px;
  }

  .detail-box {
    background-color: #fff;
    border-radius: 4px;

    .box-title {
      line-height: 24px;
      font-weight: 700;
      font-size: 19px;
      color: #333;
      padding: 15px 20px;
      border-bottom: 1px solid #f1f1f1;
    }

    .p-15px {
      padding: 15px 20px;
    }
  }

  :deep(.ant-tabs) {
    min-height: 580px;

    .ant-tabs-tab-btn {
      font-weight: 400;
      font-size: 17px;
      color: #333;
    }

    .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
      color: #3979f9;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 50%;
        transform: translateX(-50%);
        width: 35px;
        height: 1px;
        background-color: #3979f9;
      }
    }

    .ant-tabs-tabpane {
      padding-left: 45px;
    }

    .ant-tabs-content {
      padding-top: 30px;
    }

    .ant-tabs-nav-list {
      padding-top: 22px;
    }
  }

  :deep(.ant-btn) {
    height: 34px;
    border-radius: 4px;
    font-size: 15px;
  }

  :deep(.common-info-btn) {
    height: 21px;

    span {
      border-bottom: 1px solid;
    }
  }
}

.info-row-vertical {
  .common-info-row+.common-info-row {
    margin-top: 16px;
  }

  .ant-btn {
    padding-left: 0;
  }
}

.common-info-label {
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 15px;
  color: #333;
  line-height: 21px;
  min-width: 120px;
}

.common-info-text {
  line-height: 21px;
  font-weight: 400;
  font-size: 15px;
  color: #999;
  flex: 1;
  max-width: 301px;
  margin-left: 16px;
}

.w-25 {
  width: 25%;
}

.w-33 {
  width: calc(100% / 3);
}

.w-50 {
  width: calc(100% / 2);
}

.info-row-horizontal-four {
  margin-top: 16px;

  .common-info-row:nth-child(n + 5) {
    margin-top: 16px;
  }
}

.info-row-horizontal-three {
  margin-top: 16px;

  .common-info-row:nth-child(n + 4) {
    margin-top: 16px;
  }
}

.info-row-horizontal-two {
  margin-top: 16px;

  .common-info-row:nth-child(n + 3) {
    margin-top: 16px;
  }
}
