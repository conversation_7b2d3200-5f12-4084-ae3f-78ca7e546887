<template>
  <BasicDrawer
    class="manage-list__edit-drawer"
    v-bind="$attrs"
    @register="registerDrawer"
    width="91%"
    destroyOnClose
    @close="handleCancel"
  >
    <a-row :gutter="10">
      <a-col :span="2">
        <a-tabs class="edit-drawer__tabs" v-model:activeKey="state.activeKey" @change="handleChange" tabPosition="left">
          <a-tab-pane
            v-for="item in state.tabs"
            :key="item.key"
            :tab="item.title"
            :disabled="item.disabled"
            v-bind="item"
          />
        </a-tabs>
      </a-col>
      <a-col :span="22">
        <div v-show="state.activeKey == 'basic'">
          <BasicForm name="basic" @register="basicRegisterForm" :autoComplete="'off'">
            <template #license="{ model, field }">
              <JImageUpload
                v-model:value="model[field]"
                :fileMax="1"
                :fileSize="30"
                bucketName="ov-portal-pub"
                :onChange="handleLicenseChange"
                :beforeUpload="handleBeforeUpload"
              />
            </template>
          </BasicForm>
          <div class="sub-enterprise">
            <a-button
              class="sub-enterprise__btn"
              type="primary"
              @click="importSubEnter"
              v-if="!['audit', 'detail'].includes(state.labelKey)"
            >
              <DownloadOutlined />
              导入子企业
            </a-button>
            <a-table :data-source="subEnterList" :columns="subEnterColumnsComputed" :pagination="false">
              <template #bodyCell="{ column, record, index }">
                <template v-if="column.key === 'opt'">
                  <a-button class="table-btn" v-if="showFooter" type="link" @click="handleEditSubEnt(record, index)">
                    编辑
                  </a-button>
                  <a-popconfirm
                    v-if="record.pickUpNum === 0 || !record.id"
                    title="是否确认删除?"
                    ok-text="确认"
                    cancel-text="取消"
                    @confirm="
                      () => {
                        handleDeleteSubEnt(index, record);
                      }
                    "
                  >
                    <a-button class="table-btn" type="link">删除</a-button>
                  </a-popconfirm>
                </template>
              </template>
            </a-table>
          </div>
        </div>
        <div v-show="state.activeKey == 'bank'">
          <BasicForm name="finance" @register="bankRegisterForm" />
        </div>
        <div v-show="state.activeKey == 'audit'">
          <BasicForm name="audit" @register="auditRegisterForm">
            <template #table>
              <a-table :data-source="state.logList" :columns="auditColumns" :pagination="false" />
            </template>
          </BasicForm>
        </div>
      </a-col>
    </a-row>
    <template #footer v-if="showFooter">
      <template v-if="state.auditing">
        <a-button @click="handleCancel">暂不审核</a-button>
        <a-button v-auth="'dis:audit:confirm'" type="primary" @click="handleConfirmAudit">确认审核</a-button>
      </template>
      <template v-else>
        <a-button v-show="state.activeKey !== 'basic'" @click="handlePrev">上一步</a-button>
        <a-button v-show="state.activeKey == 'basic'" @click="handleCancel">取消</a-button>
        <a-button
          v-auth="'dis:acc:save'"
          v-if="state.activeKey == 'bank' && state.approvedStatus"
          type="primary"
          @click="handleSave"
          >确认</a-button
        >
        <a-button v-auth="'dis:acc:save'" v-else @click="handleSave">保存</a-button>
        <a-button
          v-auth="'dis:audit:submit'"
          v-show="state.activeKey == 'bank' && !state.approvedStatus"
          type="primary"
          @click="handleSubmitAudit"
          >提交审核</a-button
        >
        <a-button
          v-auth="'dis:acc:save'"
          v-show="!(state.activeKey == 'bank' || state.activeKey == 'audit')"
          type="primary"
          @click="handleNext"
          >下一步</a-button
        >
      </template>
    </template>
  </BasicDrawer>
  <UploadDrawer @register="registerUploadDrawer" @success="handleSuccess" />
  <EditSubEnterDrawer @register="registerEditSubEnterDrawer" @success="handleEditSuccess" />
</template>
<script setup lang="ts">
  import { reactive, ref, unref, computed } from 'vue';
  import { findIndex, isEqual } from 'lodash-es';
  import { BasicDrawer, useDrawerInner } from '@/components/Drawer';
  import { BasicForm, useForm } from '@/components/Form';
  import JImageUpload from '/@/components/Form/src/jeecg/components/JImageUpload.vue';
  import { auditColumns, subEnterColumns, auditFormSchema, basicFormSchema, bankFormSchema } from '../index.data';
  import { DownloadOutlined } from '@ant-design/icons-vue';
  import {
    confirmAudit,
    queryByIdApi,
    saveOrUpdate,
    submitAudit,
    handleDeleteSubEnter,
    handleEditSubEnter,
    uploadResult,
    getSubEnterList,
  } from '../index.api';
  import { isEmpty, isNullOrUnDef } from '@/utils/is';
  import { useDrawer } from '@/components/Drawer';
  import { useMessage } from '/@/hooks/web/useMessage';
  import UploadDrawer from './UploadDrawer.vue';
  import EditSubEnterDrawer from './EditSubEnterDrawer.vue';
  import { getUserAllList } from '/@/api/common/api';
  const emit = defineEmits(['success', 'register', 'cancel']);
  const { createMessage } = useMessage();
  const isUpdate = ref(true);
  const showFooter = ref(true);
  const subEnterList = ref<any[]>([]);
  const state = reactive({
    defaultTabs: [
      {
        title: '大客户信息',
        key: 'basic',
        disabled: false,
      },
      {
        title: '银行信息',
        key: 'bank',
        disabled: false,
      },
      {
        title: '审核意见',
        key: 'audit',
        disabled: false,
      },
    ],
    tabs: [] as any[],
    activeKey: 'bank',
    record: {} as any,
    reviewStatus: null,
    isAuditStatus: false, //是否已经审核
    approvedStatus: false, //是否审核通过
    auditing: false, //进行审核
    detailRes: {} as any,
    labelKey: '',
    currentChannelId: null,
    currentChannelInfo: null,
    logList: [],
    enterList: [] as any[],
    createId: '',
  });

  // 资企业表头字段
  const subEnterColumnsComputed = computed(() => {
    let columns = [...subEnterColumns];
    if (['detail', 'audit'].includes(state.labelKey) && columns[columns.length - 1].key === 'opt') {
      columns.pop();
    } else if (!['detail', 'audit'].includes(state.labelKey) && columns[columns.length - 1].key !== 'opt') {
      columns.push({
        title: '操作',
        dataIndex: 'opt',
        key: 'opt',
      });
    }
    return columns;
  });

  const [basicRegisterForm, basicFormMethods] = useForm({
    labelWidth: 120,
    schemas: basicFormSchema,
    showActionButtonGroup: false,
    baseColProps: {
      span: 12,
    },
  });

  const [bankRegisterForm, bankFormMethods] = useForm({
    labelWidth: 120,
    schemas: bankFormSchema,
    showActionButtonGroup: false,
    baseColProps: {
      span: 12,
    },
    wrapperCol: {
      sm: 24,
    },
  });

  const [auditRegisterForm, auditFormMethods] = useForm({
    labelWidth: 120,
    schemas: auditFormSchema,
    showActionButtonGroup: false,
  });

  // 导入子企业弹窗
  const [registerUploadDrawer, { openDrawer: openUploadDrawer }] = useDrawer();

  // 编辑子企业弹窗
  const [registerEditSubEnterDrawer, { openDrawer: openEditSubEnterDrawer }] = useDrawer();

  const [registerDrawer, { setDrawerProps, closeDrawer }] = useDrawerInner(async data => {
    setDrawerProps({ loading: true });
    const isAuditStatus = data.record?.reviewStatus === '2' || data.record?.reviewStatus === '3'; //审核后

    const approvedStatus = data.record?.reviewStatus === '2'; //审核通过后
    state.labelKey = data?.labelKey || 'detail';
    state.reviewStatus = data.record?.reviewStatus;
    isUpdate.value = !!data?.isUpdate;
    showFooter.value = !!data?.showFooter;
    const defaultActiveKey = !unref(isUpdate) && !unref(showFooter) && isAuditStatus ? 'account' : 'basic';
    state.activeKey = data?.activeKey || defaultActiveKey;
    state.record = data?.record || {};
    state.auditing = data?.activeKey === 'audit';
    let titleStr = '';
    if (state.auditing) {
      titleStr = '大客户审核页';
    } else if (unref(isUpdate)) {
      titleStr = '编辑大客户';
    } else if (unref(showFooter)) {
      titleStr = '新增大客户';
    } else {
      titleStr = '大客户详情';
    }
    await setDrawerProps({ confirmLoading: false, title: titleStr, showFooter: unref(showFooter) });
    state.approvedStatus = approvedStatus;
    state.isAuditStatus = isAuditStatus;
    //审核tab只有在审核之后的详情和点击审核进来显示
    let tabsArr: any = [];
    if (!unref(isUpdate) && !unref(showFooter) && isAuditStatus) {
      tabsArr = state.defaultTabs;
    } else if (state.auditing) {
      tabsArr = state.defaultTabs.filter(tab => tab.key !== 'account');
    } else {
      tabsArr = state.defaultTabs.filter(tab => tab.key !== 'audit' && tab.key !== 'account');
    }
    //不是审核页和不是详情页
    (tabsArr || []).map(item => (item.disabled = !state.auditing && unref(showFooter)));
    state.tabs = tabsArr;
    await handleChange();
    // await getDetailById();
    state.enterList = await getUserAllList({});
    setDrawerProps({ loading: false });
  });

  // 获取当前表单实例
  const getFormActive = (activeKey?: string) => {
    let formMethods = basicFormMethods;
    switch (activeKey ?? state.activeKey) {
      case 'bank':
        formMethods = bankFormMethods;
        break;
      case 'audit':
        formMethods = auditFormMethods;
        break;
      default:
        formMethods = basicFormMethods;
        break;
    }
    return formMethods;
  };

  //处理为空的数据
  const handleEmptyFields = (obj: any) => {
    if (!obj) return {};
    return Object.entries(obj).reduce((acc, [key, value]) => {
      if (!isNullOrUnDef(value)) {
        acc[key] = value;
      }
      return acc;
    }, {});
  };

  const handleDetailInfo = res => {
    let values = res;
    if (!res) {
      values = {
        id: state?.record?.id,
      };
    } else {
      //审核失败再审核，不能赋值ps:准入审核页不能赋值
      values = {
        ...res,
        secondPassword: res.loginPassword,
      };
    }
    let fieldsValue: any = handleEmptyFields(values);
    return fieldsValue;
  };

  // 获取详情数据
  const getDetailById = async () => {
    if (!state.record?.id) {
      return null;
    }
    const { setFieldsValue: setBasicFieldsValue } = getFormActive('basic');
    const { setFieldsValue: setBankFieldsValue } = getFormActive('bank');
    const { setFieldsValue: setAuditFieldsValue } = getFormActive('audit');
    // const { setFieldsValue } = getFormActive();
    try {
      const res: any = await queryByIdApi({ id: state.record.id });
      let fieldsValue: any = handleDetailInfo(res);
      state.detailRes = fieldsValue;
      if (state.activeKey === 'audit') {
        state.logList = state.detailRes.logList;
      }
      if (!isEmpty(fieldsValue)) {
        await setBasicFieldsValue({ ...fieldsValue });
        await setBankFieldsValue({ ...fieldsValue });
        await setAuditFieldsValue({ ...fieldsValue });
        // await setFieldsValue({ ...fieldsValue });
      }
    } catch (err) {
      console.log(err, 'err');
    }
  };
  const getSubEntList = async () => {
    if (!state.record?.id) {
      return;
    }
    try {
      const res: any = await getSubEnterList({ pageSize: 99999, pageNo: 1, id: state.record.id });
      console.log('res', res);
      subEnterList.value = res?.records;
    } catch (err) {
      console.log('err', err);
    }
  };

  const handleChange = async () => {
    const { resetFields, setProps, updateSchema, setFieldsValue } = getFormActive();
    await resetFields();
    let defaultValue: any = {
      labelKey: state.labelKey,
      isAuditEdit: state.isAuditStatus,
      approvedStatus: state.approvedStatus,
      reviewStatus: state.reviewStatus,
    };
    await setFieldsValue({
      ...defaultValue,
    });
    //禁用表单 -- 审核时，禁用其他表单填写功能
    // 2、审核时，禁用其他表单填写功能 state.auditing && state.activeKey !== 'audit'
    // 3、草稿编辑无需禁用
    // 4、审核过后编辑禁用企业名称和账号信息isAuditStatus
    setProps({
      disabled: !unref(showFooter) || (state.auditing && state.activeKey !== 'audit'),
    });
    if (state.activeKey == 'basic') {
      updateSchema([
        {
          field: 'phone',
          show: undefined,
        },
        {
          field: 'realName',
          show: undefined,
        },
        {
          field: 'loginPassword',
          show: !state.auditing && !state.isAuditStatus,
        },
        {
          field: 'secondPassword',
          show: !state.auditing && !state.isAuditStatus,
        },
      ]);
    }
    await getDetailById();
    await getSubEntList();
  };

  // 取消
  const handleCancel = () => {
    state.detailRes = {};
    subEnterList.value = [];
    closeDrawer();
    emit('cancel');
  };

  // 上一步
  const handlePrev = async () => {
    let index = findIndex(state.tabs, ['key', state.activeKey]);
    setDrawerProps({ loading: true, confirmLoading: true });
    index--;
    setTimeout(() => {
      if (index >= 0) {
        state.activeKey = state.tabs[index].key;
        // handleChange();
      }
      setDrawerProps({ loading: false, confirmLoading: false });
    }, 500);
  };

  // 下一步
  const handleNext = async () => {
    try {
      // 编辑情况下点击保存，需要先校验
      const { validate } = getFormActive('basic');
      const values = await validate();

      let index = findIndex(state.tabs, ['key', state.activeKey]);
      setDrawerProps({ loading: true, confirmLoading: true });
      index++;

      setTimeout(() => {
        if (index <= state.tabs.length - 1) {
          state.activeKey = state.tabs[index].key;
        }
      }, 500);
    } catch (err) {
      console.log(err, 'err');
    } finally {
      setTimeout(() => {
        setDrawerProps({ loading: false, confirmLoading: false });
      }, 500);
    }
  };

  // 保存
  const handleSave = async () => {
    try {
      let params = {} as any;
      // 编辑情况下点击保存，需要先校验
      const { getFieldsValue: getBasicFieldsValue } = getFormActive('basic');
      const { getFieldsValue: getBankFieldsValue } = getFormActive('bank');
      if (state.activeKey === 'basic') {
        const values = getBasicFieldsValue();
        const bankValues = getBankFieldsValue();
        const length = Object.keys(values).filter(
          key => !['auditStatus', 'isAuditEdit', 'approvedStatus', 'labelKey'].includes(key) && values[key]
        ).length;
        if (!length) {
          return createMessage.error('请填写表单信息');
        }
        params = {
          ...values,
          ...bankValues,
          subList: subEnterList.value.length ? subEnterList.value : void 0,
          salesmanName: state.enterList.find((item: any) => item.id === values.salesman)?.realname || void 0,
        };
      } else if (state.activeKey === 'bank') {
        const basicValues = getBasicFieldsValue();
        const bankValues = getBankFieldsValue();
        params = {
          ...basicValues,
          ...bankValues,
          subList: subEnterList.value.length ? subEnterList.value : void 0,
          salesmanName: state.enterList.find((item: any) => item.id === basicValues.salesman)?.realname || void 0,
        };
      }
      params.reviewStatus = Number(params.reviewStatus);
      delete params.labelKey;
      delete params.isAuditEdit;
      delete params.approvedStatus;
      delete params.list;

      if (handleFormValueIsChange(params)) {
        params.sunList = params.subList ? params.subList.filter(item => !item.id) : [];
        delete params.subList;
        setDrawerProps({ loading: true, confirmLoading: true });
        const id = await saveOrUpdate(params, isUpdate.value);
        if (state.reviewStatus === '2') {
          await submitAudit({ id: state.detailRes.id || id });
        }
      }
      createMessage.success('保存成功');
      closeDrawer();
      state.detailRes = {};
      subEnterList.value = [];
      emit('success');
    } catch (err) {
      console.log(err, 'err');
    } finally {
      setDrawerProps({ loading: false, confirmLoading: false });
    }
  };

  // 提交审核
  const handleSubmitAudit = async () => {
    const { validate: validateBasic } = getFormActive('basic');
    const { validate: validateBank } = getFormActive('bank');
    try {
      // 提交审核前需要校验
      const basicValues = await validateBasic();
      const bankValues = await validateBank();
      const params = {
        ...basicValues,
        ...bankValues,
        subList: subEnterList.value.length ? subEnterList.value : void 0,
        salesmanName: state.enterList.find((item: any) => item.id === basicValues.salesman)?.realname || void 0,
      };
      params.reviewStatus = Number(params.reviewStatus);
      delete params.labelKey;
      delete params.isAuditEdit;
      delete params.approvedStatus;
      delete params.list;
      let id = '';
      if (handleFormValueIsChange(params)) {
        params.sunList = params.subList ? params.subList.filter(item => !item.id) : [];
        delete params.subList;
        setDrawerProps({ loading: true, confirmLoading: true });
        id = await saveOrUpdate(params, isUpdate.value);
      }
      //审核后才编辑，审核前全是保存
      setDrawerProps({ loading: true, confirmLoading: true });

      await submitAudit({ id: state.detailRes.id || id }).then(() => {
        createMessage.success('提交审核成功');
      });
      closeDrawer();
      state.detailRes = {};
      subEnterList.value = [];
      emit('success');
    } catch (err) {
      console.log(err, 'err');
    } finally {
      setDrawerProps({ loading: false, confirmLoading: false });
    }
  };

  // 审核结果
  const handleConfirmAudit = async () => {
    state.activeKey = 'audit';
    const { validate } = getFormActive('audit');
    try {
      const values = await validate();
      setDrawerProps({ confirmLoading: true });
      await confirmAudit(values, () => {
        createMessage.success('审核成功');
        closeDrawer();
        state.detailRes = {};
        subEnterList.value = [];
        emit('success');
      });
    } catch (err) {
      console.log(err, 'err');
    } finally {
      setDrawerProps({ confirmLoading: false });
    }
  };

  // 判断内容是否改变
  const handleFormValueIsChange = value => {
    //过滤本地的isAuditEdit
    const { ...filterNull } = handleEmptyFields(value);
    //将值为数组改为json
    let keyArray = Object.keys(state.detailRes).filter(
      key =>
        ![
          'logList',
          'createBy',
          'createTime',
          'reviewStatusStr',
          'reviewTime',
          'reviewers',
          'submissionTime',
          'updateBy',
          'updateTime',
        ].includes(key)
    );
    const detailRes = keyArray.reduce((acc, key) => {
      acc[key] = state.detailRes[key];
      return acc;
    }, {});
    //添加一些表单中没有用到的返回值
    const addItemObj = {
      ...detailRes,
      ...filterNull,
    };
    return !isEqual(detailRes, addItemObj);
  };

  // 行驶证识别
  const handleLicenseChange = async (fileUrl: any) => {
    if (fileUrl) {
      try {
        setDrawerProps({ loading: true, confirmLoading: true });
        const result = await uploadResult({
          fileUrl,
        });
        if (result) {
          createMessage.success('上传成功');
          const { setFieldsValue } = getFormActive('basic');
          setFieldsValue({
            businessName: result.companyName,
            socialCreditCode: result.creditCode,
            address: result.businessAddress,
            industry: result.businessScope,
          });
        }
      } catch (err) {
        console.log('err', err);
      } finally {
        setDrawerProps({ loading: false, confirmLoading: false });
      }
    }
  };

  const handleBeforeUpload = (file: any) => {
    if (!file) return;
    //校验文件类型
    const validTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!validTypes.includes(file.type)) {
      createMessage.error('只能上传图片文件!');
      return false;
    }
    // 校验文件大小（30MB）
    const isLtSize = file.size / 1024 / 1024 < 30;
    if (!isLtSize) {
      createMessage.error('文件大小不能超过 30MB');
      return false;
    }
    return true;
  };

  // 导入资企业
  const importSubEnter = () => {
    openUploadDrawer(true, {});
  };

  // 导入子企业回调
  const handleSuccess = async (data: any) => {
    if (!data.length) {
      return;
    }
    const { getFieldsValue, setFieldsValue } = getFormActive('basic');
    const { businessName, socialCreditCode } = getFieldsValue();
    if (businessName && data?.some((item: any) => item.subBusinessName === businessName)) {
      const findIndex = data.findIndex((item: any) => item.subBusinessName === businessName);
      return createMessage.error(`导入的子企业中第${findIndex + 1}行名称与大客户名称重复`);
    }
    if (socialCreditCode && data?.some((item: any) => item.socialCreditCode === socialCreditCode)) {
      const findIndex = data.findIndex((item: any) => item.socialCreditCode === socialCreditCode);
      return createMessage.error(`导入的子企业中第${findIndex + 1}行统一社会信用代码与大客户重复`);
    }
    const nameList = subEnterList.value.map((item: any) => item.subBusinessName);
    const codeList = subEnterList.value.map((item: any) => item.socialCreditCode);
    if (data.some((item: any) => nameList.includes(item.subBusinessName))) {
      const findIndex = data.findIndex((item: any) => nameList.includes(item.subBusinessName));
      return createMessage.error(`导入的子企业中第${findIndex + 1}行名称与已导入的子企业名称重复`);
    }
    if (data.some((item: any) => codeList.includes(item.socialCreditCode))) {
      const findIndex = data.findIndex((item: any) => codeList.includes(item.socialCreditCode));
      return createMessage.error(`导入的子企业中第${findIndex + 1}行统一社会信用代码与已导入的子企业信用代码重复`);
    }
    if (!subEnterList.value.length) {
      subEnterList.value = data;
      setFieldsValue({ list: subEnterList.value });
    } else {
      subEnterList.value = [...subEnterList.value, ...data];
      setFieldsValue({ list: subEnterList.value });
    }
  };

  // 编辑子企业回调
  const handleEditSuccess = async (editData: any) => {
    const { data, index } = editData;
    if (data.id) {
      await handleEditSubEnter({ ...data }).then(() => {
        createMessage.success('编辑成功');
      });
      await getSubEntList();
    } else {
      subEnterList.value[index] = data;
    }
  };

  // 新增大客户->删除导入的某个子企业
  const handleDeleteSubEnt = async (index: number, { id }) => {
    if (id) {
      await handleDeleteSubEnter({ subId: id }).then(() => {
        createMessage.success('删除成功');
      });
      await getSubEntList();
    } else {
      subEnterList.value.splice(index, 1);
    }
  };

  // 新增大客户 -> 编辑资企业信息
  const handleEditSubEnt = (record: Recordable, index) => {
    openEditSubEnterDrawer(true, {
      record,
      index,
    });
  };
</script>
<style lang="less" scoped>
  .manage-list__edit-drawer {
    div :deep(.ant-picker) {
      width: 100%;
    }
    .emptyPlaceholder {
      width: 100%;
    }
    .map-slot {
      position: relative;
      #container {
        width: 90%;
        height: 400px;
        margin: 0 auto;
        overflow: hidden;
        :deep(img) {
          max-width: inherit;
        }
      }
      #result {
        position: absolute;
        bottom: 10px;
        right: 100px;
        padding: 0px 7px;
        min-width: 350px;
        height: 140px;
        line-height: 28px;
        background: #fff;
        box-shadow: 0 2px 6px 0 rgba(27, 142, 236, 0.5);
        border-radius: 7px;
        z-index: 400;
      }
    }
  }

  .red_start {
    color: #ff4d4f;
    display: inline-block;
    margin-right: 4px;
    font-size: 14px;
  }
  .sub-enterprise {
    width: 100%;
    .sub-enterprise__btn {
      margin-bottom: 12px;
    }
    .table-btn {
      padding-left: 0;
    }
  }
</style>
